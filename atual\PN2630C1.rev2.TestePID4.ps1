#####################################################################################################################################
# Pre requisito
#####################################################################################################################################
# Install-Module -Name ImportExcel -Scope CurrentUser -Force

#####################################################################################################################################
# Entrada
#####################################################################################################################################

# # Importar o modulo ImportExcel
# Import-Module Import-Excel

$variables = "Qo,Qw,Qg,Qgco2,Qghc,Qgl,On-time Fraction"
$export_path = "L:\res\campos\jubarte\er\er03\PN\2630\C1"

# $mode = ""
$mode = "pn"
$versao = "PN2630C1"
$cenario = "P50"

# $dates_filter = ""
$dates_filter = "2025-01-01:"

# Definir projetos
$projects = @{
    "RO300" = @{
        "MP" = @{
            base = "None"
            proj = "L:\res\campos\jubarte\er\er02\ZP_RO300\2025\00_PE_2529_V1\jub_ro300_PID3\p50\RO300_PE259_P50_MP.dat"
            campo = "JUB"
            zp = "RO300"
            nzp = "001"
            iupi = "NO.P-57"
            tps = ""
        }
        "IPB" = @{
            base = "L:\res\campos\jubarte\er\er02\ZP_RO300\2025\00_PE_2529_V1\jub_ro300_PID3\p50\RO300_PE259_P50_MP.dat"
            proj = "L:\res\campos\jubarte\er\er02\ZP_RO300\2025\00_PE_2529_V1\jub_ro300_PID3\p50\RO300_PE259_P50_MP_IPB.dat"
            campo = "JUB"
            zp = "RO300"
            nzp = "001"
            iupi = "DP-11-0310"
            tps = ""
        }
        "PID1" = @{
            base = "L:\res\campos\jubarte\er\er02\ZP_RO300\2025\00_PE_2529_V1\jub_ro300_PID3\p50\RO300_PE259_P50_MP_IPB.dat"
            proj = "L:\res\campos\jubarte\er\er02\ZP_RO300\2025\00_PE_2529_V1\jub_ro300_PID3\p50\RO300_PE259_P50_PID1.dat"
            campo = "JUB"
            zp = "RO300"
            nzp = "001"
            iupi = "DPCOMSO-20-0002"
            tps = ""
        }
        "PID2" = @{
            base = "L:\res\campos\jubarte\er\er02\ZP_RO300\2025\00_PE_2529_V1\jub_ro300_PID3\p50\RO300_PE259_P50_PID1.dat"
            proj = "L:\res\campos\jubarte\er\er02\ZP_RO300\2025\00_PE_2529_V1\jub_ro300_PID3\p50\RO300_PE259_P50_PID2.dat"
            campo = "JUB"
            zp = "RO300"
            nzp = "001"
            iupi = "DPCOM-20-0001"
            tps = ""
        }
        "DC" = @{
            base = "L:\res\campos\jubarte\er\er02\ZP_RO300\2025\00_PE_2529_V1\jub_ro300_PID3\p50\RO300_PE259_P50_PID2.dat"
            proj = "L:\res\campos\jubarte\er\er02\ZP_RO300\2025\00_PE_2529_V1\jub_ro300_PID3\p50\RO300_PE259_P50_DC.dat"
            campo = "JUB"
            zp = "RO300"
            nzp = "001"
            iupi = "DPCOMSO-21-0002"
            tps = ""
        }
        "EVPRO_P-57" = @{
            base = "L:\res\campos\jubarte\er\er02\ZP_RO300\2025\00_PE_2529_V1\jub_ro300_PID3\p50\RO300_PE259_P50_DC.dat"
            proj = "L:\res\campos\jubarte\er\er02\ZP_RO300\2025\00_PE_2529_V1\jub_ro300_PID3\p50\RO300_PE259_P50_EVPRO.dat"
            campo = "JUB"
            zp = "RO300"
            nzp = "001"
            iupi = "EVPRO-23-0179"
            tps = ""
        }
        "PID3" = @{
            base = "L:\res\campos\jubarte\er\er02\ZP_RO300\2025\00_PE_2529_V1\jub_ro300_PID3\p50\RO300_PE259_P50_EVPRO.dat"
            proj = "L:\res\campos\jubarte\er\er02\ZP_RO300\2025\00_PE_2529_V1\jub_ro300_PID3\p50\RO300_PE259_P50_PID3.dat"
            campo = "JUB"
            zp = "RO300"
            nzp = "001"
            iupi = "DPCOMSO-21-0003"
            tps = ""
        }
        "PID5" = @{
            base = "L:\res\campos\jubarte\er\er02\ZP_RO300\2025\00_PE_2529_V1\jub_ro300_PID3\p50\RO300_PE259_P50_PID3.dat"
            proj = "L:\res\campos\jubarte\er\er02\ZP_RO300\2025\00_PE_2529_V1\jub_ro300_PID3\p50\RO300_PE259_P50_PID5.dat"
            campo = "JUB"
            zp = "RO300"
            nzp = "001"
            iupi = "DPCOM-23-0144"
            tps = ""
        }
    }
    "CO140" = @{
        "MP" = @{
            base = "None"
            proj = "L:\res\campos\jubarte\er\er02\ZP_CO140\2025\00_PE2630_c1\P50\P50_CO140_MP_SEM_IPB_2025_c1.dat"
            campo = "JUB,BLA=Well:(BLA-0001HA)"
            zp = "CO140-ESS116,CO140-ESS122=Well:(BLA-0001HA)"
            nzp = "009,001=Well:(BLA-0001HA)"
            iupi = "NO.P-58"
            tps = ""
        }
        "IPB" = @{
            base = "L:\res\campos\jubarte\er\er02\ZP_CO140\2025\00_PE2630_c1\P50\P50_CO140_MP_SEM_IPB_2025_c1.dat"
            proj = "L:\res\campos\jubarte\er\er02\ZP_CO140\2025\00_PE2630_c1\P50\P50_CO140_IPB_2025_c1.dat"
            campo = "JUB,BLA=Well:(BLA-0001HA)"
            zp = "CO140-ESS116,CO140-ESS122=Well:(BLA-0001HA)"
            nzp = "009,001=Well:(BLA-0001HA)"
            iupi = "DP-11-0310"
            tps = ""
        }
        "PID1" = @{
            base = "L:\res\campos\jubarte\er\er02\ZP_CO140\2025\00_PE2630_c1\P50\P50_CO140_PID1_BASE_2025_c1v1.dat"
            proj = "L:\res\campos\jubarte\er\er02\ZP_CO140\2025\00_PE2630_c1\P50\P50_CO140_PID1_2025_c1v1.dat"
            campo = "JUB,BLA=Well:(BLA-0001HA)"
            zp = "CO140-ESS116,CO140-ESS122=Well:(BLA-0001HA)"
            nzp = "009,001=Well:(BLA-0001HA)"
            iupi = "DPCOMSO-20-0002"
            tps = ""
        }
        "PID2" = @{
            base = "L:\res\campos\jubarte\er\er02\ZP_CO140\2025\00_PE2630_c1\P50\P50_CO140_PID2_BASE_2025_c1.dat"
            proj = "L:\res\campos\jubarte\er\er02\ZP_CO140\2025\00_PE2630_c1\P50\P50_CO140_PID2_2025_c1.dat"
            campo = "JUB,BLA=Well:(BLA-0001HA)"
            zp = "CO140-ESS116,CO140-ESS122=Well:(BLA-0001HA)"
            nzp = "009,001=Well:(BLA-0001HA)"
            iupi = "DPCOM-20-0001"
            tps = ""
        }
        "DC" = @{
            base = "L:\res\campos\jubarte\er\er02\ZP_CO140\2025\00_PE2630_c1\P50\P50_CO140_DC_BASE_2025_c1v1.dat"
            proj = "L:\res\campos\jubarte\er\er02\ZP_CO140\2025\00_PE2630_c1\P50\P50_CO140_DC_2025_c1v1.dat"
            campo = "JUB,BLA=Well:(BLA-0001HA)"
            zp = "CO140-ESS116,CO140-ESS122=Well:(BLA-0001HA)"
            nzp = "009,001=Well:(BLA-0001HA)"
            iupi = "DPCOMSO-21-0002"
            tps = ""
        }
        "EVPRO_P-58" = @{
            base = "L:\res\campos\jubarte\er\er02\ZP_CO140\2025\00_PE2630_c1\P50\P50_CO140_EVU_BASE_2025_c1.dat"
            proj = "L:\res\campos\jubarte\er\er03\PN\2630\C1\2505071558\CO140\EVPRO_P-58_proj_P50_CO140_EVU_2025_c1.dat"
            campo = "JUB,BLA=Well:(BLA-0001HA)"
            zp = "CO140-ESS116,CO140-ESS122=Well:(BLA-0001HA)"
            nzp = "009,001=Well:(BLA-0001HA)"
            iupi = "EVPRO-23-0180"
            tps = ""
        }
    }
    "MRL700" = @{
        "DC" = @{
            base = "None"
            proj = "L:\res\campos\jubarte\er\er02\ZP_MRL700\2025\00_PE2630_c1\P50\P50_MRL700_00489_2040_2025_c1_v1.dat"
            campo = "JUB"
            zp = "MRL700"
            nzp = "XXX"
            iupi = "DPCOMSO-21-0002"
            tps = "ZP:(JUB,MRL700-153,029)=0.504*MRL700;ZP:(JUB,MRL700-136,011)=0.496*MRL700"
        }
        "EVPRO_P-58" = @{
            base = "L:\res\campos\jubarte\er\er02\ZP_MRL700\2025\00_PE2630_c1\P50\P50_MRL700_00489_2040_2025_c1_v1.dat"
            proj = "L:\res\campos\jubarte\er\er02\ZP_MRL700\2025\00_PE2630_c1\P50\P50_MRL700_00489_2056_2025_c1_v1.dat"
            campo = "JUB"
            zp = "MRL700"
            nzp = "XXX"
            iupi = "EVPRO-23-0180"
            tps = "ZP:(JUB,MRL700-153,029)=0.504*MRL700;ZP:(JUB,MRL700-136,011)=0.496*MRL700"
        }
    }
    "BR100" = @{
        "PID1" = @{
            base = "None"
            proj = "\\dfs.petrobras.biz\cientifico\res\campos\jubarte\er\er02\ZP_BR100\2025\00_PE2630\ajuste_6_00487_nome_JUB65_BASE.dat"
            campo = "JUB"
            zp = "BR100"
            nzp = "015"
            iupi = "DPCOMSO-20-0002"
            tps = ""
        }
    }
    "CXR" = @{
        "ICSPB" = @{
            base = "None"
            proj = "\\dfs.petrobras.biz\cientifico\res\campos\jubarte\er\er02\ZP_CXR\2025\00_PN2630\PN2630\Ajuste_pressao_perm3_v2_00479_ntg60_teste_por002_perm100_NTG200_DJ4_NTG60_4P_1I_mod_otimizado_6000_PN2630.dat"
            campo = "CXR"
            zp = "MCB/COQ-ESS172"
            nzp = "001"
            iupi = "DPCOM-22-0177"
            tps = ""
        }
    }
    "PreSal" = @{
        "MP" = @{
            base = "None"
            proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\00_PE2630_Ciclo1\02.P50\RMSv14_er03_geoeng_output03_087_HIST_MP.dat"
            campo = "JUB100,PRB100=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            zp = "MCB/COQ-ESS103A,MCB/COQ-PRB2=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            nzp = "005,006=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            iupi = "NO.P-58,NO.CDAN=UEP:(CDAN)"
            tps = "Campo:(JUB,MCB/COQ-ESS103A,005)=0.9725*JUB100;Campo:(ANC9,MCB/COQ-ESS103A,001)=0.0154*JUB100;Campo:(ANC8,MCB/COQ-ESS103A,001)=0.0035*JUB100;Campo:(ARGO,MCB/COQ-ESS103A,004)=0.0086*JUB100;Campo:(PRB,MCB/COQ-PRB2,006)=0.6218*PRB100;Campo:(MGG,MCB/COQ-PRB2,008)=0.3768*PRB100;Campo:(JUB,MCB/COQ-PRB2,031)=0.0014*PRB100"
        }
        "IPB" = @{
            base = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\00_PE2630_Ciclo1\02.P50\RMSv14_er03_geoeng_output03_087_HIST_MP.dat"
            proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\00_PE2630_Ciclo1\02.P50\RMSv14_er03_geoeng_output03_087_HIST_MP_IPB.dat"
            campo = "JUB100,PRB100=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            zp = "MCB/COQ-ESS103A,MCB/COQ-PRB2=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            nzp = "005,006=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            iupi = "DP-11-0310"
            tps = "Campo:(JUB,MCB/COQ-ESS103A,005)=0.9725*JUB100;Campo:(ANC9,MCB/COQ-ESS103A,001)=0.0154*JUB100;Campo:(ANC8,MCB/COQ-ESS103A,001)=0.0035*JUB100;Campo:(ARGO,MCB/COQ-ESS103A,004)=0.0086*JUB100;Campo:(PRB,MCB/COQ-PRB2,006)=0.6218*PRB100;Campo:(MGG,MCB/COQ-PRB2,008)=0.3768*PRB100;Campo:(JUB,MCB/COQ-PRB2,031)=0.0014*PRB100"
        }
        "PID2" = @{
            base = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\00_PE2630_Ciclo1\02.P50\RMSv14_er03_geoeng_output03_087_HIST_MP_IPB.dat"
            proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\00_PE2630_Ciclo1\02.P50\RMSv14_er03_geoeng_output03_087_HIST_MP_IPB_PID2.dat"
            campo = "JUB100,PRB100=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            zp = "MCB/COQ-ESS103A,MCB/COQ-PRB2=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            nzp = "005,006=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            iupi = "DPCOM-20-0001"
            tps = "Campo:(JUB,MCB/COQ-ESS103A,005)=0.9725*JUB100;Campo:(ANC9,MCB/COQ-ESS103A,001)=0.0154*JUB100;Campo:(ANC8,MCB/COQ-ESS103A,001)=0.0035*JUB100;Campo:(ARGO,MCB/COQ-ESS103A,004)=0.0086*JUB100;Campo:(PRB,MCB/COQ-PRB2,006)=0.6218*PRB100;Campo:(MGG,MCB/COQ-PRB2,008)=0.3768*PRB100;Campo:(JUB,MCB/COQ-PRB2,031)=0.0014*PRB100"
        }
        "PID1" = @{
            base = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\00_PE2630_Ciclo1\02.P50\RMSv14_er03_geoeng_output03_087_HIST_MP_IPB_PID2.dat"
            proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\00_PE2630_Ciclo1\02.P50\RMSv14_er03_geoeng_output03_087_HIST_MP_IPB_PID2_PID1.dat"
            campo = "JUB100,PRB100=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            zp = "MCB/COQ-ESS103A,MCB/COQ-PRB2=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            nzp = "005,006=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            iupi = "DPCOMSO-20-0002"
            tps = "Campo:(JUB,MCB/COQ-ESS103A,005)=0.9725*JUB100;Campo:(ANC9,MCB/COQ-ESS103A,001)=0.0154*JUB100;Campo:(ANC8,MCB/COQ-ESS103A,001)=0.0035*JUB100;Campo:(ARGO,MCB/COQ-ESS103A,004)=0.0086*JUB100;Campo:(PRB,MCB/COQ-PRB2,006)=0.6218*PRB100;Campo:(MGG,MCB/COQ-PRB2,008)=0.3768*PRB100;Campo:(JUB,MCB/COQ-PRB2,031)=0.0014*PRB100"
        }
        "EVPRO_P-58" = @{
            base = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\00_PE2630_Ciclo1\02.P50\RMSv14_er03_geoeng_output03_087_HIST_MP_IPB_PID2_PID1.dat"
            proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\00_PE2630_Ciclo1\02.P50\RMSv14_er03_geoeng_output03_087_HIST_MP_IPB_PID2_PID1_EVPRO.dat"
            campo = "JUB100,PRB100=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            zp = "MCB/COQ-ESS103A,MCB/COQ-PRB2=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            nzp = "005,006=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            iupi = "EVPRO-23-0180"
            tps = "Campo:(JUB,MCB/COQ-ESS103A,005)=0.9725*JUB100;Campo:(ANC9,MCB/COQ-ESS103A,001)=0.0154*JUB100;Campo:(ANC8,MCB/COQ-ESS103A,001)=0.0035*JUB100;Campo:(ARGO,MCB/COQ-ESS103A,004)=0.0086*JUB100;Campo:(PRB,MCB/COQ-PRB2,006)=0.6218*PRB100;Campo:(MGG,MCB/COQ-PRB2,008)=0.3768*PRB100;Campo:(JUB,MCB/COQ-PRB2,031)=0.0014*PRB100"
        }
        "PID4" = @{
            base = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\05_GRREE_PID4\03.P50\RMSv14_er03_geoeng_output03_158_P50_BASE.dat"
            proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\05_GRREE_PID4\03.P50\RMSv14_er03_geoeng_output03_158_P50_BASE_PID4.dat"
            campo = "JUB100,PRB100=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            zp = "MCB/COQ-ESS103A,MCB/COQ-PRB2=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            nzp = "005,006=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            iupi = "DPCOMSO-22-0118"
            tps = "Campo:(JUB,MCB/COQ-ESS103A,005)=0.9725*JUB100;Campo:(ANC9,MCB/COQ-ESS103A,001)=0.0154*JUB100;Campo:(ANC8,MCB/COQ-ESS103A,001)=0.0035*JUB100;Campo:(ARGO,MCB/COQ-ESS103A,004)=0.0086*JUB100;Campo:(PRB,MCB/COQ-PRB2,006)=0.6218*PRB100;Campo:(MGG,MCB/COQ-PRB2,008)=0.3768*PRB100;Campo:(JUB,MCB/COQ-PRB2,031)=0.0014*PRB100"
        }
        "ICSPB" = @{
            base = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\00_PE2630_Ciclo1\02.P50\RMSv14_er03_geoeng_output03_087_HIST_MP_IPB_PID2_PID1_EVPRO_PID4.dat"
            proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\00_PE2630_Ciclo1\02.P50\RMSv14_er03_geoeng_output03_087_HIST_MP_IPB_PID2_PID1_EVPRO_PID4_ICSPBJUBPRB.dat"
            campo = "JUB100,PRB100=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            zp = "MCB/COQ-ESS103A,MCB/COQ-PRB2=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            nzp = "005,006=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
            iupi = "DPCOM-22-0177"
            tps = "Campo:(JUB,MCB/COQ-ESS103A,005)=0.9725*JUB100;Campo:(ANC9,MCB/COQ-ESS103A,001)=0.0154*JUB100;Campo:(ANC8,MCB/COQ-ESS103A,001)=0.0035*JUB100;Campo:(ARGO,MCB/COQ-ESS103A,004)=0.0086*JUB100;Campo:(PRB,MCB/COQ-PRB2,006)=0.6218*PRB100;Campo:(MGG,MCB/COQ-PRB2,008)=0.3768*PRB100;Campo:(JUB,MCB/COQ-PRB2,031)=0.0014*PRB100"
        }
        # "CCUS" = @{
        #     base = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\00_PE2630_Ciclo1\02.P50\RMSv14_er03_geoeng_output03_087_HIST_MP_IPB_PID2_PID1_EVPRO_PID4_ICSPBJUBPRB.dat"
        #     proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\00_PE2630_Ciclo1\02.P50\RMSv14_er03_geoeng_output03_087_HIST_MP_IPB_PID2_PID1_EVPRO_PID4_ICSPBJUBPRB_CCUSJUBPRB.dat"
        #     campo = "JUB100,PRB100=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
        #     zp = "MCB/COQ-ESS103A,MCB/COQ-PRB2=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
        #     nzp = "005,006=Well:(3-PRB-2-ESS,PRBPS-IG1,PRBPS-P3)"
        #     iupi = "CCUS-24-0230"
        #     tps = "Campo:(JUB,MCB/COQ-ESS103A,005)=0.9725*JUB100;Campo:(ANC9,MCB/COQ-ESS103A,001)=0.0154*JUB100;Campo:(ANC8,MCB/COQ-ESS103A,001)=0.0035*JUB100;Campo:(ARGO,MCB/COQ-ESS103A,004)=0.0086*JUB100;Campo:(PRB,MCB/COQ-PRB2,006)=0.6218*PRB100;Campo:(MGG,MCB/COQ-PRB2,008)=0.3768*PRB100;Campo:(JUB,MCB/COQ-PRB2,031)=0.0014*PRB100"
        # }
    }
}

#####################################################################################################################################
# Verificacao de caminhos
#####################################################################################################################################

Write-Host "Verificando existencia dos caminhos em cada projeto..." -ForegroundColor Cyan

foreach ($groupName in $projects.Keys) {
    Write-Host "`n=== Grupo: $groupName ===" -ForegroundColor Magenta

    foreach ($projectName in $projects[$groupName].Keys) {
        Write-Host "`n--> Projeto: $projectName" -ForegroundColor Green

        $projPath = $projects[$groupName][$projectName].proj
        $basePath = $projects[$groupName][$projectName].base

        # Verificando arquivos do projeto
        if (Test-Path $projPath) {
            Write-Host "    OK - proj: $projPath existe." -ForegroundColor DarkGreen

            # Verificar .sr3
            $sr3Path = [System.IO.Path]::ChangeExtension($projPath, ".sr3")
            if (Test-Path $sr3Path) {
                Write-Host "    OK - sr3: $sr3Path existe." -ForegroundColor DarkGreen
            } else {
                Write-Host "    AVISO - sr3: $sr3Path nao encontrado!" -ForegroundColor Yellow
            }

            # Verificar .out
            $outPath = [System.IO.Path]::ChangeExtension($projPath, ".out")
            if (Test-Path $outPath) {
                Write-Host "    OK - out: $outPath existe." -ForegroundColor DarkGreen
            } else {
                Write-Host "    AVISO - out: $outPath nao encontrado!" -ForegroundColor Yellow
            }
        }
        else {
            Write-Host "    ERRO - proj: $projPath NAO existe!" -ForegroundColor Red
        }

        # Verificando arquivos base
        if ($basePath -and $basePath -ne "None") {
            if (Test-Path $basePath) {
                Write-Host "    OK - base: $basePath existe." -ForegroundColor DarkGreen

                # Verificar .sr3
                $sr3Path = [System.IO.Path]::ChangeExtension($basePath, ".sr3")
                if (Test-Path $sr3Path) {
                    Write-Host "    OK - sr3: $sr3Path existe." -ForegroundColor DarkGreen
                } else {
                    Write-Host "    AVISO - sr3: $sr3Path nao encontrado!" -ForegroundColor Yellow
                }

                # Verificar .out
                $outPath = [System.IO.Path]::ChangeExtension($basePath, ".out")
                if (Test-Path $outPath) {
                    Write-Host "    OK - out: $outPath existe." -ForegroundColor DarkGreen
                } else {
                    Write-Host "    AVISO - out: $outPath nao encontrado!" -ForegroundColor Yellow
                }
            }
            else {
                Write-Host "    ERRO - base: $basePath NAO existe!" -ForegroundColor Red
            }
        }
        else {
            Write-Host "    Observacao - base esta vazio ou 'None'. Nada a verificar." -ForegroundColor Yellow
        }
    }
}

Write-Host "`nVerificacao concluida.`n" -ForegroundColor Cyan

#####################################################################################################################################
# Criação da pasta AAMMDDHHMM e copia dos arquivos
#####################################################################################################################################

# Gerar timestamp AAMMDDHHMM (Ano com dois digitos, mês, dia, hora, minuto)
$timestamp = Get-Date -Format "yyMMddHHmm"
$aammddhhmm_dir = Join-Path $export_path $timestamp
New-Item -ItemType Directory -Path $aammddhhmm_dir -Force | Out-Null

Write-Host "`nIniciando copia dos arquivos.`n" -ForegroundColor Cyan

foreach ($groupName in $projects.Keys) {
    Write-Host "`n=== Grupo: $groupName ===" -ForegroundColor Magenta

    foreach ($projectName in $projects[$groupName].Keys) {
        Write-Host "`n--> Projeto: $projectName" -ForegroundColor Green
        $project = $projects[$groupName][$projectName]

        # Criar subdiretorio unico com ID + nome do projeto
        $groupNameDir = Join-Path $aammddhhmm_dir "$groupName"
        New-Item -ItemType Directory -Path $groupNameDir -Force | Out-Null

        # Função modificada para renomear arquivos
        function Process-File($path, $type, $pName) {
            if (-not $path -or $path -eq "None") { return $null }

            try {
                $file = Get-Item $path -ErrorAction Stop
                $base_name = $file.BaseName
                $new_base = "${pName}_${type}_$base_name"

                # Copiar .dat com novo nome e inserir o cabeçalho
                $dest = Join-Path $groupNameDir "$new_base.dat"

                # Ler o conteúdo do arquivo .dat original
                $content = Get-Content $file.FullName -Raw

                # Criar o cabeçalho a ser inserido
                $header =   "** <project>-A atp-jub-cht</project>`r`n" +
                            "** <ncpus>20</ncpus>`r`n" +
                            "** <queue>batch</queue>`r`n" +
                            "** <simulator>IMEX</simulator>`r`n" +
                            "** <simulator_version>2022.10</simulator_version>`r`n" +
                            "** <walltime>20:00:00</walltime>`r`n" +
                            "** <squeeze>False</squeeze>`r`n" +
                            "** <extra_command>True</extra_command>`r`n`r`n"

                # Combinar o cabeçalho com o conteúdo existente e salvar
                $header + $content | Out-File -FilePath $dest -Encoding ASCII

                # Copiar extensões relacionadas
                @(".sr3", ".out") | ForEach-Object {
                    $src = Join-Path $file.DirectoryName ($base_name + $_)
                    if (Test-Path $src) {
                        $dest_file = Join-Path $groupNameDir ($new_base + $_)
                        Copy-Item $src -Destination $dest_file -Force
                    }
                }

                return $dest
            }
            catch {
                Write-Host " ERRO - Falha ao processar $type : $path" -ForegroundColor Red
                Write-Host " Detalhes: $_" -ForegroundColor Red
                exit
            }
        }

        # Processar arquivos com novo padrão de nomes
        $new_proj = Process-File $project.proj "proj" $projectName
        $new_base = Process-File $project.base "base" $projectName

        # Atualizar caminhos
        $project.proj = $new_proj
        $project.base = if ($new_base) { $new_base } else { "None" }
    }
}

Write-Host "`nCopia dos arquivos concluida.`n" -ForegroundColor Cyan

#####################################################################################################################################
# Executar o SimpleLauncherExtrator
#####################################################################################################################################

$executavel = "C:\Users\<USER>\AppData\Local\OPR-JUB\Optimizer\util\SimpleLauncherExtrator\SimpleLauncher.exe"

Write-Host "Executando o SimpleLauncherExtrator..." -ForegroundColor Cyan

# Iniciar o processo em nova janela
$process = Start-Process -FilePath $executavel -ArgumentList "`"$aammddhhmm_dir`"" -PassThru
# $logFile = Join-Path $aammddhhmm_dir "SimpleLauncher_log.txt"
# $process = Start-Process -FilePath $executavel -ArgumentList "`"$aammddhhmm_dir`"" -RedirectStandardOutput $logFile -RedirectStandardError "$logFile.error" -NoNewWindow -PassThru
# Start-Process -FilePath $executavel -ArgumentList "`"$aammddhhmm_dir`"" -RedirectStandardOutput $logFile -RedirectStandardError "$logFile.error" -Wait

# Esperar e enviar ENTER usando automação de UI
Add-Type -AssemblyName System.Windows.Forms
$timeout = 10 # segundos
$counter = 0

do {
    Start-Sleep -Milliseconds 500
    $counter++
    try {
        [System.Windows.Forms.SendKeys]::SendWait("~") # ~ = ENTER
        break
    }
    catch {
        if ($counter -ge ($timeout * 2)) {
            Write-Host "Falha ao enviar ENTER ao executavel" -ForegroundColor Red
            exit
        }
    }
} while ($true)

$process.WaitForExit()

if ($process.ExitCode -ne 0) {
    Write-Host "Erro na execucao do executavel. Codigo: $($process.ExitCode)" -ForegroundColor Red
    exit
}

#####################################################################################################################################
# Verificar arquivos .job.end e .data
#####################################################################################################################################

$all_dat_files = Get-ChildItem -Path $aammddhhmm_dir -Recurse -Filter *.dat
$missing_data = @()

Write-Host "Verificando arquivos .job.end..." -ForegroundColor Cyan
foreach ($dat in $all_dat_files) {
    $job_end = $dat.FullName -replace '\.dat$','.job.end'
    $timeout = 600 # 10 minutos
    $start = Get-Date
    while (-not (Test-Path $job_end)) {
        if (((Get-Date) - $start).TotalSeconds -gt $timeout) {
            Write-Host "Timeout aguardando arquivo: $job_end" -ForegroundColor Red
            exit
        }
        Start-Sleep -Seconds 10
    }
}

Write-Host "Verificando arquivos .data..." -ForegroundColor Cyan
foreach ($dat in $all_dat_files) {
    $data_file = $dat.FullName -replace '\.dat$','.data'
    if (-not (Test-Path $data_file)) {
        $missing_data += $data_file
    }
}

if ($missing_data.Count -gt 0) {
    Write-Host "Arquivos .data faltantes:" -ForegroundColor Red
    $missing_data | ForEach-Object { Write-Host "    $_" }
    exit
}

#####################################################################################################################################
# Execucao
#####################################################################################################################################

# Definir diretorio do script dinamicamente
$script_dir = $PSScriptRoot # Diretorio do repositorio

# Definir caminhos e variaveis
$python = "L:\res\campos\jubarte\er\er02\00_Programas\python\windows\python-3.12.6.amd64\python.exe"
$script = "$script_dir\ensemble.py"
$command = "delta_export"

$apply_dates_filter = @()
if ($dates_filter -ne "") {
    $apply_dates_filter += "--dates"
    $apply_dates_filter += $dates_filter
}

# Lista para armazenar todos os .xlsx gerados
$excel_files = @()

foreach ($groupName in $projects.Keys) {
    foreach ($projectName in $projects[$groupName].Keys) {
        $project = $projects[$groupName][$projectName]
        $proj_path = $project.proj
        $base_path = $project.base

        Write-Output "Processando projeto: $groupName $projectName"

        # Criar um nome de arquivo unico para cada projeto
        $temp_excel = Join-Path $aammddhhmm_dir "$($groupName)_$($projectName).xlsx"

        # Comando a ser executado
        if ($mode -eq "pn") {
            $campo = $project.campo
            $zp = $project.zp
            $nzp = $project.nzp
            $iupi = $project.iupi

            $apply_tps = @()
            if ($project.tps -ne "") {
                $apply_tps += "--tps"
                $apply_tps += "`"$($project.tps)`""
            }

            # $cmd = "$python $script $command `"$proj_path`" `"$base_path`" $apply_dates_filter --pn --campo `"$campo`" --zp `"$zp`" --nzp `"$nzp`" --projeto `"$projectName`" --iupi `"$iupi`" --versao $versao --cenario $cenario $apply_tps --export_path `"$temp_excel`""
            $cmd = "$python `"$script`" $command `"$proj_path`" `"$base_path`" $apply_dates_filter --pn --campo `"$campo`" --zp `"$zp`" --nzp `"$nzp`" --projeto `"$projectName`" --iupi `"$iupi`" --versao `"$versao`" --cenario `"$cenario`" $apply_tps --export_path `"$temp_excel`" 2> `"$temp_excel.error.log`""
        }
        else {
            $cmd = "$python $script $command $proj_path $base_path $apply_dates_filter --variables `"$variables`" --export_path `"$temp_excel`""
        }

        Write-Output "Executando: $cmd"

        # Executando o comando
        # Invoke-Expression $cmd
        try {
            $output = Invoke-Expression $cmd | Out-String
            Write-Host "Saida do comando:`n$output" -ForegroundColor DarkGreen
            if (-not (Test-Path $temp_excel)) {
                throw "Arquivo Excel nao foi gerado!"
            }
            $excel_files += $temp_excel
        } catch {
            Write-Host "ERRO na execucao: $_" -ForegroundColor Red
            Get-Content "$temp_excel.error.log" | ForEach-Object { Write-Host "LOG ERRO: $_" -ForegroundColor Red }
            exit
        }

        Write-Output "$groupName $projectName processado."
        Write-Output ""
    }
}


#####################################################################################################################################
# Mesclar todos os arquivos gerados
#####################################################################################################################################

Write-Output "Realizando mescla de todos os arquivos gerados..."

# Arquivo final de mescla
$merged_xlsx = Join-Path $aammddhhmm_dir "$versao.xlsx"

# Monta o array de arquivos em uma unica string (cada caminho separado por espaco)
# Ex: "arquivo1.xlsx arquivo2.xlsx arquivo3.xlsx ..."
$files_str = $excel_files -join " "

# Monta o comando para merge_delta_exports
$merge_cmd = "$python `"$script`" merge_delta_exports $files_str --output `"$merged_xlsx`""

Write-Output "Executando: $merge_cmd"
Invoke-Expression $merge_cmd

Write-Output "Mescla final concluida. Arquivo gerado: $merged_xlsx"
Write-Output ""

#####################################################################################################################################
# Salvando o script atual no diretorio de saida
#####################################################################################################################################

if ($export_path -ne "") {
    $out_directory = $aammddhhmm_dir
    $path_without_extension = Join-Path -Path $out_directory "$versao"

    # Caminho do novo script a ser salvo
    $script_output_path = "$path_without_extension.ps1"

    # Obter o conteudo do script atual
    $script_content = Get-Content -Path $MyInvocation.MyCommand.Path

    # Substituir a variavel $script_dir pelo diretorio original
    $script_content = $script_content -replace ' = \$PSScriptRoot # Diretorio do repositorio', " = `"$script_dir`" # Diretorio do repositorio"

    # Salvar o conteudo no diretorio de saida
    $script_content | Out-File -FilePath $script_output_path -Encoding UTF8

    # Exibir mensagem de confirmacao
    Write-Output "O script foi salvo em: $script_output_path"

    Write-Output "Segue o comando para executar novamente:"
    Write-Output "powershell.exe -ExecutionPolicy Bypass -File $script_output_path"
}

#####################################################################################################################################
# Finalizacao
#####################################################################################################################################

Write-Host "`nPressione qualquer tecla para finalizar..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")