import os
import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox
import xlwings as xw

def ler_arquivos_excel(pasta):
    """Lê todos os arquivos Excel de uma pasta e retorna um DataFrame unificado."""
    dfs = []
    colunas_padrao = None
    
    for arquivo in os.listdir(pasta):
        if arquivo.endswith((".xlsx", ".xls")):
            caminho = os.path.join(pasta, arquivo)
            try:
                xls = pd.ExcelFile(caminho)
                for nome_sheet in xls.sheet_names:
                    df = xls.parse(nome_sheet)
                    # Padroniza as colunas com base no primeiro arquivo encontrado
                    if colunas_padrao is None:
                        colunas_padrao = list(df.columns)
                    else:
                        # Mantém apenas colunas que existem no padrão
                        df = df[[col for col in colunas_padrao if col in df.columns]]
                    dfs.append(df)
            except Exception as e:
                print(f"Erro ao ler arquivo {arquivo}: {e}")
    
    if not dfs:
        raise ValueError("Nenhum arquivo Excel válido encontrado na pasta selecionada.")
    
    resultado = pd.concat(dfs, ignore_index=True)
    return resultado[colunas_padrao] if colunas_padrao else resultado

def salvar_simples(df, destino):
    """Salva o DataFrame em um arquivo Excel sem formatação adicional."""
    try:
        df.to_excel(destino, index=False, sheet_name="Uniao")
        messagebox.showinfo("Sucesso", f"Arquivo salvo com sucesso em:\n{destino}")
    except Exception as e:
        messagebox.showerror("Erro", f"Falha ao salvar arquivo:\n{e}")

def salvar_com_grafico(df, destino):
    """Salva o DataFrame em um arquivo Excel com um gráfico simples."""
    try:
        df.to_excel(destino, index=False, sheet_name="Uniao")
        
        with xw.App(visible=False) as app:
            wb = app.books.open(destino)
            sht = wb.sheets["Uniao"]
            
            # Determina o intervalo de dados
            last_row = sht.range("A1").end("down").row
            last_col = sht.range("A1").end("right").column
            
            # Cria o gráfico
            chart = sht.charts.add()
            chart.chart_type = "line"
            chart.set_source_data(sht.range((1, 1), (last_row, last_col)))
            chart.name = "GraficoSimples"
            chart.top = sht.range((last_row + 2, 1)).top
            chart.left = sht.range((1, last_col + 2)).left
            
            wb.save()
            wb.close()
        
        messagebox.showinfo("Sucesso", f"Arquivo com gráfico salvo em:\n{destino}")
    except Exception as e:
        messagebox.showerror("Erro", f"Falha ao criar gráfico:\n{e}")

def salvar_com_segmentacao(df, destino):
    """Salva o DataFrame em um arquivo Excel com tabela dinâmica e gráfico."""
    try:
        # Primeiro salva o DataFrame básico
        df.to_excel(destino, index=False, sheet_name="Uniao")
        
        with xw.App(visible=False) as app:
            wb = app.books.open(destino)
            sht = wb.sheets["Uniao"]
            
            # Converte para tabela Excel
            last_row = sht.range("A1").end("down").row
            last_col = sht.range("A1").end("right").column
            table_range = sht.range((1, 1), (last_row, last_col))
            
            # Cria a tabela dinâmica em uma nova planilha
            pivot_sht = wb.sheets.add("PivotTable")
            
            # Configura o cache da tabela dinâmica
            pivot_cache = wb.api.PivotCaches().Create(1, table_range.api, 1)  # SourceType=1 (xlDatabase)
            
            # Cria a tabela dinâmica
            pivot_table = pivot_cache.CreatePivotTable(
                TableDestination=pivot_sht.range("A3").api,
                TableName="PivotResumo",
                ReadData=True
            )
            
            # Adiciona campos à tabela dinâmica
            for i, col in enumerate(df.columns):
                field = pivot_table.PivotFields(col)
                if i == 0:
                    field.Orientation = 1  # xlRowField
                    field.Position = 1
                elif i == 1:
                    field.Orientation = 2  # xlColumnField
                    field.Position = 1
                else:
                    field.Orientation = 4  # xlDataField
                    field.Function = -4157  # xlSum
            
            # Cria um gráfico a partir da tabela dinâmica
            chart_sht = wb.sheets.add("PivotChart")
            pivot_table.PivotSelect("", 3)  # xlDataAndLabel
            chart = chart_sht.charts.add()
            chart.set_source_data(pivot_sht.range("A3").expand())
            chart.chart_type = "column_clustered"
            chart.name = "GraficoDinamico"
            
            wb.save()
            wb.close()
        
        messagebox.showinfo("Sucesso", f"Arquivo com tabela dinâmica salvo em:\n{destino}")
    except Exception as e:
        messagebox.showerror("Erro", f"Falha ao criar tabela dinâmica:\n{e}")

def selecionar_pasta_e_gerar(tipo):
    """Seleciona a pasta de origem e gera o arquivo de saída conforme o tipo escolhido."""
    pasta = filedialog.askdirectory(title="Selecione a pasta com arquivos Excel")
    if not pasta:
        return
    
    destino = filedialog.asksaveasfilename(
        defaultextension=".xlsx",
        filetypes=[("Excel files", "*.xlsx")],
        title="Salvar como"
    )
    if not destino:
        return
    
    try:
        df = ler_arquivos_excel(pasta)
        funcoes = {
            1: salvar_simples,
            2: salvar_com_grafico,
            3: salvar_com_segmentacao
        }
        funcoes[tipo](df, destino)
    except Exception as e:
        messagebox.showerror("Erro", f"Ocorreu um erro:\n{e}")

def criar_interface():
    """Cria a interface gráfica do usuário."""
    janela = tk.Tk()
    janela.title("Unir Excel + Gráficos")
    janela.geometry("500x250")
    
    # Configuração de estilo
    fonte = ("Arial", 12)
    btn_width = 40
    
    tk.Label(
        janela,
        text="Selecione o tipo de união e saída:",
        font=("Arial", 14, "bold")
    ).pack(pady=10)
    
    # Botões de opção
    opcoes = [
        ("1 - União Simples", 1),
        ("2 - União + Gráfico Simples", 2),
        ("3 - União + Tabela Dinâmica + Gráfico", 3)
    ]
    
    for texto, tipo in opcoes:
        tk.Button(
            janela,
            text=texto,
            command=lambda t=tipo: selecionar_pasta_e_gerar(t),
            width=btn_width,
            font=fonte
        ).pack(pady=5)
    
    janela.mainloop()

if __name__ == "__main__":
    criar_interface()