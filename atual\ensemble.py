import sys
import os
import argparse
import traceback
import copy
import re
import pandas as pd
import numpy as np
from datetime import datetime
from collections import defaultdict
from plotly import graph_objects as go
from scipy.stats import percentileofscore
from ensemble_report import (
    read_data_files,
    calculate_values,
    calculate_scatter_info,
    set_group_members,
    extended_set_group_members,
    create_delta_time_series_plot,
    export_multiple_delta_time_series_to_excel,
    get_all_wells,
    read_wcoord_file,
    get_common_realizations,
    subtract_dataframes,
    read_single_data_file,
    create_simple_histogram,
)
from cmg_out_helper import (
    obter_conexoes_classificadas_por_data,
    ler_uep_config,
    filtrar_por_uep,
)
from Auxiliares.modules.logging_module import log_execution

@log_execution
def read_dataframes(proj_path, base_path):
    proj_dataframes = read_data_files(proj_path)
    if not proj_dataframes:
        print(f"Error: No data files found in the {proj_path}.")
        return 1
    base_dataframes = None
    if base_path != None and base_path.lower() != "none":
        base_dataframes = read_data_files(base_path)
        if not base_dataframes:
            print(f"Error: No data files found in the {base_path}.")
            return 1

    return get_common_realizations(proj_dataframes, base_dataframes)

@log_execution
def process_percentile_info(proj_path, base_path, metrics, dpercentile=None):
    """
    Lê os dataframes e calcula percentile_info para cada métrica no formato:
      "entity,var,unit,n_periods"
    onde n_periods pode ser:
      - inteiro >= 0
      - inteiro < 0
      - data 'YYYY-MM-DD'

    Exemplo de chamada:
      process_percentile_info(
         "/caminho/proj", "/caminho/base",
         [
           "FIELD-PRO,Npe*,m3,-1",
           "IPB_PROD-PRO,Qo,m3/day,0",
           "P58_PROD-PRO,Npe,m3,2060-01-01"
         ]
      )
    """
    if base_path and base_path.lower() == "none":
        base_path = None

    proj_dataframes, base_dataframes = read_dataframes(proj_path, base_path)

    vars_dict = []
    for metric in metrics:
        parts = metric.split(",")
        if len(parts) != 4:
            print(f"[Warning] Métrica '{metric}' nao está no formato esperado 'entity,var,unit,n_periods'. Ignorando.")
            continue

        entity_str, var_str, unit_str, offset_str = parts

        var_dict = {
            'values': {},
            'entity': entity_str,
            'name': var_str,
            'unit': unit_str,
            'offset_str': offset_str,
        }

        # Se offset_str for int ou data, passamos como está para calculate_values
        # O parse de fato será feito dentro de calculate_values
        # (mas podemos tentar int(...) aqui so para gerar warning cedo)
        try:
            _ = int(offset_str)
            # Se deu certo, offset_str = 0, -1, etc. => OK
        except ValueError:
            # Tentar ver se é data
            try:
                datetime.strptime(offset_str, "%Y-%m-%d")
            except ValueError:
                print(f"[Warning] '{offset_str}' nao é int nem data válida (YYYY-MM-DD). Ignorando.")
                continue

        # Chama a calculate_values usando offset_str (ou offset_int)
        result = calculate_values(
            curr_dataframes=proj_dataframes,
            base_dataframes=base_dataframes,
            var=var_dict,
            n_periods=offset_str  # passamos a string; a func interna se vira
        )
        vars_dict.append(result)

    # Finalmente, chamamos a calculate_scatter_info para obter P10/P50/P90, etc.
    dpercentile_in = 0.5
    if dpercentile is not None:
        dpercentile_in = dpercentile
    percentile_info = calculate_scatter_info(
        *vars_dict,
        initial_dpercentile=dpercentile_in,
        max_dpercentile=10
    )

    print(f"dpercentile = {percentile_info['dpercentile']}")
    print(f"P10 = {percentile_info['simulations']['P10']}")
    print(f"P50 = {percentile_info['simulations']['P50']}")
    print(f"P90 = {percentile_info['simulations']['P90']}")

    return percentile_info, proj_dataframes, base_dataframes

@log_execution
def process_plot_time_series(
        percentile_info,
        proj_dataframes,
        base_dataframes,
        p10, p50, p90, user,
        time_series,
        output_html=None,
        output_excel=None
    ):
    """
    Gera as figuras de time-series (plotly) e, opcionalmente:
      - Mostra em tela (caso `output_html` = None)
      - Salva tudo em um unico HTML (caso `output_html` seja fornecido)
      - Salva os dados de cada figura em um unico Excel, cada figura em uma aba (caso `output_excel` seja fornecido)
    """
    # Atualiza o percentile_info com P10, P50, P90, user (se fornecidos)
    if p10 is not None:
        percentile_info = set_group_members(percentile_info, "P10", p10.strip('()').split(','))
    if p50 is not None:
        percentile_info = set_group_members(percentile_info, "P50", p50.strip('()').split(','))
    if p90 is not None:
        percentile_info = set_group_members(percentile_info, "P90", p90.strip('()').split(','))
    if user is not None:
        user_sims = user.strip("()").split(',')
        # Extend set_group_members() to handle group="user"
        percentile_info = extended_set_group_members(percentile_info, "user", user_sims)

    figs = []

    # Loop sobre cada time_series especificado
    for time_serie in time_series:
        try:
            entity, variable, unit = time_serie.strip('()').split(',')
        except ValueError as e:
            print(f"Error unpacking time series '{time_serie}': {e}")
            continue  # pule para o proximo item se houver erro
        fig = create_delta_time_series_plot(proj_dataframes, base_dataframes, percentile_info, entity, variable, unit)
        figs.append(fig)

    # Exibir ou salvar em HTML (já existente)
    if output_html is None:
        for fig in figs:
            fig.show()
    else:
        save_figs_to_html(figs, output_html, percentile_info)
        print(f"All time-series figures have been saved to '{output_html}'")

    # Se output_excel foi fornecido, salvamos as séries de cada figura em uma aba do Excel
    if output_excel:
        # Monta a lista (entity,variable,unit) a partir de args.time_series
        list_of_specs = []
        for ts in time_series:
            # Cada ts está no formato "(entity,variable,unit)" – remover parênteses antes do split
            entity, variable, unit = ts.strip("()").split(",")
            list_of_specs.append((entity.strip(), variable.strip(), unit.strip()))

        # Agora chamamos a funcao que gera uma aba por (entity, variable, unit)
        export_multiple_delta_time_series_to_excel(
            list_of_specs=list_of_specs,
            proj_dataframes_in=proj_dataframes,
            base_dataframes_in=base_dataframes,
            output_excel_path=output_excel
        )
        print(f"Planilha de séries temporais gerada em '{output_excel}'.")

@log_execution
def process_plot_histograms(
        percentile_info,
        proj_dataframes,
        base_dataframes,
        p10, p50, p90, user,
        histogram_specs,
        output_html=None
    ):
    """
    Cria histogramas dos valores do ensemble para uma ou mais especificacões:
      Entidade,Variavel,Unidade,Periodo
    onde 'Periodo' pode ser um int (offset em meses apos a 1a data nao-nula) ou uma data YYYY-MM-DD.
    """
    if p10 is not None:
        percentile_info = set_group_members(percentile_info, "P10", p10.strip('()').split(','))
    if p50 is not None:
        percentile_info = set_group_members(percentile_info, "P50", p50.strip('()').split(','))
    if p90 is not None:
        percentile_info = set_group_members(percentile_info, "P90", p90.strip('()').split(','))
    if user is not None:
        user_sims = user.strip("()").split(',')
        # Extend set_group_members() to handle group="user"
        percentile_info = extended_set_group_members(percentile_info, "user", user_sims)

    # 2) Iterar sobre cada especificacao e criar fig
    figs = []
    for hist_spec in histogram_specs:
        # hist_spec vem no formato "Entidade,Variavel,Unidade,Periodo"
        try:
            entity, variable, unit, offset_str = hist_spec.strip("()").split(",")
        except ValueError:
            print(f"Erro ao fazer split em '{hist_spec}'. Formato esperado: Entidade,Variavel,Unidade,Periodo")
            continue

        # 3) Montar o dicionário var_dict no formato esperado por calculate_values
        #    ver ensemble_report.calculate_values
        var_dict = {
            'values': {},         # será preenchido pela calculate_values
            'entity': entity,     # ex: "FIELD-PRO"
            'name': variable,     # ex: "Npe*"
            'unit': unit,         # ex: "m3"
            'offset_str': offset_str
        }

        # 4) Chamar calculate_values => var_out com var_out['values'][sim] = valor
        var_out = calculate_values(
            curr_dataframes=proj_dataframes,
            base_dataframes=base_dataframes,
            var=var_dict,
            n_periods=offset_str
        )

        # 5) Criar um histograma com esses valores
        fig = create_simple_histogram(var_out)
        figs.append(fig)

    # 6) Se output_html for None, faz fig.show() de cada
    if output_html is None:
        for fig in figs:
            fig.show()
    else:
        # salvar todas as figs em um unico HTML
        # Se preferir, podemos usar a mesma funcao "save_figs_to_html" que
        # já existe, mas ela exige um 'percentile_info'.
        save_figs_to_html(figs, output_html, percentile_info=percentile_info)
        print(f"Histogramas salvos em '{output_html}'")

@log_execution
def difference_dataframes(proj_dataframes, base_dataframes):
    """
    Computes df_diff = proj_dataframes[key] - base_dataframes[key] for each
    key that exists in BOTH dictionaries. If the key is missing in either dict,
    that key is ignored.

    If a column or row is missing in one of the DataFrames,
    that portion is treated as zero.

    Returns
    -------
    dict
        A dictionary {sim_number: pd.DataFrame} of the differences.
    """

    if base_dataframes is None:
        return proj_dataframes

    diff_dfs = {}
    # Only compute differences for keys common to both dictionaries
    common_keys = set(proj_dataframes.keys()).intersection(base_dataframes.keys())

    for sim_number in common_keys:
        df_proj = proj_dataframes[sim_number]
        df_base = base_dataframes[sim_number]

        # Union of indexes and columns
        union_index = df_proj.index.union(df_base.index)
        union_cols  = df_proj.columns.union(df_base.columns)

        if df_proj.index[-1] != df_base.index[-1]:
            print(f"Warning: Simulation {sim_number} end in {df_proj.index[-1]} for proj and end in {df_base.index[-1]} for base")
            continue

        # Reindex, filling missing with zero
        df_proj_ = df_proj.reindex(index=union_index, columns=union_cols, fill_value=0)
        df_base_ = df_base.reindex(index=union_index, columns=union_cols, fill_value=0)

        # Compute difference
        df_diff = df_proj_ - df_base_

        diff_dfs[sim_number] = df_diff

    return diff_dfs

@log_execution
def create_var_bubble_chart_with_ref(
    wcoord_df,
    dfs,
    wells,
    variable,
    unit,
    offset_str,
    realization="P50",
    global_max_val=1.0
):
    """
    Igual ao create_var_bubble_chart, mas usando global_max_val
    para definir o tamanho de referência das bolhas, ao invés de
    recalcular localmente.
    """

    is_int, offset_value = parse_offset_or_date(offset_str)

    x_coords = []
    y_coords = []
    bubble_values = []
    well_texts = []
    hover_texts = []

    var_name = "Oil Volume SC SCTR" if variable == "VOIP" else variable
    is_percentile_mode = realization in ["P10", "P50", "P90"]

    # Logica de loop
    for well in wells:
        row = wcoord_df[wcoord_df['Poço'] == well]
        if row.empty:
            continue
        x = row['x'].values[0]
        y = row['y'].values[0]

        if not is_percentile_mode:
            val = get_value_for_sim(sim_number=realization,
                                    dfs=dfs,
                                    well=well,
                                    var_name=var_name,
                                    unit=unit,
                                    offset_str=offset_str)
            if val is None:
                val = 0
        else:
            all_values = []
            for sim_number in dfs.keys():
                v = get_value_for_sim(sim_number=sim_number,
                                      dfs=dfs,
                                      well=well,
                                      var_name=var_name,
                                      unit=unit,
                                      offset_str=offset_str)
                if v is not None:
                    all_values.append(v)
            if len(all_values) == 0:
                val = 0
            else:
                if realization == "P10":
                    percentile_val = 90
                elif realization == "P50":
                    percentile_val = 50
                elif realization == "P90":
                    percentile_val = 10
                val = np.percentile(all_values, percentile_val)

        x_coords.append(x)
        y_coords.append(y)
        bubble_values.append(val)
        well_texts.append(well)
        hover_texts.append(
            f"<b>Well: {well}</b><br>"
            f"{variable} = {val:.2f} {unit}<br>"
            f"x = {x:.2f}, y = {y:.2f}"
        )

    # cores
    colors = []
    for val in bubble_values:
        if val >= 0:
            colors.append("green")
        else:
            colors.append("red")

    fig = go.Figure()
    # Em vez de recalcular max(abs(v)), usamos global_max_val
    # definimos sizeref baseado nesse valor
    if global_max_val <= 1e-12:
        global_max_val = 1.0
    sizeref = 2.0 * global_max_val / (40.0 ** 2)

    fig.add_trace(go.Scatter(
        x=x_coords,
        y=y_coords,
        mode='markers+text',
        text=well_texts,
        textposition='top center',
        marker=dict(
            size=[abs(v) for v in bubble_values],
            color=colors,
            sizemode='area',
            sizeref=sizeref,
            sizemin=5,
            line=dict(width=1, color='DarkSlateGrey')
        ),
        hovertext=hover_texts,
        hoverinfo='text'
    ))

    # Ajustes de layout (range, aspect ratio etc.)
    if len(x_coords) > 0:
        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        x_range = x_max - x_min
        y_range = y_max - y_min
        max_range = max(x_range, y_range)
        x_center = (x_max + x_min) / 2
        y_center = (y_max + y_min) / 2

        fig.update_xaxes(
            range=[x_center - max_range/2, x_center + max_range/2],
            scaleanchor='y',
            scaleratio=1,
            title_text="X (m)"
        )
        fig.update_yaxes(
            range=[y_center - max_range/2, y_center + max_range/2],
            title_text="Y (m)"
        )

    fig.update_layout(
        title=f"Bubble Chart: {variable} ({unit}) — {offset_value} months after first nonzero | Realization={realization}",
        showlegend=False
    )
    return fig

@log_execution
def gather_max_abs_value_for_time_serie(
    wcoord_df,
    dfs,
    wells,
    variable,
    unit,
    offset_str,
    realization
):
    """
    Faz a mesma logica de create_var_bubble_chart mas
    em vez de criar a figura, so retorna o valor absoluto máximo
    encontrado para (variable, unit) nesse time_serie específico.
    """
    is_int, offset_value = parse_offset_or_date(offset_str)

    var_name = "Oil Volume SC SCTR" if variable == "VOIP" else variable

    is_percentile_mode = realization in ["P10", "P50", "P90"]

    # Vamos acumular valores absolutos
    all_vals = []
    for well in wells:
        row = wcoord_df[wcoord_df['Poço'] == well]
        if row.empty:
            continue

        if not is_percentile_mode:
            # single realization (ex: "005")
            v = get_value_for_sim(realization, dfs, well, var_name, unit, offset_str)
            val = v if v is not None else 0
            all_vals.append(abs(val))
        else:
            # se for "P10"/"P50"/"P90", precisamos achar a lista de todos
            # e depois pegar o percentile
            all_sims_vals = []
            for sim_number in dfs.keys():
                vv = get_value_for_sim(sim_number, dfs, well, var_name, unit, offset_str)
                if vv is not None:
                    all_sims_vals.append(vv)

            if len(all_sims_vals) == 0:
                final_val = 0
            else:
                if realization == "P10":
                    percentile_val = 90  # 100 - 10
                elif realization == "P50":
                    percentile_val = 50
                elif realization == "P90":
                    percentile_val = 10  # 100 - 90
                final_val = np.percentile(all_sims_vals, percentile_val)
            all_vals.append(abs(final_val))

    # se nao houve nenhum valor (todos None), retornamos 1 para evitar problemas
    if len(all_vals) == 0:
        return 1.0
    else:
        return max(all_vals)

@log_execution
def parse_offset_or_date(n_str):
    """
    Tenta converter 'n_str' em um inteiro.
    - Se der certo, retorna (True, <int>)
    - Caso contrário, tenta converter para datetime no formato yyyy-mm-dd.
      Se der certo, retorna (False, <datetime>)
    - Se der erro, levanta ValueError.
    """
    # Tenta como inteiro
    try:
        n_int = int(n_str)
        return True, n_int  # (is_int, valor)
    except ValueError:
        pass
    # Tenta como data yyyy-mm-dd
    try:
        dt = datetime.strptime(n_str, "%Y-%m-%d")
        return False, dt  # (is_int, valor)
    except ValueError as e:
        raise ValueError(f"Could not parse '{n_str}' as int or yyyy-mm-dd") from e

@log_execution
def get_value_for_sim(
    sim_number: str,
    dfs: dict,        # Dicionário {sim_number: DataFrame}
    well: str,
    var_name: str,
    unit: str,
    offset_str: str
):
    """
    Retorna o valor de (well, var_name, unit) para um dado sim_number em dfs,
    considerando offset_str (que pode ser int -> n meses, ou yyyy-mm-dd -> data).
    Se nao houver dado, ou result for NaN, retorna None.
    """
    # Se o sim_number nao existe no dfs, nada a fazer
    if sim_number not in dfs:
        return None

    df = dfs[sim_number]
    # Confere se a coluna existe
    if (well, var_name, unit) not in df.columns:
        return None

    series = df[(well, var_name, unit)]
    # Se a série nao tiver valores nao nulos>1e-12, retorna None
    nonzero_mask = series.notnull() & (series.abs() > 1e-12)
    if not nonzero_mask.any():
        return None

    is_int, offset_value = parse_offset_or_date(offset_str)

    if is_int:
        # offset em meses a partir do first_nonzero_date
        first_nonzero_date = series[nonzero_mask].index[0]
        target_date = first_nonzero_date + pd.DateOffset(months=offset_value)
    else:
        # offset_value é um datetime
        target_date = offset_value

    closest_date = df.index.asof(target_date)
    if pd.isnull(closest_date):
        return None

    # Checar se 'closest_date' de fato está no index
    if closest_date not in series.index:
        # Exemplo: se nao estiver, retorne None (ou 0, caso queira)
        return None

    val = series.loc[closest_date]
    if pd.isnull(val):
        return None

    return val

@log_execution
def process_plot_bubble_charts(
        proj_path,
        base_path,
        proj_dataframes,
        base_dataframes,
        time_series,
        output_html=None
        ):
    """
    Gera um ou mais bubble charts e exibe-os;
    se output_html for especificado, gera um unico arquivo HTML
    contendo todas as figuras.

    O diferencial agora é que TODAS as figuras de um mesmo (variable, unit)
    terao a mesma referência de tamanho (mesmo 'max_val') para as bolhas.
    """
    all_wells = get_all_wells(proj_path)
    wcoord_df = read_wcoord_file(proj_path)

    # Primeiro: vamos criar dfs com a diferenca
    diff_dataframes = difference_dataframes(proj_dataframes, base_dataframes)

    # Passo 1) Agrupar time_series por (variable, unit) e encontrar o max_abs
    # Exemplo de estrutura:
    #   grouped = {
    #       (variable, unit): [ (offset_str1, realization), (offset_str2, realization2), ... ],
    #   }
    grouped = defaultdict(list)
    for ts in time_series:
        try:
            variable, unit, offset_str, realization = ts.strip('()').split(',')
            grouped[(variable, unit)].append( (offset_str, realization) )
        except:
            print(f"Could not parse {ts}. Skipping.")
            continue

    # max_abs_for_var_unit = { (var, unit): max_abs_value }
    max_abs_for_var_unit = {}
    for (var, unit), configs in grouped.items():
        # Precisamos analisar todas as configs (offset_str, realization) desse (var, unit)
        # e pegar o maior abs.
        big_values = []
        for (offset_value, realization) in configs:
            max_abs_val = gather_max_abs_value_for_time_serie(
                wcoord_df=wcoord_df,
                dfs=diff_dataframes,
                wells=all_wells,
                variable=var,
                unit=unit,
                offset_str=offset_str,
                realization=realization
            )
            big_values.append(max_abs_val)
        if len(big_values) == 0:
            max_abs_for_var_unit[(var, unit)] = 1.0
        else:
            max_abs_for_var_unit[(var, unit)] = max(big_values)

    # Passo 2) Criar as figuras com base nesse dicionário
    figs = []
    for ts in time_series:
        try:
            variable, unit, offset_str, realization = ts.strip('()').split(',')
        except ValueError as e:
            print(f"Error unpacking time series '{ts}': {e}")
            continue

        # Chamamos create_var_bubble_chart mas passamos o max_abs_for_var_unit
        # Precisamos modificar create_var_bubble_chart para aceitar param 'global_max_val'
        # que substituirá o 'max_val' local.
        global_max_val = max_abs_for_var_unit.get((variable, unit), 1.0)

        fig = create_var_bubble_chart_with_ref(
            wcoord_df=wcoord_df,
            dfs=diff_dataframes,
            wells=all_wells,
            variable=variable,
            unit=unit,
            offset_str=offset_str,
            realization=realization,
            global_max_val=global_max_val
        )
        figs.append(fig)

    # Exibir ou salvar
    if output_html is None:
        # Modo anterior: mostra cada figura interativamente
        for fig in figs:
            fig.show()
    else:
        # Salvamos todas as figs em um unico HTML
        save_figs_to_html(figs, output_html)
        print(f"All bubble charts have been saved to {output_html}")

@log_execution
def save_figs_to_html(figs, html_filename, percentile_info=None):
    """
    Gera um unico HTML com:
      - Cabecalho fixo (header) ocupando a largura total da tela.
      - A área das figuras (content-container) limitada a 1300px de largura, centralizada.
    """

    html_content = """
<html>
<head>
  <meta charset="utf-8" />
  <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
  <link rel="stylesheet" href="https://www.w3schools.com/w3css/4/w3.css">
  <style>
    body, html {
      margin: 0; padding: 0;
      font-family: "Helvetica", sans-serif;
    }

    /* Overlay de "Carregando" */
    #loadingOverlay {
      display: flex;
      justify-content: center;
      align-items: center;
      position: fixed;
      z-index: 9999;
      top: 0; left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
    }
    #loadingOverlay span {
      font-size: 5vw;
      color: white;
    }

    /* Cabecalho fixo ocupando 100% da largura */
    .header-fixed {
      position: fixed;
      top: 0; left: 0;
      width: 100%;
      background: #f1f1f1;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
      z-index: 1000;
    }

    /* Container interno so para organizar, sem limitar a 1300px */
    .header-inner {
      padding: 10px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
    }

    /* Se quiser organizar os filtros e destaque lado a lado */
    .filters-highlight {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
    }

    .filters-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      align-items: center;
    }
    .filters-container label {
      margin-right: 5px;
      font-weight: bold;
    }
    .filters-container input[type="text"] {
      width: 180px;
      padding: 3px 6px;
    }
    .filters-container select {
      padding: 3px 6px;
    }

    .highlight-section {
      margin-left: 20px;
    }
    .highlight-row {
      margin: 5px 0;
    }
    .highlight-row input[type="text"] {
      width: 120px;
      margin-right: 5px;
    }

    /* Dropdown do percentile_info, fixado à direita (dentro do header-inner) */
    .dropdown {
      position: relative;
      display: inline-block;
      margin-top: 5px;
    }
    .dropdown-content {
      display: none;
      position: absolute;
      top: 40px;
      right: 0;
      background-color: #fafafa;
      min-width: 280px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      padding: 10px;
      z-index: 9999;
    }
    .dropdown-content.show {
      display: block;
    }

    /* Container principal das figuras */
    .content-container {
      margin-top: 160px; /* deixa espaco para o header fixo */
      max-width: 1100px;
      margin-left: auto;
      margin-right: auto;
      padding: 10px;
    }

    .figure-container {
      margin-bottom: 40px;
    #   max-width: 1100px;
    #   max-height: 1000px;
    #   overflow: auto; /* adiciona scroll se exceder */
    }
    .figure-container h3 {
      margin-top: 20px;
      margin-bottom: 10px;
    }
  </style>
</head>

<body>
  <!-- Overlay de carregamento -->
  <div id="loadingOverlay">
    <span>Carregando...</span>
  </div>
  <script>
  window.addEventListener("load", function() {
    var overlay = document.getElementById("loadingOverlay");
    if (overlay) {
      overlay.remove();
    }
  });
  </script>

  <!-- Cabecalho fixo full width -->
  <div class="header-fixed">
    <div class="header-inner">

      <!-- Parte da esquerda: Filtros + Destaque -->
      <div class="filters-highlight">
        <div class="filters-container">
          <div>
            <label for="filterInput">Título:</label>
            <input type="text" id="filterInput" onkeyup="filterFigures()" placeholder="Filtrar título...">
          </div>
          <div>
            <label for="entitySelect">Entidade:</label>
            <select id="entitySelect" onchange="filterFigures()">
              <option value="">(All)</option>
    """

    # Coletar informacões das figuras para montar selects
    figure_data = []
    collected_entities = []
    collected_variables = []

    for i, fig in enumerate(figs, start=1):
        # Título
        if hasattr(fig.layout, 'title') and getattr(fig.layout.title, 'text', None):
            fig_title = fig.layout.title.text
        else:
            fig_title = f"Figure {i}"

        # Extracao simplificada de "entity" e "variable"
        parts = fig_title.split()
        if len(parts) >= 4:
            entity_str = parts[0]
            variable_str = " ".join(parts[1:-2])
        else:
            entity_str = "unknown"
            variable_str = "unknown"

        div_id = f"plotly-figure-{i}"
        fig_html = fig.to_html(include_plotlyjs=False, full_html=False, div_id=div_id)
        figure_data.append((fig_html, fig_title, entity_str, variable_str, div_id))
        collected_entities.append(entity_str)
        collected_variables.append(variable_str)

    unique_entities = sorted(set(collected_entities))
    unique_variables = sorted(set(collected_variables))

    # Insere options de entidade
    for ent in unique_entities:
        html_content += f'              <option value="{ent}">{ent}</option>\n'

    html_content += """
            </select>
          </div>
          <div>
            <label for="variableSelect">Variável:</label>
            <select id="variableSelect" onchange="filterFigures()">
              <option value="">(All)</option>
    """
    # Insere options de variável
    for var in unique_variables:
        html_content += f'              <option value="{var}">{var}</option>\n'

    html_content += """
            </select>
          </div>
        </div> <!-- /filters-container -->

        <div class="highlight-section">
          <button class="w3-button w3-green" onclick="addHighlightRow()">Adicionar Realizacao</button>
          <button class="w3-button w3-orange" onclick="resetColors()" style="margin-left:10px;">Resetar Cores</button>
          <div id="highlightRows"></div>
        </div>
      </div> <!-- /filters-highlight -->
    """

    # Botao de percentile_info na direita
    html_content += """
      <div class="dropdown">
        <button class="w3-button w3-blue" onclick="toggleDropdown()">Percentile Info</button>
        <div id="percentileDropdown" class="dropdown-content">
    """

    # Se houver percentile_info, exibimos
    if percentile_info is not None:
        dpercentile_value = percentile_info.get('dpercentile', None)
        simulations = percentile_info.get('simulations', {}).copy()
        simulations.pop('all', None)
        simulations.pop('other', None)

        if dpercentile_value is not None:
            html_content += f"<p><b>dpercentile:</b> {dpercentile_value}</p>"

        if 'variables' in percentile_info:
            html_content += "<p><b>Variables:</b></p><ul>"
            for vd in percentile_info['variables']:
                e = vd.get('entity', '')
                n = vd.get('name', '')
                u = vd.get('unit', '')
                off = vd.get('offset_str', '')
                offset_str = f",{off}" if off else ""
                html_content += f"<li>{e},{n},{u}{offset_str}</li>"
            html_content += "</ul>"

        if simulations:
            html_content += "<p><b>Simulations:</b></p><ul>"
            for grp, sims in simulations.items():
                html_content += f"<li>{grp}: {sims}</li>"
            html_content += "</ul>"
    else:
        html_content += "<p>Nenhuma informacao de percentile_info disponível.</p>"

    html_content += """
        </div> <!-- /dropdown-content -->
      </div> <!-- /dropdown -->
    </div> <!-- /header-inner -->
  </div> <!-- /header-fixed -->

  <!-- Container principal limitado a 1300px -->
  <div class="content-container">
"""

    # Inserir cada figura
    for i, (fig_html, fig_title, entity_str, variable_str, div_id) in enumerate(figure_data, start=1):
        html_content += (
            f'<div class="figure-container" '
            f'data-title="{fig_title}" data-entity="{entity_str}" data-variable="{variable_str}">\n'
        )
        html_content += f"  <h3>{fig_title}</h3>\n"
        html_content += fig_html
        html_content += "\n<hr>\n</div>\n"

    html_content += """
  </div> <!-- /content-container -->

<script>
function filterFigures() {
  const inputText = document.getElementById('filterInput').value.toLowerCase();
  const selectedEntity = document.getElementById('entitySelect').value;
  const selectedVariable = document.getElementById('variableSelect').value;

  const containers = document.getElementsByClassName('figure-container');
  for (let i = 0; i < containers.length; i++) {
    const figTitle = containers[i].getAttribute('data-title').toLowerCase();
    const figEntity = containers[i].getAttribute('data-entity');
    const figVariable = containers[i].getAttribute('data-variable');

    const textMatch = (figTitle.indexOf(inputText) !== -1);
    const entityMatch = (!selectedEntity || selectedEntity === figEntity);
    const variableMatch = (!selectedVariable || selectedVariable === figVariable);

    containers[i].style.display = (textMatch && entityMatch && variableMatch) ? '' : 'none';
  }
}

function addHighlightRow() {
  const container = document.getElementById('highlightRows');
  const div = document.createElement('div');
  div.className = 'highlight-row';

  const inputSim = document.createElement('input');
  inputSim.type = 'text';
  inputSim.placeholder = 'Simulation 001';
  inputSim.oninput = applyAllHighlights;

  const inputColor = document.createElement('input');
  inputColor.type = 'color';
  inputColor.value = '#ff0000';
  inputColor.onchange = applyAllHighlights;

  const removeBtn = document.createElement('button');
  removeBtn.innerText = 'Remover';
  removeBtn.onclick = () => {
    div.remove();
    applyAllHighlights();
  };

  div.appendChild(inputSim);
  div.appendChild(inputColor);
  div.appendChild(removeBtn);

  container.appendChild(div);
}

function applyAllHighlights() {
  // 1) Criar mapa simColor
  const rows = document.getElementsByClassName('highlight-row');
  let simColor = {};

  for (let i = 0; i < rows.length; i++) {
    const inputs = rows[i].getElementsByTagName('input');
    if (inputs.length >= 2) {
      let simName = inputs[0].value.trim();
      let color = inputs[1].value;
      if (simName) {
        simColor[simName] = color;
      }
    }
  }

  // 2) Para cada figura, 1 unica chamada restyle
  const totalFigures = """ + str(len(figure_data)) + """;
  for (let i = 1; i <= totalFigures; i++) {
    const figId = 'plotly-figure-' + i;
    const gd = document.getElementById(figId);
    if (!gd || !gd.data) continue;

    let traceIndices = [...Array(gd.data.length).keys()];
    let newColors = new Array(gd.data.length);
    let newWidths = new Array(gd.data.length);

    for (let t = 0; t < gd.data.length; t++) {
      const simId = gd.data[t].simulationId || null;
      const origColor = gd.data[t].originalColor || '#000';
      const origWidth = gd.data[t].originalWidth || 2;

      if (simId && simColor[simId]) {
        newColors[t] = simColor[simId];
        newWidths[t] = 5;
      } else {
        newColors[t] = origColor;
        newWidths[t] = origWidth;
      }
    }

    Plotly.restyle(gd, {
      "line.color": newColors,
      "line.width": newWidths
    }, traceIndices);
  }
}

function resetColors() {
  const totalFigures = """ + str(len(figure_data)) + """;
  for (let i = 1; i <= totalFigures; i++) {
    const figId = 'plotly-figure-' + i;
    const gd = document.getElementById(figId);
    if (!gd || !gd.data) continue;

    let traceIndices = [...Array(gd.data.length).keys()];
    let newColors = [];
    let newWidths = [];

    for (let t = 0; t < gd.data.length; t++) {
      newColors.push(gd.data[t].originalColor || '#000');
      newWidths.push(gd.data[t].originalWidth || 2);
    }

    Plotly.restyle(gd, {
      "line.color": newColors,
      "line.width": newWidths
    }, traceIndices);
  }
}

// Dropdown do Percentile Info
function toggleDropdown() {
  const dd = document.getElementById('percentileDropdown');
  dd.classList.toggle('show');
}

// Ao carregar a página, guardar cor/espessura originais e extrair simulationId
window.addEventListener('load', function() {
  const totalFigures = """ + str(len(figure_data)) + """;
  for (let i = 1; i <= totalFigures; i++) {
    const figId = 'plotly-figure-' + i;
    const gd = document.getElementById(figId);
    if (!gd || !gd.data) continue;

    for (let t = 0; t < gd.data.length; t++) {
      const c = (gd.data[t].line && gd.data[t].line.color) ? gd.data[t].line.color : '#000';
      gd.data[t].originalColor = c;
      const w = (gd.data[t].line && gd.data[t].line.width) ? gd.data[t].line.width : 2;
      gd.data[t].originalWidth = w;

      let ht = gd.data[t].hovertemplate || '';
      let simMatch = ht.match(/Simulation\\s+([^<\\n]+)/);
      if (simMatch) {
        gd.data[t].simulationId = "Simulation " + simMatch[1].trim();
      } else {
        gd.data[t].simulationId = null;
      }
    }
  }
});

// Fecha dropdown se clicar fora dele
window.onclick = function(event) {
  if (!event.target.matches('.w3-button')) {
    var dropdowns = document.getElementsByClassName("dropdown-content");
    for (let i = 0; i < dropdowns.length; i++) {
      let openDropdown = dropdowns[i];
      if (openDropdown.classList.contains('show')) {
        openDropdown.classList.remove('show');
      }
    }
  }
}
</script>

</body>
</html>
"""

    with open(html_filename, "w", encoding="utf-8") as f:
        f.write(html_content)

    print(f"[save_figs_to_html] HTML gerado em: {html_filename}")

@log_execution
def process_export_excel(proj_path, base_path, export_path, proj_dataframes, base_dataframes, time_series):
    """
    Gera uma planilha Excel "wide" onde cada poco é uma linha
    e cada time_serie gera apenas UMA coluna (o valor numérico).
    """

    # 1) Wells e coordenadas
    all_wells = get_all_wells(proj_path)
    wcoord_df = read_wcoord_file(proj_path)

    # 2) Diferenca
    diff_dataframes = difference_dataframes(proj_dataframes, base_dataframes)

    # 3) data_dict[well] = {
    #       "x": ...,
    #       "y": ...,
    #       "(Np,m3,6,P50)": ...,
    #       "(Qw,m3/day,12,005)": ...
    #    }
    data_dict = {}

    # Inicializa para cada well
    for well in all_wells:
        data_dict[well] = {}
        # pega coord
        row_coord = wcoord_df[wcoord_df['Poço'] == well]
        if not row_coord.empty:
            data_dict[well]["x"] = row_coord['x'].values[0]
            data_dict[well]["y"] = row_coord['y'].values[0]
        else:
            data_dict[well]["x"] = float('nan')
            data_dict[well]["y"] = float('nan')

    for time_serie_raw in time_series:
        try:
            variable, unit, offset_str, realization = time_serie_raw.strip('()').split(',')
        except ValueError as e:
            print(f"Error unpacking '{time_serie_raw}': {e}")
            continue

        # Em vez de gerar "-Value" e "-Color", geramos so a coluna time_serie_raw
        # Ex: "(Np,m3,6,P50)"
        ts_label = f"{time_serie_raw}"
        is_percentile_mode = realization in ["P10", "P50", "P90"]

        for well in all_wells:
            if well not in data_dict:
                data_dict[well] = {"x": float('nan'), "y": float('nan')}

            if not is_percentile_mode:
                # single realization
                sim_number = realization
                val = get_value_for_sim(sim_number, diff_dataframes, well, variable, unit, offset_str)
                if val is None:
                    val = 0
            else:
                # percentile across all sims
                all_values = []
                for sim_number in diff_dataframes.keys():
                    v = get_value_for_sim(sim_number, diff_dataframes, well, variable, unit, offset_str)
                    if v is not None:
                        all_values.append(v)
                if len(all_values) == 0:
                    val = 0
                else:
                    if realization == "P10":
                        percentile_val = 90
                    elif realization == "P50":
                        percentile_val = 50
                    elif realization == "P90":
                        percentile_val = 10
                    val = np.percentile(all_values, percentile_val)

            data_dict[well][ts_label] = val

    # Converter data_dict em DataFrame wide
    df_rows = []
    for well, well_data in data_dict.items():
        row = {"Well": well}
        row.update(well_data)
        df_rows.append(row)

    df_out = pd.DataFrame(df_rows)
    # Move "Well" para a frente
    cols = list(df_out.columns)
    if "Well" in cols:
        cols.remove("Well")
        df_out = df_out[ ["Well"] + cols ]

    excel_filename = export_path
    df_out.to_excel(excel_filename, index=False)
    print(f"Exported to '{excel_filename}'")

@log_execution
def process_rank_realizations(
    proj_path,
    base_path,
    export_path,
    list_of_sims,   # ex: ["001","013","119","200"]
    time_series     # ex: ["Npe,m3,2060-01-01,P50", "Np,m3,2060-01-01,P50", ...]
):
    """
    Para cada sim em `list_of_sims`, cria uma aba no Excel `export_path`.
    Cada aba terá:
       - Linha = Poco
       - Colunas = cada item em time_series
       - Célula = "Pxx", indicando em qual percentil a sim se encontra
                  em relacao a TODAS as simulacões do ensemble,
                  para aquele (poco, var, unit, offset).

    Obs: assim como em outros comandos, vamos subtrair base_dataframes
         de proj_dataframes, para trabalhar nos valores de *diferenca*.
    """

    # Lê dataframes e computa a diferenca
    proj_dataframes, base_dataframes = read_dataframes(proj_path, base_path)
    if proj_dataframes is None:
        raise ValueError("proj_dataframes is empty or invalid.")
    diff_dataframes = difference_dataframes(proj_dataframes, base_dataframes)

    # Coleta wells e coordenadas
    all_wells = get_all_wells(proj_path)
    wcoord_df = read_wcoord_file(proj_path)

    # Vamos parsear cada time_series no mesmo estilo do export_excel
    # Ex: "Npe,m3,2060-01-01,P50" => (variable="Npe", unit="m3", offset_str="2060-01-01", _="P50")
    # O quarto valor "_" é ignorado aqui, pois iremos sempre usar a sim real (list_of_sims).
    # Se o quarto valor nao for fornecido tambem funcionara.
    parsed_time_series = []
    for ts_raw in time_series:
        ts_raw = ts_raw.strip("()")  # remove possíveis parênteses ao redor
        fields = ts_raw.split(",")

        if len(fields) == 3:
            variable, unit, offset_str = fields
            underscore = None
        elif len(fields) == 4:
            variable, unit, offset_str, underscore = fields
        else:
            print(f"Could not parse time series '{ts_raw}': expecting 3 or 4 comma-separated fields.")
            continue

        # Mesmo que nao use underscore, podemos armazenar/ignorar:
        parsed_time_series.append((ts_raw, variable, unit, offset_str))

    # Abrimos um ExcelWriter para criar várias abas
    with pd.ExcelWriter(export_path) as writer:
        # Para cada realizacao que foi pedida explicitamente
        for sim in list_of_sims:
            data_dict = {}

            # Inicializa o dicionário com x,y
            for well in all_wells:
                data_dict[well] = {}
                row_coord = wcoord_df[wcoord_df['Poço'] == well]
                if not row_coord.empty:
                    data_dict[well]["x"] = row_coord['x'].values[0]
                    data_dict[well]["y"] = row_coord['y'].values[0]
                else:
                    data_dict[well]["x"] = float('nan')
                    data_dict[well]["y"] = float('nan')

            # Para cada time_series parseado
            for (ts_label, var, unit, offset_str) in parsed_time_series:
                # Precisamos:
                #  1) montar a lista de valores de TODAS as simulacões (diff_dataframes)
                #  2) descobrir o valor da sim-alvo (sim)
                #  3) calcular percentile rank
                #  4) colocar algo como "Pxx" na célula.

                #  (ts_label = string original, ex: "Npe,m3,2060-01-01,P50")

                # Montamos a distribuicao para cada well
                for well in all_wells:
                    # Coletamos os valores de cada sim do ensemble
                    # para (well, var, unit, offset_str).
                    all_sims_vals = []
                    for s in diff_dataframes.keys():
                        v = get_value_for_sim(s, diff_dataframes, well, var, unit, offset_str)
                        if v is not None:
                            all_sims_vals.append(v)

                    # Valor da sim-alvo
                    val_sim = get_value_for_sim(sim, diff_dataframes, well, var, unit, offset_str)
                    if val_sim is None:
                        val_sim = 0.0  # ou float('nan'), a critério

                    # Se nao tivermos scipy.stats, podemos fazer
                    # uma implementacao manual do percentile rank.
                    # Caso tenhamos, fica mais fácil:
                    if not all_sims_vals:
                        # Se nao tem valores, retorne "NaN" ou algo assim
                        data_dict[well][ts_label] = "NaN"
                    else:
                        if percentileofscore is not None:
                            pct = percentileofscore(all_sims_vals, val_sim, kind='mean')
                            data_dict[well][ts_label] = 100 - pct
                        else:
                            # Implementacao manual simples:
                            arr = sorted(all_sims_vals)
                            n = len(arr)
                            # posicao = quantos sao menores ou iguais a val_sim
                            # (para kind='mean', deve-se lidar com empates)
                            # mas faremos algo simples
                            count_le = sum(v <= val_sim for v in arr)
                            # rank = count_le / n, em [0,1].
                            # Transforma em percentil
                            pct_approx = (count_le / n) * 100.0
                            data_dict[well][ts_label] = 100 - pct_approx

            # Agora convertemos data_dict[well] em linha de DataFrame
            rows = []
            for well, wdata in data_dict.items():
                row = {
                    "Well": well,
                    "x": wdata.get("x", float('nan')),
                    "y": wdata.get("y", float('nan'))
                }
                # Adiciona as colunas de time_series
                for (ts_label, var, unit, offset_str) in parsed_time_series:
                    row[ts_label] = wdata.get(ts_label, "PNA")
                rows.append(row)

            df_out = pd.DataFrame(rows)
            # Reordena colunas para deixar "Well","x","y" no início
            base_cols = ["Well","x","y"]
            other_cols = [c for c in df_out.columns if c not in base_cols]
            df_out = df_out[base_cols + other_cols]

            # Salva numa aba do Excel
            sheet_name = f"Realization_{sim}"
            # sheet_name tem limite de 31 chars no Excel, se for maior convém truncar
            sheet_name = (sheet_name[:29] + "..") if len(sheet_name)>31 else sheet_name
            df_out.to_excel(writer, sheet_name=sheet_name, index=False)

    print(f"Done! Created '{export_path}' with {len(list_of_sims)} sheets.")

@log_execution
def build_intervals_for_well(well, uep_dict):
    """
    Constroi uma lista de intervalos de datas para o 'well' no dicionario uep_dict.

    Parametros:
      well (str): Nome do poco.
      uep_dict (dict): Dicionario no formato:
         {
             "YYYY.MM.DD": {
                "P58": [("WELL-A", "PRO"), ("WELL-B","INJ")],
                "CDAN": [("WELL-X","OUTRO")]
             },
             "YYYY.MM.DD2": ...
         }

    Retorna:
      intervals: lista de tuplas (start_dt, end_dt, uep_name, ent_type)
        - start_dt (datetime)
        - end_dt (datetime ou None)
        - uep_name (str), ou "FIELD" se nao houver UEP para esse well
    """
    # 1) Ordenar as datas do uep_dict como datetime
    sorted_dates = sorted(datetime.strptime(k, "%Y.%m.%d") for k in uep_dict.keys())
    intervals = []

    # Se nao houver NENHUMA data, retorno vazio (ou 1 intervalo "FIELD"?)
    if not sorted_dates:
        return intervals

    @log_execution
    def get_uep_and_type_for_well_in_date(date_str):
        """
        Devolve (uep_name, ent_type) para o well na data date_str.
        Se nao encontrar, devolve ("FIELD", "OUTRO") como fallback.
        """
        parent_dict = uep_dict[date_str]
        for parent, lista_filhos in parent_dict.items():
            for (filho, ent_type) in lista_filhos:
                if filho == well:
                    return parent, ent_type
        return ("FIELD", "OUTRO")

    for i in range(len(sorted_dates)):
        original_dt = sorted_dates[i]
        start_dt = datetime(original_dt.year, original_dt.month, 1)

        date_str = original_dt.strftime("%Y.%m.%d")
        uep_name, ent_type = get_uep_and_type_for_well_in_date(date_str)

        if i < len(sorted_dates) - 1:
            original_end_dt = sorted_dates[i+1]
            end_dt = datetime(original_end_dt.year, original_end_dt.month, 1)
        else:
            end_dt = None  # sem data final

        intervals.append((start_dt, end_dt, uep_name, ent_type))

    return intervals

@log_execution
def transform_df_with_uep_intervals(df_in, uep_dict, well_names):
    """
    Versao otimizada de transform_df_with_uep:
      - Pré-calcula intervalos [data_i, data_(i+1)) p/ cada well
      - Preenche de uma so vez os valores para cada UEP.

    Parâmetros:
      df_in (pd.DataFrame):
         colunas MultiIndex (Well, Var, Unit), index = datas (Timestamp)
      uep_dict (dict):
         Hierarquia { 'YYYY.MM.DD': {UEP: [(well, 'PRO'/'INJ'/'OUTRO'), ...], ...}, ... }
         => já filtrado para UEPs de interesse.
      well_names (set):
         Conjunto de pocos para processar.

    Retorna:
      df_out (pd.DataFrame):
         colunas MultiIndex (UEP, Well, Type, Var, Unit), index = mesmas datas.
    """
    if df_in is None or df_in.empty:
        return df_in

    # Filtra apenas colunas cujos wells estejam em well_names
    valid_cols = [col for col in df_in.columns if col[0] in well_names]
    df_in_filtered = df_in[valid_cols]

    # new_data[(uep, well, ent_type, var, unit)] = dict { date: val }
    new_data = defaultdict(dict)

    # 1) Construir intervalos para cada well
    intervals_map = {}
    for well in well_names:
        intervals_map[well] = build_intervals_for_well(well, uep_dict)
        # Ex: [ (start_dt, end_dt, "P58", "PRO"), (start_dt, end_dt, "CDAN","OUTRO"), ... ]

    # 2) Percorrer cada série (well, var, unit) do DataFrame filtrado
    for (well, var, unit) in df_in_filtered.columns:
        s = df_in_filtered[(well, var, unit)]
        if s.isnull().all():
            continue

        intervals = intervals_map[well]
        s_sorted_dates = s.index.sort_values()
        s_values = s.reindex(s_sorted_dates)

        idx_i = 0
        int_j = 0
        n_dates = len(s_sorted_dates)
        n_ints  = len(intervals)

        while idx_i < n_dates and int_j < n_ints:
            date_i = s_sorted_dates[idx_i]
            (start_dt, end_dt, uep_name, ent_type) = intervals[int_j]

            if end_dt is None:
                # Vale para todo período apos start_dt
                if date_i < start_dt:
                    idx_i += 1
                    continue
                val = s_values.iloc[idx_i]
                new_data[(uep_name, well, ent_type, var, unit)][date_i] = val
                idx_i += 1
            else:
                # Intervalo [start_dt, end_dt)
                if date_i < start_dt:
                    idx_i += 1
                    continue
                if date_i >= end_dt:
                    int_j += 1
                    continue

                val = s_values.iloc[idx_i]
                new_data[(uep_name, well, ent_type, var, unit)][date_i] = val
                idx_i += 1

    # 3) Montar df_out com MultiIndex (UEP, Well, Type, Var, Unit)
    all_dates = df_in_filtered.index
    all_new_cols = sorted(new_data.keys())

    # Cria um MultiIndex com nomes para cada nível
    col_index = pd.MultiIndex.from_tuples(
        all_new_cols,
        names=["UEP", "Well", "Type", "Var", "Unit"]
    )

    # df_out com mesmo índice de datas
    df_out = pd.DataFrame(index=all_dates, columns=col_index, dtype=float)

    # Preenche os valores
    for col_key, date_dict in new_data.items():
        for d, val in date_dict.items():
            df_out.loc[d, col_key] = val

    # Ordena colunas (so para manter consistência)
    df_out.sort_index(axis=1, inplace=True)

    return df_out

@log_execution
def process_delta_export(proj_dat_path: str, base_dat_path: str):
    """
    Lê .data de proj/base, filtra pocos via .wcoord, cria colunas (UEP,Well,Var,Unit)
    para cada DataFrame, e entao subtrai. Retorna df_diff final.
    """
    # 1) Ler DataFrame do PROJ
    proj_data_path = os.path.splitext(proj_dat_path)[0] + ".data"
    df_proj = read_single_data_file(proj_data_path)
    if df_proj is None:
        raise ValueError(f"[delta_export] Could not read project data from {proj_data_path}")

    # 2) Ler DataFrame do BASE (se nao for None)
    df_base = None
    if base_dat_path.lower() != "none":
        base_data_path = os.path.splitext(base_dat_path)[0] + ".data"
        df_base = read_single_data_file(base_data_path)
        if df_base is None:
            raise ValueError(f"[delta_export] Could not read base data from {base_data_path}")

    # 3) Ler as conexões (UEP)
    uep_config = ler_uep_config()
    # print("ler_uep_config")
    # print(uep_config)
    uep_poco_proj = obter_conexoes_classificadas_por_data(proj_dat_path)
    # print("obter_conexoes_classificadas_por_data")
    # print(uep_poco_proj)
    # input()
    uep_poco_proj = filtrar_por_uep(uep_poco_proj, uep_config)
    # print("filtrar_por_uep")
    # print(uep_poco_proj)
    # input()
    uep_poco_base = None
    if base_dat_path.lower() != "none":
        uep_poco_base = obter_conexoes_classificadas_por_data(base_dat_path)
        uep_poco_base = filtrar_por_uep(uep_poco_base, uep_config)

    # 4) Ler wcoord do proj e base, extrair nomes de pocos para filtrar
    proj_wcoord_df = read_wcoord_file(proj_dat_path)
    if proj_wcoord_df is None:
        print(f"[delta_export] .wcoord not found in: {proj_dat_path}")
        return
    proj_well_names = set(proj_wcoord_df['Poço'].unique())

    base_well_names = set()
    if base_dat_path.lower() != "none":
        base_wcoord_df = read_wcoord_file(base_dat_path)
        if base_wcoord_df is None:
            print(f"[delta_export] .wcoord not found in: {base_dat_path}")
            return
        base_well_names = set(base_wcoord_df['Poço'].unique())

    # O usuário pode decidir se quer union ou intersection.
    # Supondo union (assim todos os pocos aparecem):
    all_well_names = proj_well_names.union(base_well_names)

    # 5) Transformar df_proj para (UEP, Well, Var, Unit)
    df_proj_uep = transform_df_with_uep_intervals(df_proj, uep_poco_proj, all_well_names)

    # 6) Transformar df_base para (UEP, Well, Var, Unit), se existir
    df_base_uep = None
    if df_base is not None:
        df_base_uep = transform_df_with_uep_intervals(df_base, uep_poco_base, all_well_names)

    # print(f'df_proj_uep={df_proj_uep}')
    # if df_proj_uep is not None:
    #     df_proj_uep.to_excel(r"L:\res\campos\jubarte\er\er03\PN\2630\C2\deletar\df_proj_uep.xlsx", index=True)
    #     print('df_proj_uep')
    # print(f'df_base_uep={df_base_uep}')
    # if df_base_uep is not None:
    #     df_base_uep.to_excel(r"L:\res\campos\jubarte\er\er03\PN\2630\C2\deletar\df_base_uep.xlsx", index=True)
    #     print('df_base_uep')
    # 7) Subtrair
    df_diff = subtract_dataframes(df_proj_uep, df_base_uep)

    # df_diff.to_excel(r"L:\res\campos\jubarte\er\er03\PN\2630\C2\deletar\df_diff.xlsx", index=True)
    # print("df_diff")
    # sys.exit()

    # 8) Retorna o DataFrame final
    return df_diff


@log_execution
def process_delta_export2(
    proj_dat_path: str,
    base_dat_path: str,
    variables_str: str = None,
    pn_mode: bool = False,
    versao: str = None,
    cenario: str = None,
    campo: str = None,
    zp: str = None,
    nzp: str = None,
    projeto: str = None,
    iupi: str = None,
    dates_filter: str = ":",
    tps_desc: str = None
) -> pd.DataFrame:
    """
    Lê .data de proj/base, filtra pocos via .wcoord, cria colunas (UEP,Well,Var,Unit)
    para cada DataFrame, e entao subtrai. Retorna df_diff final.

    Recebe a saída de process_delta_export (df MultiIndex nas colunas),
    onde cada coluna é (UEP, Well, Type, Var, Unit).

    Filtra para manter apenas as colunas cujo nível `Var` (col[2]) esteja
    em `variables_str` e, por fim, retorna um DataFrame reestruturado com:
       - Colunas: [Date, UEP, Well, Var1(Unit1), Var2(Unit2), ...]
       - Index: numérico (resetado).

    Se pn_mode=True, processa as colunas especificamente para cálculo PN.
    Adiciona colunas personalizadas (Versao, Cenario, Campo, ZP, NZP, IUPI)

    Parâmetros:
        df (pd.DataFrame): DataFrame MultiIndex, colunas => (UEP, Well, Var, Unit)
                           Index => datetime (datas).
        variables_str (str): "Qo,Qw,Qg" etc. Variáveis a manter.
        pn_mode (bool): Se True, calcula valores PN e formata colunas especificamente.

    Retorna:
        df_out (pd.DataFrame): DataFrame reformado.
    """
    # 1) Ler DataFrame do PROJ
    proj_data_path = os.path.splitext(proj_dat_path)[0] + ".data"
    df_proj = read_single_data_file(proj_data_path)
    if df_proj is None:
        raise ValueError(f"[delta_export] Could not read project data from {proj_data_path}")

    # 2) Ler DataFrame do BASE (se nao for None)
    df_base = None
    if base_dat_path.lower() != "none":
        base_data_path = os.path.splitext(base_dat_path)[0] + ".data"
        df_base = read_single_data_file(base_data_path)
        if df_base is None:
            raise ValueError(f"[delta_export] Could not read base data from {base_data_path}")

    # 3) Ler as conexões (UEP)
    uep_config = ler_uep_config()
    # print("ler_uep_config")
    # print(uep_config)
    uep_poco_proj = obter_conexoes_classificadas_por_data(proj_dat_path)
    # print("obter_conexoes_classificadas_por_data")
    # print(uep_poco_proj)
    # input()
    uep_poco_proj = filtrar_por_uep(uep_poco_proj, uep_config)
    # print("filtrar_por_uep")
    # print(uep_poco_proj)
    # input()
    uep_poco_base = None
    if base_dat_path.lower() != "none":
        uep_poco_base = obter_conexoes_classificadas_por_data(base_dat_path)
        uep_poco_base = filtrar_por_uep(uep_poco_base, uep_config)

    # 4) Ler wcoord do proj e base, extrair nomes de pocos para filtrar
    proj_wcoord_df = read_wcoord_file(proj_dat_path)
    if proj_wcoord_df is None:
        print(f"[delta_export] .wcoord not found in: {proj_dat_path}")
        return
    proj_well_names = set(proj_wcoord_df['Poço'].unique())

    base_well_names = set()
    if base_dat_path.lower() != "none":
        base_wcoord_df = read_wcoord_file(base_dat_path)
        if base_wcoord_df is None:
            print(f"[delta_export] .wcoord not found in: {base_dat_path}")
            return
        base_well_names = set(base_wcoord_df['Poço'].unique())

    # O usuário pode decidir se quer union ou intersection.
    # Supondo union (assim todos os pocos aparecem):
    all_well_names = proj_well_names.union(base_well_names)

    # 5) Transformar df_proj para (UEP, Well, Var, Unit)
    df_proj_uep = transform_df_with_uep_intervals(df_proj, uep_poco_proj, all_well_names)
    df_proj_pivoted = post_process_delta_export(
        df_proj_uep,
        variables_str,
        pn_mode,
        versao,
        cenario,
        campo,
        zp,
        nzp,
        projeto,
        iupi,
        dates_filter,
        tps_desc
    )
    # df_proj_pivoted.to_excel(r"L:\res\campos\jubarte\er\er03\PN\2630\C2\deletar\df_proj_pivoted.xlsx", index=True)
    # print(f"df_proj_pivoted.columns={df_proj_pivoted.columns}")
    # sys.exit()

    # 6) Transformar df_base para (UEP, Well, Var, Unit), se existir
    df_base_uep = None
    if df_base is not None:
        df_base_uep = transform_df_with_uep_intervals(df_base, uep_poco_base, all_well_names)
        df_base_pivoted = post_process_delta_export(
            df_base_uep,
            variables_str,
            pn_mode,
            versao,
            cenario,
            campo,
            zp,
            nzp,
            projeto,
            iupi,
            dates_filter,
            tps_desc
        )
        # Claude: Inverter o sinal das colunas numéricas de df_base_pivoted
        # Identificar colunas numéricas
        numeric_cols = df_base_pivoted.select_dtypes(include=['number']).columns.tolist()

        # Remover colunas que não devem ser invertidas (como IDs, códigos, etc.)
        # Assumindo que colunas como 'NZP' não devem ser invertidas
        cols_to_exclude = ['NZP']  # Adicione outras colunas se necessário
        numeric_cols_to_invert = [col for col in numeric_cols if col not in cols_to_exclude]

        # Inverter o sinal das colunas numéricas
        df_base_pivoted[numeric_cols_to_invert] = -df_base_pivoted[numeric_cols_to_invert]

    if df_base is not None:
        # Concatenar verticalmente (df_base_pivoted abaixo de df_proj_pivoted)
        df_fim = pd.concat([df_proj_pivoted, df_base_pivoted],
                          ignore_index=True,
                          sort=False)

        # # Opcional: adicionar uma coluna para identificar a origem dos dados
        # # Criar array de identificação
        # proj_size = len(df_proj_pivoted)
        # base_size = len(df_base_pivoted)

        # source_labels = ['PROJ'] * proj_size + ['BASE'] * base_size
        # df_fim.insert(0, 'Source', source_labels)
    else:
        df_fim = df_proj_pivoted

    return df_fim

@log_execution
def process_merge_delta_exports(files, output_xlsx, file_name2proj=None):
    all_dfs = []

    for f in files:
        # Lê o arquivo excel
        df = pd.read_excel(f, engine='openpyxl')

        if file_name2proj:
            # Extrai o nome do arquivo, removendo caminho e extensao
            project_name = os.path.splitext(os.path.basename(f))[0]

            # Se quiser inserir a coluna "Projeto" apos "Date", verifique se "Date" está nas colunas
            if "Date" in df.columns:
                date_idx = df.columns.get_loc("Date")
                # Insere a coluna "Projeto" na posicao date_idx+1
                df.insert(date_idx + 1, "Projeto", project_name)
            else:
                # Caso nao exista a coluna "Date", você pode simplesmente inserir no início:
                df.insert(0, "Projeto", project_name)

        all_dfs.append(df)

    # Concatena todos os DataFrames (verticalmente)
    df_merged = pd.concat(all_dfs, ignore_index=True)

    # (Opcional) se quiser garantir que "Projeto" fique logo apos "Date", faca reorder
    # caso algum DataFrame nao tenha 'Date', você pode precisar tratar esse caso antes.
    # Mas assumindo que todos têm a coluna "Date", podemos fazer:
    # cols = df_merged.columns.tolist()
    # if "Date" in cols and "Projeto" in cols:
    #     # remove colunas do array e reinsere na ordem desejada
    #     cols.remove("Date")
    #     cols.remove("Projeto")
    #     # recoloca "Date" e "Projeto" no início, mas Date primeiro
    #     # e Projeto em seguida
    #     new_col_order = ["Date", "Projeto"] + cols
    #     df_merged = df_merged[new_col_order]

    # Salva no arquivo de saída
    df_merged.to_excel(output_xlsx, index=False)
    print(f"[merge_delta_exports] Arquivos mesclados e salvos em: {output_xlsx}")

@log_execution
def parse_column_rules(rule_str):
    """
    Analisa uma string de regras considerando vírgulas dentro de parênteses.
    """
    if not rule_str:
        return None, []

    default_value = None
    rules = []
    # Usar regex para split por vírgulas fora de parênteses
    parts = re.split(r',(?![^(]*\))', rule_str)  # Ignora vírgulas dentro de ()

    for part in parts:
        part = part.strip()
        if '=' in part and ':' in part:
            value_part, filter_part = part.split('=', 1)
            value = value_part.strip()
            col_part, vals_part = filter_part.split(':', 1)
            col_name = col_part.strip()
            # Extrai valores dentro dos parênteses (removendo espacos)
            vals = [v.strip() for v in vals_part.strip(' ()').split(',')]
            rules.append((col_name, set(vals), value))
        else:
            if default_value is None:
                default_value = part
            else:
                print(f"Warning: Multiple default values in rule '{rule_str}'. Using the first.")
    return default_value, rules

@log_execution
def apply_column_rules(df, rule_str, column_name):
    """
    Aplica as regras à coluna especificada no DataFrame.
    """
    if not rule_str:
        return df

    default_value, rules = parse_column_rules(rule_str)
    if default_value is None and not rules:
        return df

    # Inicializa a coluna com o valor padrao (se fornecido)
    if default_value is not None:
        df[column_name] = default_value
    else:
        df[column_name] = ""  # ou outro valor padrao

    # Aplica cada regra
    for (filter_col, filter_values, value) in rules:
        if filter_col not in df.columns:
            print(f"Warning: Column '{filter_col}' nao existe. Ignorando regra para '{column_name}'.")
            continue
        mask = df[filter_col].isin(filter_values)
        df.loc[mask, column_name] = value

    return df

@log_execution
def apply_tps_transformations(df, tps_desc):
    """
    Nova versao que aceita multiplas transformacões com sintaxe:
    "ColunaFiltro:(NovoCampo,NovoZP,NovoNZP)=fator*ValorFiltro;..."
    """
    if not tps_desc or not isinstance(df, pd.DataFrame) or df.empty:
        return df

    transformations = []
    errors = []

    # Dividir as transformacões por ";"
    for item in tps_desc.split(';'):
        item = item.strip()
        if not item:
            continue

        try:
            # Dividir lado esquerdo (config) e direito (fator e valor)
            left_side, right_side = item.split('=', 1)

            # Processar lado esquerdo - ColunaFiltro:(NovoCampo,NovoZP,NovoNZP)
            col_part, vals_part = left_side.split(':', 1)
            filter_col = col_part.strip()

            # Extrair valores dentro dos parênteses
            new_vals = [v.strip() for v in vals_part.strip(' ()').split(',')]
            if len(new_vals) != 3:
                raise ValueError("Deve conter exatamente 3 valores (Campo,ZP,NZP)")

            new_campo, new_zp, new_nzp = new_vals

            # Processar lado direito - fator*ValorFiltro
            factor_str, filter_value = right_side.split('*', 1)
            factor = float(factor_str.strip())
            filter_value = filter_value.strip()

            transformations.append({
                'filter_col': filter_col,
                'filter_value': filter_value,
                'factor': factor,
                'new_campo': new_campo,
                'new_zp': new_zp,
                'new_nzp': new_nzp
            })

        except Exception as e:
            errors.append(f"Item inválido '{item}': {str(e)}")
            continue

    if errors:
        print("Erros no processamento --tps:")
        for error in errors:
            print(error)
        return df

    # Aplicar cada transformacao
    indices_to_remove = []  # <--- NOVO: Lista para armazenar índices das linhas originais

    for transf in transformations:
        try:
            # Filtrar linhas onde filter_col == filter_value
            mask = df[transf['filter_col']] == transf['filter_value']
            if not mask.any():
                print(f"Nenhum match para {transf['filter_col']}={transf['filter_value']}")
                continue

            # Guardar índices das linhas originais para remocao posterior
            indices_to_remove.extend(df[mask].index.tolist())

            # Criar copia das linhas filtradas
            new_rows = df[mask].copy()

            # Aplicar fator nas colunas numéricas
            # numeric_cols = new_rows.select_dtypes(include=[np.number]).columns
            temp_numeric_cols = new_rows.select_dtypes(include=['number']).columns.tolist()
            numeric_cols = []
            for col in new_rows.columns:
                if 'Pot' in col and col in temp_numeric_cols:
                    numeric_cols.append(col)
            new_rows[numeric_cols] = new_rows[numeric_cols] * transf['factor']

            # Atualizar campos
            new_rows['Campo'] = transf['new_campo']
            new_rows['ZP'] = transf['new_zp']
            new_rows['NZP'] = transf['new_nzp']

            # Adicionar ao DataFrame
            df = pd.concat([df, new_rows], ignore_index=True)

        except Exception as e:
            print(f"Erro aplicando transformacao {transf}: {str(e)}")
            continue

    # Remover linhas originais apos processar todas as transformacoes
    # if tps_desc == "Campo:(JUB,MCB/COQ-ESS103A,005)=0.9725*JUB100;Campo:(ANC9,MCB/COQ-ESS103A,001)=0.0154*JUB100;Campo:(ANC8,MCB/COQ-ESS103A,001)=0.0035*JUB100;Campo:(ARGO,MCB/COQ-ESS103A,004)=0.0086*JUB100;Campo:(PRB,MCB/COQ-PRB2,006)=0.6218*PRB100;Campo:(MGG,MCB/COQ-PRB2,008)=0.3768*PRB100;Campo:(JUB,MCB/COQ-PRB2,031)=0.0014*PRB100":
    #     df.to_excel(r"C:\Users\<USER>\Desktop\temp\teste\df_antes.xlsx", index=True)
    #     print("df_antes")
    df = df.drop(indices_to_remove, errors='ignore')
    # if tps_desc == "Campo:(JUB,MCB/COQ-ESS103A,005)=0.9725*JUB100;Campo:(ANC9,MCB/COQ-ESS103A,001)=0.0154*JUB100;Campo:(ANC8,MCB/COQ-ESS103A,001)=0.0035*JUB100;Campo:(ARGO,MCB/COQ-ESS103A,004)=0.0086*JUB100;Campo:(PRB,MCB/COQ-PRB2,006)=0.6218*PRB100;Campo:(MGG,MCB/COQ-PRB2,008)=0.3768*PRB100;Campo:(JUB,MCB/COQ-PRB2,031)=0.0014*PRB100":
    #     df.to_excel(r"C:\Users\<USER>\Desktop\temp\teste\df_depois.xlsx", index=True)
    #     print("df_depois")
    #     input()

    return df

@log_execution
def post_process_delta_export(
    df: pd.DataFrame,
    variables_str: str = None,
    pn_mode: bool = False,
    versao: str = None,
    cenario: str = None,
    campo: str = None,
    zp: str = None,
    nzp: str = None,
    projeto: str = None,
    iupi: str = None,
    dates_filter: str = ":",
    tps_desc: str = None
) -> pd.DataFrame:
    """
    Recebe a saída de process_delta_export (df MultiIndex nas colunas),
    onde cada coluna é (UEP, Well, Type, Var, Unit).

    Filtra para manter apenas as colunas cujo nível `Var` (col[2]) esteja
    em `variables_str` e, por fim, retorna um DataFrame reestruturado com:
       - Colunas: [Date, UEP, Well, Var1(Unit1), Var2(Unit2), ...]
       - Index: numérico (resetado).

    Se pn_mode=True, processa as colunas especificamente para cálculo PN.
    Adiciona colunas personalizadas (Versao, Cenario, Campo, ZP, NZP, IUPI)

    Parâmetros:
        df (pd.DataFrame): DataFrame MultiIndex, colunas => (UEP, Well, Var, Unit)
                           Index => datetime (datas).
        variables_str (str): "Qo,Qw,Qg" etc. Variáveis a manter.
        pn_mode (bool): Se True, calcula valores PN e formata colunas especificamente.

    Retorna:
        df_out (pd.DataFrame): DataFrame reformado.
    """
    # print(f"df={df}")
    # sys.exit()

    if df is None or df.empty:
        print(f"Warning: df is empty!")
        return pd.DataFrame()

    if dates_filter:
        # Parse date range
        date_parts = dates_filter.split(":")
        start_str = date_parts[0].strip() if len(date_parts) > 0 else ""
        end_str = date_parts[1].strip() if len(date_parts) > 1 else ""

        try:
            start_date = pd.to_datetime(start_str) if start_str else None
            end_date = pd.to_datetime(end_str) if end_str else None
        except ValueError as e:
            print(f"Invalid date format in --dates filter: {str(e)}")
            return pd.DataFrame()

        # Filter DataFrame
        if start_date or end_date:
            if start_date and end_date:
                mask = (df.index >= start_date) & (df.index <= end_date)
            elif start_date:
                mask = df.index >= start_date
            else:
                mask = df.index <= end_date

            df = df.loc[mask]

            if df.empty:
                print("Warning: Date filter resulted in empty DataFrame")
                return pd.DataFrame()

    # Handle PN mode - override variables
    if pn_mode:
        variables = ["Qo", "Qw", "Qg", "Qgco2", "Qghc", "Qgl", "On-time Fraction"]
    else:
        # 1) Converte a string de variáveis em lista
        if not variables_str:
            raise ValueError("Either --pn must be specified or --variables must be provided")
        variables = [v.strip() for v in variables_str.split(',')]

        # Verificar se as variáveis existem nas colunas
        available_vars = set(df.columns.get_level_values(3))  # Nível 'Var'
        missing_vars = set(variables) - available_vars
        if missing_vars:
            print(f"Aviso: Variaveis nao encontradas: {missing_vars}")
            variables = list(set(variables) & available_vars)

    # 2) Filtrar colunas cujo nível `Var` (nível=3) esteja em `variables`
    #    col = (UEP, Well, Type, Var, Unit)
    columns_of_interest = []
    for col in df.columns:
        # col[3] => Var
        if col[3] in variables:
            columns_of_interest.append(col)

    if not columns_of_interest:
        print("[post_process_delta_export] Nenhuma coluna corresponde às variáveis fornecidas.")
        return pd.DataFrame()

    df_filtered = df.loc[:, columns_of_interest]

    # 3) "Empilhar" todos os níveis do MultiIndex de colunas => 5 níveis
    df_stacked = df_filtered.stack(level=[0,1,2,3,4], future_stack=True)
    df_stacked.name = "Value"
    df_stacked = df_stacked.reset_index()

    # 4) Ajustar nomes das colunas apos reset_index
    df_stacked.columns = ["Date", "UEP", "Well", "Type", "Var", "Unit", "Value"]

    # 5) Criar uma coluna combinada Var_Unit
    df_stacked["Var_Unit"] = df_stacked["Var"] + "(" + df_stacked["Unit"] + ")"

    # 6) Pivot para termos uma coluna por Var_Unit, usando como índice (Date,UEP,Well,Type)
    df_pivoted = df_stacked.pivot(
        index=["Date","UEP","Well","Type"],
        columns="Var_Unit",
        values="Value"
    ).reset_index()

    # Handle PN mode specific processing
    if pn_mode:
        # Calculate PN values by dividing by On-time Fraction
        for var in ["Qg", "Qgco2", "Qghc", "Qgl", "Qo", "Qw"]:
            var_col = f"{var}(m3/day)"
            otf_col = "On-time Fraction(fraction)"

            if var_col in df_pivoted.columns and otf_col in df_pivoted.columns:
                # Calculate PN values
                df_pivoted[f"{var} Pot(m3/day)"] = df_pivoted[var_col] / df_pivoted[otf_col].replace(0, 1)

                # For injection wells, create injection-specific columns
                if var in ["Qg", "Qgco2", "Qghc", "Qw"]:
                    mask_inj = df_pivoted["Type"] == "INJ"
                    df_pivoted.loc[mask_inj, f"{var}i Pot(m3/day)"] = df_pivoted.loc[mask_inj, f"{var} Pot(m3/day)"]
                    df_pivoted.loc[mask_inj, f"{var} Pot(m3/day)"] = 0

        # Convert gas columns from m3/day to mil m3/day
        gas_vars = ["Qg", "Qgco2", "Qghc", "Qgl", "Qgi", "Qgco2i", "Qghci"]
        for var in gas_vars:
            old_col = f"{var} Pot(m3/day)"
            new_col = f"{var} Pot(mil m3/day)"

            if old_col in df_pivoted.columns:
                # Convert to mil m3/day by dividing by 1000
                df_pivoted[new_col] = df_pivoted[old_col] / 1000
                # Drop the old column
                df_pivoted = df_pivoted.drop(columns=[old_col])

        # Keep only the columns we want in the final output
        keep_cols = ["Date", "UEP", "Well"]  # Type will be dropped
        pn_cols = [
            "Qo Pot(m3/day)", "Qw Pot(m3/day)", "Qwi Pot(m3/day)",
            "Qg Pot(mil m3/day)", "Qgi Pot(mil m3/day)",
            "Qgco2 Pot(mil m3/day)", "Qgco2i Pot(mil m3/day)",
            "Qghc Pot(mil m3/day)", "Qghci Pot(mil m3/day)",
            "Qgl Pot(mil m3/day)"  # No injection version for Qgl
        ]

        # Only add columns that exist in the dataframe
        final_cols = keep_cols + [col for col in pn_cols if col in df_pivoted.columns]
        df_pivoted = df_pivoted[final_cols]

        # Fill NaN with 0 for all numeric columns
        num_cols = df_pivoted.select_dtypes(include=[np.number]).columns
        df_pivoted[num_cols] = df_pivoted[num_cols].fillna(0)

        # Aplicar regras para as novas colunas
        df_pivoted = apply_column_rules(df_pivoted, versao, 'Versao')
        df_pivoted = apply_column_rules(df_pivoted, cenario, 'Cenario')
        df_pivoted = apply_column_rules(df_pivoted, campo, 'Campo')
        df_pivoted = apply_column_rules(df_pivoted, zp, 'ZP')
        df_pivoted = apply_column_rules(df_pivoted, nzp, 'NZP')
        df_pivoted = apply_column_rules(df_pivoted, projeto, 'Projeto')
        df_pivoted = apply_column_rules(df_pivoted, iupi, 'IUPI')

        # Reordenar colunas (opcional)
        desired_order = ['Versao', 'Cenario', 'Campo', 'ZP', 'NZP', 'UEP', 'Projeto', 'IUPI', 'Well', 'Date'] + \
                        [c for c in df_pivoted.columns if c not in {'Versao', 'Cenario', 'Campo', 'ZP', 'NZP', 'UEP', 'Projeto', 'IUPI', 'Well', 'Date'}]
        df_pivoted = df_pivoted[desired_order]

        if tps_desc:
            df_pivoted = apply_tps_transformations(df_pivoted, tps_desc)

        # Obter o dicionário de configuracao das UEPs
        uep_config = ler_uep_config()

        # Criar um mapeamento de UEP para realName
        uep_to_realname = {}
        for uep_key, uep_data in uep_config.items():
            if 'realName' in uep_data:
                uep_to_realname[uep_key] = uep_data['realName']

        # Substituir os valores na coluna UEP pelos valores em realName
        if 'UEP' in df_pivoted.columns:
            df_pivoted['UEP'] = df_pivoted['UEP'].map(lambda x: uep_to_realname.get(x, x))

        # Remover linhas zeradas
        # Identificar as colunas numéricas
        numeric_cols = df_pivoted.select_dtypes(include=['number']).columns.tolist()

        # Remover a coluna 'NZP' da lista de colunas numéricas, se ela existir
        if 'NZP' in numeric_cols:
            numeric_cols.remove('NZP')

        # Se houver colunas numéricas
        if len(numeric_cols) > 0:
            # Arredondar todas as colunas que contem um padrao especifico
            for col in df_pivoted.columns:
                if 'Pot' in col and col in numeric_cols:
                    # df_pivoted[col] = df_pivoted[col].round(2)
                    # df_pivoted[col] = df_pivoted[col].round(8)
                    df_pivoted[col] = df_pivoted[col].round(9)

            # Criar uma máscara que identifica linhas onde TODAS as colunas numéricas sao 0 ou NaN
            is_zero_or_nan = (df_pivoted[numeric_cols] == 0) | df_pivoted[numeric_cols].isna()
            all_zero_or_nan = is_zero_or_nan.all(axis=1)

            # Filtrar o DataFrame para manter apenas as linhas onde pelo menos uma coluna numérica tem valor
            df_pivoted = df_pivoted[~all_zero_or_nan].copy()

        # Preencher valores da coluna NZP com zeros à esquerda para ter pelo menos 3 caracteres
        if 'NZP' in df_pivoted.columns:
            df_pivoted['NZP'] = df_pivoted['NZP'].astype(str).str.strip().str.zfill(3)

        return df_pivoted

    # Original processing for non-PN mode
    all_cols = list(df_pivoted.columns)
    base_cols = ["Date","UEP","Well","Type"]
    var_cols = [c for c in all_cols if c not in base_cols]

    final_cols = base_cols + sorted(var_cols)
    df_pivoted = df_pivoted[final_cols]

    # Remove rows where all variable columns are 0 or NaN (except 'On-time Fraction')
    var_unit_cols = [col for col in var_cols if col != 'On-time Fraction(fraction)']
    if var_unit_cols:
        is_zero_or_nan = (df_pivoted[var_unit_cols] == 0) | (df_pivoted[var_unit_cols].isnull())
        mask_all = is_zero_or_nan.all(axis=1)
        df_pivoted = df_pivoted[~mask_all].copy()

    return df_pivoted

@log_execution
def main():
    parser = argparse.ArgumentParser(description="Generate ensemble report")
    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # subparser para o comando 'percentile_info'
    percentile_info_parser = subparsers.add_parser('percentile_info', help='Calculate percentile information')
    percentile_info_parser.add_argument("proj_path", help="Path to the directory containing the project data files")
    percentile_info_parser.add_argument("base_path", help="Path to the directory containing the base data files. This can be set to None.")
    # percentile_info_parser.add_argument("--entities", required=True, help="List of entities in order of significance")
    percentile_info_parser.add_argument(
        "--metrics",
        nargs="+",
        required=True,
        help="Lista de métricas no formato entity,var,unit,n_periods. "
             "Exemplo: FIELD-PRO,Npe*,m3,-1 IPB_PROD-PRO,Qo,m3/day,0 P58_PROD-PRO,Npe,m3,2060-01-01"
    )
    percentile_info_parser.add_argument(
        "--dpercentile",
        type=float,
        default=None,
        help="Valor de initial_dpercentile a ser usado em calculate_scatter_info (opcional)."
    )

    # subparser para o comando 'plot_time_series'
    plot_time_series_parser = subparsers.add_parser('plot_time_series', help='Calculate percentile information')
    plot_time_series_parser.add_argument("proj_path", help="Path to the directory containing the project data files")
    plot_time_series_parser.add_argument("base_path", help="Path to the directory containing the base data files. This can be set to None.")
    plot_time_series_parser.add_argument(
        "--metrics",
        nargs="+",
        required=True,
        help="Lista de métricas no formato 'entity,var,unit,n_periods'. "
            "Ex: FIELD-PRO,Npe*,m3,-1 IPB_PROD-PRO,Qo,m3/day,0 P58_PROD-PRO,Npe,m3,2060-01-01"
    )
    plot_time_series_parser.add_argument(
        "--dpercentile",
        type=float,
        default=None,
        help="Valor de initial_dpercentile a ser usado em calculate_scatter_info (opcional)."
    )
    plot_time_series_parser.add_argument("--p10", help="Optional. List of P10 representatives realizations")
    plot_time_series_parser.add_argument("--p50", help="Optional. List of P50 representatives realizations")
    plot_time_series_parser.add_argument("--p90", help="Optional. List of P90 representatives realizations")
    plot_time_series_parser.add_argument("--user", help="Optional. List of user-defined realizations, e.g. '(001,005,019,200)'")
    plot_time_series_parser.add_argument(
        "--time_series",
        nargs='+',
        required=True,
        help="List of time_series to plot in format (Entity,Var,Unit)"
    )
    plot_time_series_parser.add_argument("--output_html", default=None, help="If provided, saves all time-series figures into a single HTML file.")
    plot_time_series_parser.add_argument("--output_excel", default=None, help="Se fornecido, salva em um unico arquivo Excel os dados de cada figura (cada time-series) em uma aba.")

    plot_histogram_parser = subparsers.add_parser('plot_histogram', help='Plot histogram(s) of ensemble data.')
    plot_histogram_parser.add_argument("proj_path", help="Path to the directory containing the project data files")
    plot_histogram_parser.add_argument("base_path", help="Path to the directory containing the base data files. Use None to skip base.")
    plot_histogram_parser.add_argument(
        "--metrics",
        nargs="+",
        required=True,
        help="Lista de métricas no formato 'entity,var,unit,n_periods'. "
            "Ex: FIELD-PRO,Npe*,m3,-1 IPB_PROD-PRO,Qo,m3/day,0 P58_PROD-PRO,Npe,m3,2060-01-01"
    )
    plot_histogram_parser.add_argument(
        "--dpercentile",
        type=float,
        default=None,
        help="Valor de initial_dpercentile a ser usado em calculate_scatter_info (opcional)."
    )
    plot_histogram_parser.add_argument("--p10", help="Optional. List of P10 representatives realizations")
    plot_histogram_parser.add_argument("--p50", help="Optional. List of P50 representatives realizations")
    plot_histogram_parser.add_argument("--p90", help="Optional. List of P90 representatives realizations")
    plot_histogram_parser.add_argument("--user", help="Optional. List of user-defined realizations, e.g. '(001,005,019,200)'")
    plot_histogram_parser.add_argument(
        "--histograms",
        nargs="+",
        required=True,
        help="Lista de especificacões de histogramas no formato Entidade,Variavel,Unidade,Periodo. "
             "Ex: FIELD-PRO,Npe*,m3,0 FIELD-PRO,Npe*,m3,2060-01-01"
    )
    plot_histogram_parser.add_argument(
        "--output_html",
        default=None,
        help="Se fornecido, salva todos os histogramas em um unico arquivo HTML."
    )
    # python ensemble.py plot_histogram path/to/proj path/to/base \
    # --histograms "FIELD-PRO,Npe*,m3,0" "FIELD-PRO,Npe*,m3,2060-01-01" \
    # --output_html "my_histograms.html"

    # subparser para o comando 'plot_bubble_charts'
    plot_bubble_charts_parser = subparsers.add_parser('plot_bubble_charts', help='Calculate percentile information')
    plot_bubble_charts_parser.add_argument("proj_path", help="Path to the directory containing the project data files")
    plot_bubble_charts_parser.add_argument("base_path", help="Path to the directory containing the base data files. This can be set to None.")
    plot_bubble_charts_parser.add_argument("time_series", nargs='+', help="List of time_series to plot in format (Var,Unit,n_months or yyyy-mm-dd,realization)")
    plot_bubble_charts_parser.add_argument("--output_html", default=None, help="If provided, saves all bubble charts into a single HTML file at this path.")

    # subparser para o comando 'export_excel'
    export_excel_parser = subparsers.add_parser('export_excel', help='Calculate percentile information')
    export_excel_parser.add_argument("proj_path", help="Path to the directory containing the project data files")
    export_excel_parser.add_argument("base_path", help="Path to the directory containing the base data files. This can be set to None.")
    export_excel_parser.add_argument("--export_path", required=True, help="List of entities in order of significance")
    export_excel_parser.add_argument("time_series", nargs='+', help="List of time_series to plot in format (Var,Unit,n_months or yyyy-mm-dd,realization)")

    rank_parser = subparsers.add_parser('rank_realizations', help='Calcula percentil de realizacões específicas')
    rank_parser.add_argument("proj_path", help="Path to the directory containing the project data files")
    rank_parser.add_argument("base_path", help="Path to the directory containing the base data files. This can be set to None.")
    rank_parser.add_argument("--realizations", required=True, help="Ex: 001,013,119,200 (lista separada por vírgulas)")
    rank_parser.add_argument("--export_path", required=True, help="Caminho para o Excel de saída.")
    rank_parser.add_argument("time_series", nargs='+', help="Lista de time_series no formato (Var,Unit,Offset,Algo)")

    delta_export_parser = subparsers.add_parser('delta_export', help='Compute difference of single .data (derived from .dat) and export result.')
    delta_export_parser.add_argument("proj_dat", help="Path to the project .dat file")
    delta_export_parser.add_argument("base_dat", help="Path to the base .dat file (use None to skip)")
    delta_export_parser.add_argument("--variables", help="Comma-separated list of variables to keep from second level of columns")
    delta_export_parser.add_argument("--dates", default=":", help="Filter dates using format 'start:end' (inclusive). Ex: '2008-01-01:2059-03-01' or '2008-01-01:' or ':2059-03-01'")
    delta_export_parser.add_argument("--pn", action='store_true', help="Calculate PN values (overrides --variables)")
    delta_export_parser.add_argument("--versao", help="Define regras para preencher a coluna Versao. Ex: 'NZP1,NZP2=Type:(PRO,INJ)'")
    delta_export_parser.add_argument("--cenario", help="Define regras para preencher a coluna Cenario. Ex: 'IUPI1,IUPI2=Well:(PocoX,PocoY)'")
    delta_export_parser.add_argument("--campo", help="Define regras para preencher a coluna Campo. Ex: 'Nome1,Nome2=Well:(Poco1,Poco2)'")
    delta_export_parser.add_argument("--zp", help="Define regras para preencher a coluna ZP. Ex: 'ZonaA,ZonaB=UEP:(UEP1,UEP2)'")
    delta_export_parser.add_argument("--nzp", help="Define regras para preencher a coluna NZP. Ex: 'NZP1,NZP2=Type:(PRO,INJ)'")
    delta_export_parser.add_argument("--projeto", help="Define regras para preencher a coluna Campo. Ex: 'Nome1,Nome2=Well:(Poco1,Poco2)'")
    delta_export_parser.add_argument("--iupi", help="Define regras para preencher a coluna IUPI. Ex: 'IUPI1,IUPI2=Well:(PocoX,PocoY)'")
    delta_export_parser.add_argument("--tps", help="Transformacões de porcentagem por campo. Formato: CAMPO=PORCENTAGEM*ORIGEM,...")
    delta_export_parser.add_argument("--export_path", help="Path to export the resulting Excel file. If not provided, will use default path.")

    merge_parser = subparsers.add_parser('merge_delta_exports', help='Mescla vários .xlsx resultantes de delta_export em um unico arquivo')
    merge_parser.add_argument("excel_files", nargs="+", help="Lista de arquivos .xlsx a serem mesclados")
    merge_parser.add_argument("--output", required=True, help="Caminho para o .xlsx de saída")
    merge_parser.add_argument("--file_name2proj", action='store_true', help="Get the name of the projects from the file name.")
    # python meu_script.py merge_delta_exports arquivo1.xlsx arquivo2.xlsx arquivo3.xlsx --output merged.xlsx
    # python meu_script.py merge_delta_exports arquivo1.xlsx arquivo2.xlsx arquivo3.xlsx --output merged.xlsx --file_name2proj

    args = parser.parse_args()

    if args.command == 'percentile_info':
        try:
            # Chamamos a funcao de processamento passando args.metrics
            process_percentile_info(args.proj_path, args.base_path, args.metrics, dpercentile=args.dpercentile)
        except Exception as e:
            traceback.print_exc()
            print(f"Error processing percentile info: {str(e)}")
    elif args.command == 'plot_time_series':
        try:
            percentile_info, proj_dataframes, base_dataframes = process_percentile_info(args.proj_path, args.base_path, args.metrics, dpercentile=args.dpercentile)
            process_plot_time_series(
                percentile_info,
                proj_dataframes,
                base_dataframes,
                args.p10,
                args.p50,
                args.p90,
                args.user,
                args.time_series,
                output_html=args.output_html,
                output_excel=args.output_excel,
            )
        except Exception as e:
            traceback.print_exc()
            print(f"Error processing plot time series: {str(e)}")
    elif args.command == 'plot_histogram':
        try:
            percentile_info, proj_dataframes, base_dataframes = process_percentile_info(args.proj_path, args.base_path, args.metrics, dpercentile=args.dpercentile)
            process_plot_histograms(
                percentile_info,
                proj_dataframes,
                base_dataframes,
                args.p10,
                args.p50,
                args.p90,
                args.user,
                histogram_specs=args.histograms,
                output_html=args.output_html,
            )
        except Exception as e:
            traceback.print_exc()
            print(f"Error processing plot_histogram: {str(e)}")
    elif args.command == 'plot_bubble_charts':
        try:
            proj_dataframes, base_dataframes = read_dataframes(args.proj_path, args.base_path)
            process_plot_bubble_charts(
                proj_path=args.proj_path,
                base_path=args.base_path,
                proj_dataframes=proj_dataframes,
                base_dataframes=base_dataframes,
                time_series=args.time_series,
                output_html=args.output_html
            )
        except Exception as e:
            print(f"Error processing plot bubble chart: {str(e)}")
    elif args.command == 'export_excel':
        try:
            proj_dataframes, base_dataframes = read_dataframes(args.proj_path, args.base_path)
            process_export_excel(
                args.proj_path,
                args.base_path,
                args.export_path,
                proj_dataframes,
                base_dataframes,
                args.time_series,
            )
        except Exception as e:
            print(f"Error processing export excel: {str(e)}")
    elif args.command == 'rank_realizations':
        try:
            list_of_sims = [r.strip() for r in args.realizations.split(',')]
            process_rank_realizations(
                proj_path=args.proj_path,
                base_path=args.base_path,
                export_path=args.export_path,
                list_of_sims=list_of_sims,
                time_series=args.time_series
            )
        except Exception as e:
            traceback.print_exc()
            print(f"Error in rank_realizations: {str(e)}")
    elif args.command == 'delta_export':
        # print('delta_export')
        if not os.path.exists(args.proj_dat):
            raise FileNotFoundError(f"Arquivo proj_dat nao encontrado: {args.proj_dat}")
        try:
            # print('process_delta_export')
            df_final = process_delta_export2(
                    proj_dat_path=args.proj_dat,
                    base_dat_path=args.base_dat,
                    variables_str=args.variables,
                    pn_mode=args.pn,
                    versao=args.versao,
                    cenario=args.cenario,
                    campo=args.campo,
                    zp=args.zp,
                    nzp=args.nzp,
                    projeto=args.projeto,
                    iupi=args.iupi,
                    dates_filter=args.dates,
                    tps_desc=args.tps
            )

            if args.export_path:
                output = args.export_path
            else:
                dir_proj = os.path.dirname(args.proj_dat)
                if args.pn:
                    output = os.path.join(dir_proj, "delta_export_pn.xlsx")
                else:
                    output = os.path.join(dir_proj, "delta_export_difference.xlsx")

            # Reduzindo o dataframe:
            # Definir as colunas que serão usadas para agrupamento
            colunas_agrupamento = ['Versao', 'Cenario', 'Campo', 'ZP', 'NZP', 'UEP', 'Projeto', 'IUPI', 'Well', 'Date']

            # Identificar colunas numéricas para somar (todas as outras colunas que não estão no agrupamento)
            colunas_numericas = [col for col in df_final.columns if col not in colunas_agrupamento]

            # Fazer o agrupamento e soma
            df_final = df_final.groupby(colunas_agrupamento)[colunas_numericas].sum().reset_index()
            # # Alternativa mais explícita (caso queira especificar as colunas exatamente):
            # df_final = df_final.groupby(colunas_agrupamento).agg({
            #     'Qo Pot(m3/day)': 'sum',
            #     'Qw Pot(m3/day)': 'sum',
            #     'Qwi Pot(m3/day)': 'sum',
            #     'Qg Pot(mil m3/day)': 'sum',
            #     'Qgi Pot(mil m3/day)': 'sum',
            #     'Qgco2 Pot(mil m3/day)': 'sum',
            #     'Qgco2i Pot(mil m3/day)': 'sum',
            #     'Qghc Pot(mil m3/day)': 'sum',
            #     'Qghci Pot(mil m3/day)': 'sum',
            #     'Qgl Pot(mil m3/day)': 'sum'
            # }).reset_index()

            print(f"Forma do DataFrame final: {df_final.shape}")
            # print("Amostra dos dados:", df_final.head(3).to_dict())
            if args.pn:
                print("Valores unicos na coluna Campo apos formatacao:", df_final['Campo'].unique())
                print("Valores unicos na coluna ZP apos formatacao:", df_final['ZP'].unique())
                print("Valores unicos na coluna NZP apos formatacao:", df_final['NZP'].unique())
                print("Valores unicos na coluna UEP apos formatacao:", df_final['UEP'].unique())
            df_final.to_excel(output, index=False)

            # df_filtered = process_delta_export(args.proj_dat, args.base_dat)
            # if df_filtered is not None:
            #     # print('post_process_delta_export')
            #     df_final = post_process_delta_export(
            #         df_filtered,
            #         variables_str=args.variables,
            #         pn_mode=args.pn,
            #         versao=args.versao,
            #         cenario=args.cenario,
            #         campo=args.campo,
            #         zp=args.zp,
            #         nzp=args.nzp,
            #         projeto=args.projeto,
            #         iupi=args.iupi,
            #         dates_filter=args.dates,
            #         tps_desc=args.tps
            #     )

            #     if args.export_path:
            #         output = args.export_path
            #     else:
            #         dir_proj = os.path.dirname(args.proj_dat)
            #         if args.pn:
            #             output = os.path.join(dir_proj, "delta_export_pn.xlsx")
            #         else:
            #             output = os.path.join(dir_proj, "delta_export_difference.xlsx")

            #     print(f"Forma do DataFrame final: {df_final.shape}")
            #     # print("Amostra dos dados:", df_final.head(3).to_dict())
            #     if args.pn:
            #         print("Valores unicos na coluna Campo apos formatacao:", df_final['Campo'].unique())
            #         print("Valores unicos na coluna ZP apos formatacao:", df_final['ZP'].unique())
            #         print("Valores unicos na coluna NZP apos formatacao:", df_final['NZP'].unique())
            #         print("Valores unicos na coluna UEP apos formatacao:", df_final['UEP'].unique())
            #     df_final.to_excel(output, index=False)


            print(f"[delta_export] Done! Filtered result saved to {output}")
        except Exception as e:
            traceback.print_exc()
            print(f"[delta_export] Error: {str(e)}")
    elif args.command == 'merge_delta_exports':
        try:
            process_merge_delta_exports(args.excel_files, args.output, args.file_name2proj)
        except Exception as e:
            traceback.print_exc()
            print(f"[merge_delta_exports] Erro ao mesclar arquivos: {str(e)}")
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
