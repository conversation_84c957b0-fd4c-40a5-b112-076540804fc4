# Script para verificar arquivos .dat sem equivalentes .sr3 e executá-los com um programa específico

# Definir o diretório a ser verificado
$diretorio = $PSScriptRoot

# Definir o caminho do executável que processará os arquivos .dat
$executavel = "C:\Users\<USER>\AppData\Local\OPR-JUB\Optimizer\util\SimpleLauncher\SimpleLauncher.exe"

# Obter todos os arquivos .dat no diretório
$arquivosDat = Get-ChildItem -Path $diretorio -Filter "*.dat"

# Criar um array para armazenar os arquivos .dat sem correspondentes .sr3
$arquivosSemLog = @()

# Verificar cada arquivo .dat
foreach ($arquivo in $arquivosDat) {
    # Construir o nome do arquivo .sr3 correspondente
    $nomeArquivoLog = [System.IO.Path]::ChangeExtension($arquivo.FullName, ".sr3")

    # Verificar se o arquivo .sr3 existe
    if (-not (Test-Path -Path $nomeArquivoLog)) {
        # Adicionar o arquivo .dat ao array de arquivos sem correspondentes .sr3
        $arquivosSemLog += $arquivo.FullName

        Write-Host "Arquivo sem log correspondente encontrado: $($arquivo.Name)"
    }
}

# Processar os arquivos .dat sem correspondentes .sr3
foreach ($arquivo in $arquivosSemLog) {
    Write-Host "Executando programa com o arquivo: $arquivo"

    try {
        # Iniciar o processo sem redirecionamento
        $process = Start-Process -FilePath $executavel -ArgumentList "`"$arquivo`"" -PassThru

        # Aguardar um momento para que a aplicação inicie completamente
        Start-Sleep -Seconds 2

        # Adicionar biblioteca para SendKeys
        Add-Type -AssemblyName System.Windows.Forms

        # Enviar tecla Enter
        [System.Windows.Forms.SendKeys]::SendWait("{ENTER}")

        # Aguardar a conclusão do processo
        $process.WaitForExit()

        Write-Host "Processamento concluído para: $arquivo" -ForegroundColor Green
    }
    catch {
        Write-Host "Erro ao processar o arquivo: $arquivo" -ForegroundColor Red
        Write-Host "Erro: $_" -ForegroundColor Red
    }
}

# Exibir resumo
Write-Host "`nResumo do processamento:" -ForegroundColor Cyan
Write-Host "Total de arquivos .dat verificados: $($arquivosDat.Count)" -ForegroundColor Cyan
Write-Host "Total de arquivos .dat sem log correspondente: $($arquivosSemLog.Count)" -ForegroundColor Cyan

Write-Host "`nPressione qualquer tecla para finalizar..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")