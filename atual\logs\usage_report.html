
        <html>
            <head>
                <title>Relator<PERSON> de Uso de Scripts</title>
                <script src="https://cdn.plot.ly/plotly-2.32.0.min.js"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .container { max-width: 1400px; margin: 0 auto; }
                    .header { text-align: center; padding: 20px; background: #f8f9fa; border-bottom: 1px solid #dee2e6; }
                    .chart { margin: 25px 0; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,.05); }
                    .grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 25px; }
                    .full-width { grid-column: span 2; }
                    .table-container { margin: 30px 0; overflow-x: auto; }
                    table { width: 100%; border-collapse: collapse; }
                    th, td { padding: 12px 15px; text-align: left; border-bottom: 1px solid #dee2e6; }
                    th { background-color: #f8f9fa; font-weight: 600; }
                    .info-card { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                    .stats-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; text-align: center; }
                    .stat-card { background: #e9ecef; padding: 15px; border-radius: 8px; }
                    .filtro-data { background: #e9ecef; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
                    .filtro-data h3 { margin-top: 0; }
                    .date-inputs { display: flex; gap: 10px; align-items: center; flex-wrap: wrap; }
                    .date-inputs > div { display: flex; align-items: center; gap: 5px; }
                    .date-inputs label { font-weight: bold; white-space: nowrap; }
                    .date-inputs input { padding: 5px; border: 1px solid #ccc; border-radius: 4px; }
                    .date-inputs button { padding: 6px 12px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; }
                    .date-inputs button:hover { background: #45a049; }
                    .script-stats {
                        background: #f0f8ff;
                        padding: 20px;
                        border-radius: 8px;
                        margin: 30px 0;
                        border: 1px solid #d1e7ff;
                    }
                    .script-funcao-stats {
                        background: #fff0f6;
                        padding: 20px;
                        border-radius: 8px;
                        margin: 30px 0;
                        border: 1px solid #ffd6e7;
                    }
                    .script-stats h2 {
                        border-bottom: 2px solid #a8d1ff;
                        padding-bottom: 10px;
                        margin-top: 0;
                    }
                    .script-funcao-stats h2 {
                        border-bottom: 2px solid #ff85c0;
                        padding-bottom: 10px;
                        margin-top: 0;
                    }
                    .script-chart {
                        height: 500px;
                    }
                    .script-grid {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 20px;
                    }
                    .script-funcao-grid {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 20px;
                    }
                    
                    /* Estilos para o sistema de abas */
                    .tabs {
                        display: flex;
                        flex-wrap: wrap;
                        margin-bottom: 20px;
                        border-bottom: 1px solid #dee2e6;
                    }
                    .tab {
                        padding: 10px 20px;
                        cursor: pointer;
                        background-color: #f8f9fa;
                        border: 1px solid transparent;
                        border-top-left-radius: 5px;
                        border-top-right-radius: 5px;
                        margin-right: 5px;
                        margin-bottom: -1px;
                    }
                    .tab:hover {
                        background-color: #e9ecef;
                    }
                    .tab.active {
                        background-color: white;
                        border-color: #dee2e6 #dee2e6 white;
                        font-weight: bold;
                    }
                    .tab-content {
                        display: none;
                    }
                    .tab-content.active {
                        display: block;
                    }
                </style>
                <script>
                    // Dados completos para filtro
                    const logsData = [{"Script":"teste2.py","Usuario":"BH2A","Timestamp":"2025-08-04T16:26:40.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-04T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-04"},{"Script":"teste2.py","Usuario":"BH2A","Timestamp":"2025-08-04T16:27:00.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-04T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-04"},{"Script":"teste2.py","Usuario":"BH2A","Timestamp":"2025-08-04T16:27:06.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-04T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-04"},{"Script":"teste1.py","Usuario":"BH2A","Timestamp":"2025-08-04T16:27:11.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-04T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-04"},{"Script":"teste2.py","Usuario":"BH2A","Timestamp":"2025-08-04T16:27:11.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-04T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-04"},{"Script":"teste1.py","Usuario":"BH2A","Timestamp":"2025-08-05T16:27:14.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-05T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-05"},{"Script":"teste2.py","Usuario":"BH2A","Timestamp":"2025-08-05T16:27:14.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-05T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-05"},{"Script":"teste1.py","Usuario":"BH2A","Timestamp":"2025-08-05T16:27:18.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-05T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-05"},{"Script":"teste2.py","Usuario":"BH2A","Timestamp":"2025-08-05T16:27:18.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-05T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-05"},{"Script":"teste2.py","Usuario":"BH2A","Timestamp":"2025-08-06T08:36:01.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-06T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-06"},{"Script":"teste1.py","Usuario":"BH2A","Timestamp":"2025-08-06T08:36:01.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-06T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-06"},{"Script":"teste2.py","Usuario":"BH2A","Timestamp":"2025-08-06T11:05:33.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-06T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-06"},{"Script":"teste1.py","Usuario":"BH2A","Timestamp":"2025-08-06T11:19:16.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-06T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-06"},{"Script":"teste2.py","Usuario":"BH2A","Timestamp":"2025-08-06T11:19:16.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-06T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-06"},{"Script":"teste2.py","Usuario":"BH2A","Timestamp":"2025-08-06T11:19:31.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-06T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-06"},{"Script":"teste3.py","Usuario":"BH2A","Timestamp":"2025-08-06T11:19:37.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-06T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-06"},{"Script":"teste1.py","Usuario":"BH2A","Timestamp":"2025-08-06T11:19:37.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-06T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-06"},{"Script":"teste2.py","Usuario":"BH2A","Timestamp":"2025-08-06T11:19:37.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-06T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-06"},{"Script":"teste2.py","Usuario":"BH2A","Timestamp":"2025-08-06T11:26:45.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-06T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-06"},{"Script":"teste3.py","Usuario":"BH2A","Timestamp":"2025-08-06T11:26:45.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-06T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-06"},{"Script":"teste1.py","Usuario":"BH2A","Timestamp":"2025-08-06T11:26:45.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-06T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-06"},{"Script":"teste2.py","Usuario":"BH2A","Timestamp":"2025-08-07T10:16:52.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-07T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-07"},{"Script":"teste1.py","Usuario":"BH2A","Timestamp":"2025-08-07T10:16:52.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-07T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-07"},{"Script":"teste3.py","Usuario":"BH2A","Timestamp":"2025-08-07T10:16:52.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-07T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-07"},{"Script":"limitesPN.py","Usuario":"BH2A","Timestamp":"2025-08-07T11:52:06.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-07T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-07"},{"Script":"limitesPN.py","Usuario":"BH2A","Timestamp":"2025-08-08T11:28:46.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-08T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-08"},{"Script":"limitesPN.py","Usuario":"AKP4","Timestamp":"2025-08-08T11:29:26.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-08T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-08"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-08T17:01:20.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-08T00:00:00.000","Mes":"2025-08","Hora":17,"DataStr":"2025-08-08"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-08T17:01:20.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-08T00:00:00.000","Mes":"2025-08","Hora":17,"DataStr":"2025-08-08"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-08T17:01:20.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-08T00:00:00.000","Mes":"2025-08","Hora":17,"DataStr":"2025-08-08"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-08T17:12:12.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-08T00:00:00.000","Mes":"2025-08","Hora":17,"DataStr":"2025-08-08"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-08T17:12:12.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-08T00:00:00.000","Mes":"2025-08","Hora":17,"DataStr":"2025-08-08"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-08T17:12:12.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-08T00:00:00.000","Mes":"2025-08","Hora":17,"DataStr":"2025-08-08"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-11T11:19:47.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-11T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-11"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-11T11:19:47.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-11T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-11"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-11T11:19:48.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-11T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-11"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-11T14:54:07.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-11T00:00:00.000","Mes":"2025-08","Hora":14,"DataStr":"2025-08-11"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-11T14:54:07.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-11T00:00:00.000","Mes":"2025-08","Hora":14,"DataStr":"2025-08-11"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-11T14:54:08.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-11T00:00:00.000","Mes":"2025-08","Hora":14,"DataStr":"2025-08-11"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-11T15:23:38.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-11T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-11"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-11T15:23:38.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-11T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-11"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-11T15:23:38.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-11T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-11"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-11T15:56:32.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-11T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-11"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-11T15:56:32.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-11T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-11"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-11T15:56:33.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-11T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-11"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T08:44:53.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T08:44:53.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T08:44:53.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T08:46:51.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T08:46:51.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T08:46:52.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T08:50:02.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:29:29.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:29:29.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:29:29.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:31:15.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:31:15.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:31:16.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:34:24.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:43:31.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:43:31.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:43:32.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:45:19.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:45:19.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:45:20.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:49:32.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:49:32.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:49:33.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:53:22.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:53:22.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:53:23.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:57:57.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:57:57.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T09:57:58.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:01:20.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:01:20.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:01:22.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:05:22.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:05:22.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:05:24.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:09:05.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-12T10:41:36.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"WTQ8","Timestamp":"2025-08-12T10:41:36.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:46:27.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:46:27.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:46:27.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:48:27.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:48:27.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:48:28.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:52:17.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:52:17.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:52:18.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:55:57.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:55:57.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T10:55:58.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T11:00:22.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T11:00:22.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T11:00:23.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T11:03:37.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T11:03:37.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T11:03:39.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T11:07:04.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T11:07:05.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T11:07:05.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T11:10:28.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-12T11:25:48.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"WTQ8","Timestamp":"2025-08-12T11:25:49.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-12T11:30:34.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"WTQ8","Timestamp":"2025-08-12T11:30:34.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-12T11:38:31.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"WTQ8","Timestamp":"2025-08-12T11:38:31.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:13:07.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:13:07.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:13:07.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:33:15.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:33:15.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:33:15.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:34:11.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:34:11.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:34:11.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:35:19.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:35:19.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:35:19.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:37:20.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:37:20.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:37:21.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:38:49.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:38:49.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:38:50.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:40:53.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:40:53.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:40:54.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:42:28.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:42:28.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:42:29.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:44:17.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:44:17.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:44:17.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:45:52.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:45:52.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:45:53.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:47:25.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:47:25.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:47:25.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:49:20.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:49:20.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:49:21.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:53:01.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:53:01.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:53:02.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:56:31.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:56:31.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T15:56:32.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:00:54.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:00:54.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:00:55.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:04:12.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:04:12.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:04:13.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:07:42.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:07:42.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:07:43.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:11:01.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:11:01.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:11:01.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:11:57.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:11:57.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:11:57.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:13:05.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:13:05.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:13:06.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:14:13.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:14:13.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:14:13.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:15:16.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:15:16.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:15:17.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:16:24.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:16:24.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:16:25.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:17:28.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:17:28.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:17:28.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:18:31.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:18:31.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:18:31.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:19:32.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:19:32.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:19:33.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:20:43.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:20:43.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:20:43.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-12T16:21:37.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-12T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-12"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-13T08:45:43.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-13"},{"Script":"ensemble_report.py","Usuario":"WTQ8","Timestamp":"2025-08-13T08:45:43.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-13"},{"Script":"ensemble_report.py","Usuario":"WTQ8","Timestamp":"2025-08-13T10:08:05.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-13"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-13T10:08:05.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-13"},{"Script":"ensemble_report.py","Usuario":"WTQ8","Timestamp":"2025-08-13T10:08:05.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-13"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-13T10:08:05.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-13"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-13T13:33:20.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":13,"DataStr":"2025-08-13"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-13T13:39:33.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":13,"DataStr":"2025-08-13"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-13T13:40:19.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":13,"DataStr":"2025-08-13"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-13T13:51:04.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":13,"DataStr":"2025-08-13"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-13T13:54:19.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":13,"DataStr":"2025-08-13"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-13T14:27:37.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":14,"DataStr":"2025-08-13"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-13T14:39:06.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":14,"DataStr":"2025-08-13"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-13T14:49:23.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":14,"DataStr":"2025-08-13"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-13T15:17:48.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-13"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-13T15:19:59.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":15,"DataStr":"2025-08-13"},{"Script":"limitesPN.py","Usuario":"BH2A","Timestamp":"2025-08-13T16:08:50.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-13"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-13T16:08:50.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-13"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-13T16:29:31.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-13"},{"Script":"ensemble_report.py","Usuario":"WTQ8","Timestamp":"2025-08-13T16:29:31.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":16,"DataStr":"2025-08-13"},{"Script":"limitesPN.py","Usuario":"AKP4","Timestamp":"2025-08-13T18:16:53.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":18,"DataStr":"2025-08-13"},{"Script":"ajustadorPN.rev2.py","Usuario":"AKP4","Timestamp":"2025-08-13T18:16:53.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-13T00:00:00.000","Mes":"2025-08","Hora":18,"DataStr":"2025-08-13"},{"Script":"limitesPN.py","Usuario":"BH2A","Timestamp":"2025-08-14T09:34:18.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-14T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-14"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-14T09:34:18.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-14T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-14"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-14T10:07:14.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-14T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-14"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-14T10:14:18.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-14T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-14"},{"Script":"limitesPN.py","Usuario":"BH2A","Timestamp":"2025-08-14T10:14:18.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-14T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-14"},{"Script":"limitesPN.py","Usuario":"BH2A","Timestamp":"2025-08-14T10:16:28.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-14T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-14"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-14T10:16:28.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-14T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-14"},{"Script":"limitesPN.py","Usuario":"BH2A","Timestamp":"2025-08-14T11:01:30.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-14T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-14"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-14T11:01:30.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-14T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-14"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-14T14:32:19.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-14T00:00:00.000","Mes":"2025-08","Hora":14,"DataStr":"2025-08-14"},{"Script":"ensemble_report.py","Usuario":"WTQ8","Timestamp":"2025-08-14T14:32:19.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-14T00:00:00.000","Mes":"2025-08","Hora":14,"DataStr":"2025-08-14"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-14T14:40:32.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-14T00:00:00.000","Mes":"2025-08","Hora":14,"DataStr":"2025-08-14"},{"Script":"ensemble_report.py","Usuario":"WTQ8","Timestamp":"2025-08-14T14:40:32.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-14T00:00:00.000","Mes":"2025-08","Hora":14,"DataStr":"2025-08-14"},{"Script":"ensemble_report.py","Usuario":"WTQ8","Timestamp":"2025-08-15T11:29:14.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-15T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-15"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-15T11:29:14.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-15T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-15"},{"Script":"limitesPN.py","Usuario":"BH2A","Timestamp":"2025-08-15T12:30:37.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-15T00:00:00.000","Mes":"2025-08","Hora":12,"DataStr":"2025-08-15"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-15T12:30:37.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-15T00:00:00.000","Mes":"2025-08","Hora":12,"DataStr":"2025-08-15"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-18T09:13:30.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-18T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-18"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-18T09:13:42.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-18T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-18"},{"Script":"ensemble_report.py","Usuario":"WTQ8","Timestamp":"2025-08-18T09:13:42.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-18T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-18"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-18T09:19:48.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-18T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-18"},{"Script":"ensemble_report.py","Usuario":"WTQ8","Timestamp":"2025-08-18T09:19:48.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-18T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-18"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-20T11:24:14.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-20T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-20"},{"Script":"ensemble_report.py","Usuario":"WTQ8","Timestamp":"2025-08-20T11:24:14.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-20T00:00:00.000","Mes":"2025-08","Hora":11,"DataStr":"2025-08-20"},{"Script":"ensemble_report.py","Usuario":"WTQ8","Timestamp":"2025-08-21T10:55:48.000","Tipo":"Subchamada","Funcao":"'N\/A'","Data":"2025-08-21T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-21"},{"Script":"ensemble.py","Usuario":"WTQ8","Timestamp":"2025-08-21T10:55:48.000","Tipo":"Principal","Funcao":"'N\/A'","Data":"2025-08-21T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-21"},{"Script":"teste1.py","Usuario":"BH2A","Timestamp":"2025-08-25T14:55:34.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-25T00:00:00.000","Mes":"2025-08","Hora":14,"DataStr":"2025-08-25"},{"Script":"teste2.py","Usuario":"BH2A","Timestamp":"2025-08-25T14:55:34.000","Tipo":"Subchamada","Funcao":"minha_logica2","Data":"2025-08-25T00:00:00.000","Mes":"2025-08","Hora":14,"DataStr":"2025-08-25"},{"Script":"teste1.py","Usuario":"BH2A","Timestamp":"2025-08-25T14:56:32.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-25T00:00:00.000","Mes":"2025-08","Hora":14,"DataStr":"2025-08-25"},{"Script":"teste2.py","Usuario":"BH2A","Timestamp":"2025-08-25T14:56:32.000","Tipo":"Subchamada","Funcao":"minha_logica2","Data":"2025-08-25T00:00:00.000","Mes":"2025-08","Hora":14,"DataStr":"2025-08-25"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:02.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:02.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:02.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:03.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:03.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:03.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:03.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:03.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:03.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:04.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:04.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:04.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:04.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:04.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:04.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:04.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:04.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:04.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:04.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:04.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:19.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:19.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:19.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:04:19.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:16.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:16.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:16.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:16.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:16.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:16.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:16.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:16.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:16.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:16.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:16.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:16.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:17.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:17.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:17.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:17.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:17.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:17.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:17.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:17.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:24.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:24.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:24.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:05:24.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:13.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:13.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:13.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:14.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:15.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:15.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:16.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:16.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:16.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:16.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:16.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:16.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:16.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:16.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:16.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:16.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:16.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:17.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:18.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:06:18.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:07:24.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:07:25.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:07:25.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:07:25.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:31.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:31.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:31.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:32.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:32.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:32.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:33.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:33.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:33.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:33.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:33.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:33.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:33.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:33.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:33.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:33.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:34.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:35.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:35.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:09:35.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:10:47.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:10:48.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:10:48.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:10:48.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:12.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:12.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:12.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:13.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:13.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:13.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:14.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:14.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:14.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:14.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:14.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:14.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:14.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:14.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:14.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:14.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:14.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:14.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:14.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:13:14.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:14:06.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:14:06.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:14:06.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:14:06.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:03.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:03.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:03.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:04.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:04.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:04.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:05.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:05.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:05.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:05.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:05.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:05.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:05.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:05.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:05.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:06.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:06.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:07.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:07.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:15:07.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:16:22.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:16:22.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:16:22.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:16:22.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:44.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:44.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:44.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:45.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:45.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:45.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:47.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:47.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:47.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:47.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:47.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:47.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:47.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:47.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:47.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:47.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:47.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:48.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:48.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:18:48.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:20:19.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:20:20.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:20:20.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:20:20.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:55.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:55.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:55.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:56.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:56.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:56.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:58.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:58.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:58.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:58.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:58.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:58.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:58.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:58.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:58.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:58.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:58.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:59.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:59.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:22:59.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:24:02.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:24:03.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:24:03.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:24:03.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:25:59.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:25:59.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:25:59.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:00.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:00.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:00.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:02.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:02.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:02.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:02.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:02.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:02.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:02.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:02.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:02.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:02.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:02.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:03.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:03.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:26:03.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:27:11.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:27:12.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:27:12.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:27:12.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:22.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:22.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:22.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:22.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:23.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:23.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:23.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:23.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:23.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:23.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:23.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:23.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:23.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:23.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:23.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:23.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:23.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:24.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:24.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:24.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:31.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:31.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:29:31.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:36.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:36.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:36.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:36.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:36.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:36.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:37.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:37.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:37.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:37.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:37.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:37.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:37.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:37.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:37.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:37.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:37.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:38.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:38.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:38.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:44.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:44.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:30:45.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:47.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:47.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:47.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:47.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:47.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:47.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:48.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:48.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:48.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:48.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:48.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:48.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:48.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:48.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:48.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:48.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:48.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:49.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:49.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:49.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:58.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:58.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:31:58.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:58.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:58.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:58.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:58.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:58.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:58.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:59.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:59.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:59.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:59.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:59.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:59.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:59.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:59.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:59.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:59.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:59.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:59.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:59.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:32:59.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:33:08.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:33:08.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:33:08.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:01.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:01.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:01.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:01.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:01.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:01.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:02.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:02.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:02.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:02.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:02.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:02.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:02.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:02.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:02.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:02.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:02.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:03.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:03.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:03.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:12.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:12.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:34:12.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:13.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:14.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:14.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:14.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:14.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:14.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:15.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:15.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:15.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:15.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:15.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:15.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:15.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:15.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:15.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:15.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:15.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:15.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:15.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:16.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:24.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:25.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:35:25.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:24.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:24.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:24.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:25.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:25.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:25.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:26.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:26.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:26.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:26.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:26.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:26.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:26.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:26.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:26.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:26.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:26.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:27.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:27.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:27.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:34.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:34.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:36:34.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:31.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:31.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:31.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:31.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:31.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:31.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:32.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:32.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:32.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:32.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:32.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:32.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:32.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:32.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:32.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:32.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:32.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:32.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:32.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:32.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:38.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:39.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:37:39.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:27.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:27.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:27.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:27.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:27.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:27.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:28.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:28.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:28.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:28.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:28.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:28.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:28.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:28.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:28.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:28.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:28.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:28.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:28.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:28.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:29.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:29.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:38:29.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:19.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:19.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:19.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:20.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:20.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:20.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:21.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:21.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:21.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:21.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:21.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:21.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:21.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:21.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:21.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:21.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:21.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:23.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:23.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:23.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:45.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:45.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:39:45.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:56.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:56.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:56.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:56.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:56.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:56.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:58.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:58.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:58.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:58.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:58.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:58.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:58.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:58.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:58.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:58.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:58.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:59.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:59.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:40:59.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:41:33.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:41:33.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:41:33.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:46.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:46.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:46.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:47.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:47.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:47.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:48.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:48.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:48.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:48.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:48.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:48.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:48.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:48.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:48.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:48.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:48.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:50.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:50.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:42:50.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:43:11.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:43:11.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:43:11.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:23.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:23.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:23.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:24.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:24.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:24.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:25.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:25.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:25.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:25.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:25.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:25.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:25.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:25.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:25.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:25.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:25.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:25.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:25.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:25.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:43.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:43.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:44:43.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:38.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:38.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:38.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:39.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:39.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:39.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:41.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:41.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:41.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:41.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:41.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:41.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:41.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:41.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:41.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:41.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:41.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:43.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:43.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:45:43.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:46:16.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:46:17.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:46:17.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:49.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:49.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:49.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:50.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:50.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:50.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:51.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:51.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:51.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:51.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:51.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:52.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:52.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:52.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:52.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:52.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:52.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:53.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:53.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:47:53.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:48:27.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:48:27.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:48:28.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:53.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:53.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:53.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:54.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:54.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:54.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:55.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:55.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:55.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:55.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:55.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:55.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:55.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:55.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:55.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:55.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:55.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:56.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:56.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:49:57.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:50:18.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:50:18.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:50:18.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:25.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:25.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:25.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:26.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:26.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:26.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:27.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:27.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:27.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:27.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:27.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:27.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:27.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:27.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:27.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:27.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:27.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:28.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:29.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:29.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:50.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:50.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:51:50.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:53:01.000","Tipo":"Principal","Funcao":"process_merge_delta_exports","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-26T23:53:01.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-26T00:00:00.000","Mes":"2025-08","Hora":23,"DataStr":"2025-08-26"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:19.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:19.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:19.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:19.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:19.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:19.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:20.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:20.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:20.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:20.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:20.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:20.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:20.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:20.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:20.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:20.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:20.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:20.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:21.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:21.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:35.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:35.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:35.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:54:35.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:34.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:34.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:34.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:34.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:34.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:34.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:35.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:35.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:35.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:35.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:35.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:35.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:35.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:35.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:35.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:35.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:35.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:35.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:35.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:35.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:42.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:42.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:42.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:55:42.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:35.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:35.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:35.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:36.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:36.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:36.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:37.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:37.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:37.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:37.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:37.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:37.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:37.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:37.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:37.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:37.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:37.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:38.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:38.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:56:39.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:57:46.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:57:47.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:57:47.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:57:47.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:59:59.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:59:59.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T08:59:59.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":8,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:00.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:00.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:00.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:01.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:01.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:01.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:02.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:02.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:02.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:02.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:02.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:02.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:02.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:02.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:04.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:04.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:00:04.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:01:19.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:01:20.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:01:20.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:01:20.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:01.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:01.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:01.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:01.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:01.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:01.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:02.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:03.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:03.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:03.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:03.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:03.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:03.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:03.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:03.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:03.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:03.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:03.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:03.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:03.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:56.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:57.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:57.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:04:57.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:56.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:56.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:56.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:57.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:57.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:57.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:58.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:59.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:59.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:59.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:59.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:59.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:59.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:59.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:59.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:59.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:05:59.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:06:00.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:06:00.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:06:00.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:07:15.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:07:16.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:07:16.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:07:16.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:33.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:33.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:33.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:35.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:35.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:35.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:36.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:36.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:36.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:36.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:36.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:36.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:36.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:36.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:36.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:36.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:36.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:37.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:37.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:09:37.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:11:07.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:11:08.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:11:08.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:11:08.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:47.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:47.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:47.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:48.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:48.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:48.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:49.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:49.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:49.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:49.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:49.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:49.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:49.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:49.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:50.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:50.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:50.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:51.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:51.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:13:51.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:14:57.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:14:58.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:14:58.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:14:58.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:01.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:01.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:01.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:02.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:02.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:02.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:03.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:03.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:03.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:03.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:04.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:04.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:04.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:04.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:04.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:04.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:04.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:05.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:05.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:17:05.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:18:14.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:18:14.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:18:14.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:18:14.000","Tipo":"Principal","Funcao":"apply_tps_transformations","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:44.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:44.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:44.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:45.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:45.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:45.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:46.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:46.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:46.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:46.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:46.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:46.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:46.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:46.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:46.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:46.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:46.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:47.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:47.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:47.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:54.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:54.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:20:54.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:55.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:55.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:55.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:56.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:56.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:56.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:56.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:56.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:56.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:56.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:56.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:57.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:57.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:57.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:57.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:57.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:57.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:57.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:57.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:21:57.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:22:04.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:22:04.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:22:04.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:04.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:04.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:04.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:05.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:05.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:05.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:06.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:06.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:06.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:06.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:06.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:06.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:06.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:06.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:06.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:06.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:06.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:07.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:07.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:07.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:16.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:16.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:23:16.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:19.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:19.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:19.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:19.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:20.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:20.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:20.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:20.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:20.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:20.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:20.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:20.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:20.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:20.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:20.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:20.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:20.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:20.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:20.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:21.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:29.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:30.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:24:30.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:22.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:22.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:22.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:23.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:23.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:23.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:24.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:24.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:24.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:24.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:24.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:24.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:24.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:24.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:24.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:24.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:24.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:24.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:25.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:25.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:33.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:34.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:25:34.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:36.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:36.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:36.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:37.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:37.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:37.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:37.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:37.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:37.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:37.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:37.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:37.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:37.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:38.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:38.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:38.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:38.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:38.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:38.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:38.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:47.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:47.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:26:47.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:50.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:51.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:51.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:51.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:51.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:51.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:52.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:52.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:52.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:52.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:52.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:52.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:52.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:52.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:52.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:52.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:52.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:52.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:52.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:52.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:27:59.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:28:00.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:28:00.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:00.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:00.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:00.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:00.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:00.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:00.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:01.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:01.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:01.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:01.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:01.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:01.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:01.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:01.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:01.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:01.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:01.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:01.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:01.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:01.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:07.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:07.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:29:08.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:00.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:00.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:00.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:00.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:00.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:00.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:00.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:00.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:00.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:01.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:01.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:01.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:01.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:01.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:01.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:01.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:01.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:01.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:01.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:01.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:01.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:02.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:02.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:54.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:55.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:55.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:55.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:55.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:55.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:56.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:56.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:57.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:57.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:57.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:57.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:57.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:57.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:57.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:57.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:57.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:58.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:58.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:30:58.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:31:20.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:31:20.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:31:20.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:37.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:37.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:37.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:37.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:37.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:37.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:39.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:39.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:39.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:39.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:39.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:39.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:39.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:39.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:39.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:39.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:39.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:41.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:41.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:32:41.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:33:16.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:33:17.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:33:17.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:49.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:49.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:49.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:50.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:50.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:50.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:51.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:51.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:51.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:51.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:51.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:51.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:51.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:51.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:51.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:51.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:52.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:53.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:53.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:34:53.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:35:15.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:35:15.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:35:15.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:29.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:29.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:29.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:30.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:30.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:30.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:31.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:31.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:31.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:31.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:31.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:31.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:31.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:31.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:31.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:31.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:32.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:32.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:32.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:32.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:49.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:49.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:36:49.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:41.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:41.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:41.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:42.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:42.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:42.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:44.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:44.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:44.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:44.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:44.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:44.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:44.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:44.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:44.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:44.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:44.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:45.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:45.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:37:45.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:38:20.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:38:20.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:38:20.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:50.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:50.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:50.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:51.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:51.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:51.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:53.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:53.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:53.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:53.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:53.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:53.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:53.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:53.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:53.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:53.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:54.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:56.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:56.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:39:56.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:40:30.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:40:31.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:40:31.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:01.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:01.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:01.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:02.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:02.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:02.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:03.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:03.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:03.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:03.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:03.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:03.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:04.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:04.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:04.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:04.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:04.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:05.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:05.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:05.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:26.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:26.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:42:26.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:35.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:35.000","Tipo":"Principal","Funcao":"process_delta_export2","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:35.000","Tipo":"Subchamada","Funcao":"read_single_data_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:36.000","Tipo":"Subchamada","Funcao":"ler_uep_config","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:36.000","Tipo":"Subchamada","Funcao":"obter_conexoes_classificadas_por_data","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:36.000","Tipo":"Subchamada","Funcao":"extrair_hierarquia","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:37.000","Tipo":"Subchamada","Funcao":"filtrar_por_uep","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:37.000","Tipo":"Subchamada","Funcao":"get_ancestors","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:37.000","Tipo":"Subchamada","Funcao":"classify_node_ancestry","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:37.000","Tipo":"Subchamada","Funcao":"flatten_children","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:37.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_por_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:37.000","Tipo":"Subchamada","Funcao":"processar_bloco","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:37.000","Tipo":"Subchamada","Funcao":"carregar_coordenadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:37.000","Tipo":"Subchamada","Funcao":"filtrar_conexoes_e_classificar","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble_report.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:37.000","Tipo":"Subchamada","Funcao":"read_wcoord_file","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:38.000","Tipo":"Subchamada","Funcao":"colapsar_pais_para_uep_ou_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"cmg_out_helper.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:38.000","Tipo":"Subchamada","Funcao":"get_uep_or_field","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:39.000","Tipo":"Principal","Funcao":"transform_df_with_uep_intervals","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:39.000","Tipo":"Principal","Funcao":"build_intervals_for_well","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:43:39.000","Tipo":"Principal","Funcao":"get_uep_and_type_for_well_in_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:44:00.000","Tipo":"Principal","Funcao":"apply_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:44:00.000","Tipo":"Principal","Funcao":"post_process_delta_export","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:44:00.000","Tipo":"Principal","Funcao":"parse_column_rules","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:45:19.000","Tipo":"Principal","Funcao":"process_merge_delta_exports","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ensemble.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:45:19.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:57:00.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:57:00.000","Tipo":"Principal","Funcao":"processar_po","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:57:00.000","Tipo":"Principal","Funcao":"ler_arquivo_excel","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:57:08.000","Tipo":"Principal","Funcao":"renomear_colunas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:57:08.000","Tipo":"Principal","Funcao":"substituir_nan_por_zero","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:57:08.000","Tipo":"Principal","Funcao":"converter_nzp_inteiro","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"processar_PO.py","Usuario":"BH2A","Timestamp":"2025-08-27T09:58:21.000","Tipo":"Principal","Funcao":"salvar_para_excel","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":9,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:04:29.000","Tipo":"Principal","Funcao":"ler_excel","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:04:29.000","Tipo":"Principal","Funcao":"main","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"limitesPN.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:04:29.000","Tipo":"Subchamada","Funcao":"standalone_execution","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:49.000","Tipo":"Principal","Funcao":"check_limits_for_platform_ql","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:49.000","Tipo":"Principal","Funcao":"ler_ordem_projetos","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:49.000","Tipo":"Principal","Funcao":"pre_ajuste","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:49.000","Tipo":"Principal","Funcao":"analisar_projeto_cumulativo","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:49.000","Tipo":"Principal","Funcao":"analisar_plataforma","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:49.000","Tipo":"Principal","Funcao":"check_limits_for_platform_qg","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:49.000","Tipo":"Principal","Funcao":"create_campo_zp_nzp_mapping","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:49.000","Tipo":"Principal","Funcao":"get_limit_for_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:49.000","Tipo":"Principal","Funcao":"verificar_ajustes_negativos","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:49.000","Tipo":"Principal","Funcao":"check_limits_for_platform_qw","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:50.000","Tipo":"Principal","Funcao":"criar_linhas_ajuste_rateadas","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:50.000","Tipo":"Principal","Funcao":"get_unique_values","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:50.000","Tipo":"Principal","Funcao":"calcular_rateio_campo_zp","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:50.000","Tipo":"Principal","Funcao":"get_highest_priority","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:50.000","Tipo":"Principal","Funcao":"check_limits_for_platform_qo","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:50.000","Tipo":"Principal","Funcao":"flatten_and_unique","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:50.000","Tipo":"Principal","Funcao":"check_limits_for_platform_qwi","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:50.000","Tipo":"Principal","Funcao":"criar_linha_ajuste_base","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:50.000","Tipo":"Principal","Funcao":"apply_priority_sorting","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:50.000","Tipo":"Principal","Funcao":"extract_value","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:05:52.000","Tipo":"Principal","Funcao":"str_to_date","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"},{"Script":"ajustadorPN.rev2.py","Usuario":"BH2A","Timestamp":"2025-08-27T10:21:40.000","Tipo":"Principal","Funcao":"planilhaGIR","Data":"2025-08-27T00:00:00.000","Mes":"2025-08","Hora":10,"DataStr":"2025-08-27"}];
                    const allScripts = ["ajustadorPN.rev1.py", "limitesPN.py", "ranking_well_ensemble.py", "ajustadorPN_P90.py", "PO_conversor_GIR.py", "ensemble_report_single_dir.py", "ajustadorPNProbabilisticas.py", "unirExcelsPnPOs5.py", "volume_calculator.py", "readingSR3.py", "trigger_tdate_translator.py", "cmg_out_helper.py", "dispersaoPNv2.0.py", "ranking.py", "teste3.py", "ajustadorPN_rev1.py", "AddColunaExcel7.py", "ajustadorPN.TestePID4.py", "ajustadorPN_P10.py", "dispersaoPN.py", "ensemble.py", "volume_calculatorv2.py", "lerData3.py", "ensemble_report.py", "rateio_correcoes.py", "ajustadorPN.rev2.py", "dispersaoPNv3.0_P90.py", "dispersaoP10_teste.py", "testeAF.py", "volume_calculator_ultra.py", "data_analyzer.py", "dispersaoPNv3.0_P10.py", "cmg.dat_helperv03.py", "teste2.py", "logging_module.py", "ajustadorPN.py", "processar_PO.py", "cmg.dat_helper.py", "unirExcelsPnPOs4.py", "cmg.dat_helperv01.py", "teste1.py", "cmg.dat_helperv02.py", "diagnostico.py"];

                    // Funcao para converter data para formato comparavel
                    function parseDate(dateStr) {
                        return new Date(dateStr);
                    }

                    // Funcao para alternar entre abas
                    function openTab(evt, tabName) {
                        // Esconder todos os conteúdos de abas
                        const tabContents = document.getElementsByClassName("tab-content");
                        for (let i = 0; i < tabContents.length; i++) {
                            tabContents[i].classList.remove("active");
                        }
                        
                        // Desativar todas as abas
                        const tabs = document.getElementsByClassName("tab");
                        for (let i = 0; i < tabs.length; i++) {
                            tabs[i].classList.remove("active");
                        }
                        
                        // Ativar a aba atual
                        document.getElementById(tabName).classList.add("active");
                        evt.currentTarget.classList.add("active");
                        
                        // Redimensionar graficos após mudar de aba
                        setTimeout(() => {
                            const plots = document.querySelectorAll('.js-plotly-plot');
                            plots.forEach(plot => {
                                Plotly.Plots.resize(plot);
                            });
                        }, 100);
                    }

                    function aplicarFiltro() {
                        const startDateInput = document.getElementById('start-date').value;
                        const endDateInput = document.getElementById('end-date').value;

                        console.log('Filtro aplicado - Data inicial:', startDateInput, 'Data final:', endDateInput);

                        // Criar objetos Date com horarios especificos
                        const startDate = new Date(startDateInput + 'T00:00:00'); // Inicio do dia
                        const endDate = new Date(endDateInput + 'T23:59:59.999'); // Final do dia

                        console.log('Periodo de filtro:', startDate, 'ate', endDate);

                        // Filtrar dados (incluindo as datas de inicio e fim)
                        const filteredData = logsData.filter(log => {
                            const logDate = new Date(log.Timestamp);
                            const isInRange = logDate >= startDate && logDate <= endDate;

                            // Log de debug para os primeiros registros
                            if (logsData.indexOf(log) < 3) {
                                console.log(`Log ${logsData.indexOf(log)}: ${log.Timestamp} -> ${logDate} -> Incluido: ${isInRange}`);
                            }

                            return isInRange;
                        });

                        console.log('Registros filtrados:', filteredData.length, 'de', logsData.length, 'total');

                        // Atualizar todos os graficos
                        atualizarGraficos(filteredData);
                    }

                    function resetarFiltro() {
                        // Restaurar datas min/max
                        document.getElementById('start-date').value = '2025-08-04';
                        document.getElementById('end-date').value = '2025-08-27';

                        // Atualizar com todos os dados
                        atualizarGraficos(logsData);
                    }

                    function atualizarGraficos(data) {
                        // Atualizar estatisticas
                        document.getElementById('total-execucoes').textContent = data.length;
                        document.getElementById('total-principal').textContent =
                            data.filter(d => d.Tipo === 'Principal').length;
                        document.getElementById('total-subchamada').textContent =
                            data.filter(d => d.Tipo === 'Subchamada').length;
                        document.getElementById('usuarios-unicos').textContent =
                            new Set(data.map(d => d.Usuario)).size;

                        // Atualizar graficos
                        atualizarGrafico('grafico-geral-dia', data, 'Data', 'Tipo', 'Execucoes por Dia (Geral)', 'histogram', 'group');
                        atualizarGrafico('grafico-geral-mes', data, 'Mes', 'Tipo', 'Execucoes por Mes (Geral)', 'histogram');
                        atualizarGraficoPizza('grafico-geral-pizza', data, 'Tipo', 'Distribuicao por Tipo (Geral)');
                        atualizarGrafico('grafico-geral-hora', data, 'Hora', 'Tipo', 'Execucoes por Hora (Geral)', 'histogram');
                        atualizarGraficoLinha('grafico-evolucao-tipo', data, 'Data', 'Tipo', 'Evolucao de Execucoes por Tipo');

                        // Graficos de usuario
                        atualizarGraficoBarras('grafico-usuario-barras', data, 'Usuario', 'Execucoes por Usuario');
                        atualizarGraficoPizza('grafico-usuario-pizza', data, 'Usuario', 'Distribuicao por Usuario');
                        atualizarGraficoLinha('grafico-evolucao-usuario', data, 'Data', 'Usuario', 'Evolucao de Execucoes por Usuario');

                        // Graficos de execucoes principais
                        const principalData = data.filter(d => d.Tipo === 'Principal');
                        atualizarGrafico('grafico-principal-dia', principalData, 'Data', 'Script', 'Execucoes por Dia (Principal)', 'histogram', 'group');
                        atualizarGrafico('grafico-principal-mes', principalData, 'Mes', 'Script', 'Execucoes por Mes (Principal)', 'histogram');

                        // Graficos de subchamadas
                        const subchamadaData = data.filter(d => d.Tipo === 'Subchamada');
                        atualizarGrafico('grafico-subchamada-dia', subchamadaData, 'Data', 'Script', 'Execucoes por Dia (Subchamada)', 'histogram', 'group');
                        atualizarGrafico('grafico-subchamada-mes', subchamadaData, 'Mes', 'Script', 'Execucoes por Mes (Subchamada)', 'histogram');

                        // Atualizar estatisticas de scripts
                        updateScriptStats(data);
                        
                        // Atualizar estatisticas de script + funcao
                        updateScriptFuncaoStats(data);
                    }

                    // Funcao generica para atualizar histogramas
                    function atualizarGrafico(elementId, data, xAxis, color, title, type='histogram', barmode='') {
                        const traces = [];
                        const categories = [...new Set(data.map(d => d[color]))];

                        categories.forEach(category => {
                            const categoryData = data.filter(d => d[color] === category);
                            const xValues = categoryData.map(d => d[xAxis]);

                            traces.push({
                                x: xValues,
                                type: type,
                                name: category,
                                marker: { color: getColor(category) }
                            });
                        });

                        const layout = {
                            title: title,
                            barmode: barmode,
                            xaxis: { title: xAxis },
                            yaxis: { title: 'Quantidade' }
                        };

                        Plotly.react(elementId, traces, layout);
                    }

                    // Funcao para graficos de pizza
                    function atualizarGraficoPizza(elementId, data, category, title) {
                        const counts = {};
                        data.forEach(d => {
                            counts[d[category]] = (counts[d[category]] || 0) + 1;
                        });

                        const labels = Object.keys(counts);
                        const values = Object.values(counts);

                        const trace = {
                            labels: labels,
                            values: values,
                            type: 'pie',
                            hole: 0.4,
                            name: title
                        };

                        const layout = {
                            title: title
                        };

                        Plotly.react(elementId, [trace], layout);
                    }

                    // Funcao para graficos de linha
                    function atualizarGraficoLinha(elementId, data, xAxis, color, title) {
                        const traces = [];
                        const categories = [...new Set(data.map(d => d[color]))];

                        categories.forEach(category => {
                            const categoryData = data.filter(d => d[color] === category);
                            const counts = {};

                            categoryData.forEach(d => {
                                counts[d[xAxis]] = (counts[d[xAxis]] || 0) + 1;
                            });

                            const xValues = Object.keys(counts).sort();
                            const yValues = xValues.map(x => counts[x] || 0);

                            traces.push({
                                x: xValues,
                                y: yValues,
                                type: 'scatter',
                                mode: 'lines+markers',
                                name: category,
                                line: { shape: 'linear' }
                            });
                        });

                        const layout = {
                            title: title,
                            xaxis: { title: xAxis },
                            yaxis: { title: 'Quantidade' }
                        };

                        Plotly.react(elementId, traces, layout);
                    }

                    // Funcao para graficos de barras horizontales
                    function atualizarGraficoBarras(elementId, data, category, title) {
                        const counts = {};
                        data.forEach(d => {
                            counts[d[category]] = (counts[d[category]] || 0) + 1;
                        });

                        const labels = Object.keys(counts);
                        const values = Object.values(counts);

                        const trace = {
                            x: values,
                            y: labels,
                            type: 'bar',
                            orientation: 'h',
                            marker: { color: '#1f77b4' }
                        };

                        const layout = {
                            title: title,
                            xaxis: { title: 'Execucoes' },
                            yaxis: { title: category, automargin: true }
                        };

                        Plotly.react(elementId, [trace], layout);
                    }

                    // Funcao auxiliar para cores
                    function getColor(category) {
                        const colors = [
                            '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                            '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
                        ];
                        return colors[Math.abs(category.split('').reduce((a,b)=>{a=((a<<5)-a)+b.charCodeAt(0);return a&a},0)) % colors.length];
                    }

                    // ====================================================
                    // FUNCÕES PARA ESTATISTICAS DE SCRIPTS
                    // ====================================================

                    // Funcao para atualizar estatisticas de scripts
                    function updateScriptStats(data) {
                        console.log('Iniciando updateScriptStats com', data.length, 'registros');

                        // Verifica se as tabelas existem antes de continuar
                        const mostUsedTable = document.getElementById('most-used-table');
                        const leastUsedTable = document.getElementById('least-used-table');

                        if (!mostUsedTable || !leastUsedTable) {
                            console.error('Tabelas nao encontradas no DOM');
                            console.log('most-used-table exists:', !!mostUsedTable);
                            console.log('least-used-table exists:', !!leastUsedTable);
                            return;
                        }

                        // Agrupa dados por script
                        const scriptCounts = {};

                        // Inicializa com todos os scripts conhecidos
                        allScripts.forEach(script => {
                            scriptCounts[script] = {
                                Total: 0,
                                Principal: 0,
                                Subchamada: 0,
                                Ultima_Execucao: null
                            };
                        });

                        // Processa dados de log
                        data.forEach(log => {
                            if (!scriptCounts[log.Script]) {
                                scriptCounts[log.Script] = {
                                    Total: 0,
                                    Principal: 0,
                                    Subchamada: 0,
                                    Ultima_Execucao: null
                                };
                            }

                            scriptCounts[log.Script].Total++;

                            if (log.Tipo === 'Principal') {
                                scriptCounts[log.Script].Principal++;
                            } else {
                                scriptCounts[log.Script].Subchamada++;
                            }

                            const logDate = new Date(log.Timestamp);
                            if (!scriptCounts[log.Script].Ultima_Execucao ||
                                logDate > scriptCounts[log.Script].Ultima_Execucao) {
                                scriptCounts[log.Script].Ultima_Execucao = logDate;
                            }
                        });

                        // Converte para array
                        let scriptsArray = Object.keys(scriptCounts).map(script => ({
                            Script: script,
                            Total: scriptCounts[script].Total,
                            Principal: scriptCounts[script].Principal,
                            Subchamada: scriptCounts[script].Subchamada,
                            Ultima_Execucao: scriptCounts[script].Ultima_Execucao ?
                                scriptCounts[script].Ultima_Execucao.toISOString() : null
                        }));

                        console.log('Scripts processados:', scriptsArray.length);

                        // Ordena por total (ascendente) e depois por script
                        const leastUsed = [...scriptsArray]
                            .sort((a, b) => {
                                if (a.Total !== b.Total) {
                                    return a.Total - b.Total;
                                }
                                return a.Script.localeCompare(b.Script);
                            })
                            .slice(0, 10);

                        // Ordena por total (decrescente) para os mais usados
                        const mostUsed = [...scriptsArray]
                            .sort((a, b) => b.Total - a.Total)
                            .slice(0, 10);

                        console.log('Menos usados:', leastUsed.length);
                        console.log('Mais usados:', mostUsed.length);

                        // Atualiza tabelas
                        updateScriptTable('least-used-table', leastUsed);
                        updateScriptTable('most-used-table', mostUsed);

                        // Atualiza grafico de pizza
                        updateScriptPieChart(scriptsArray);
                    }

                    // Atualiza a tabela de scripts
                    function updateScriptTable(tableId, data) {
                        console.log('Atualizando tabela:', tableId, 'com', data.length, 'registros');

                        const table = document.getElementById(tableId);
                        if (!table) {
                            console.error('Tabela nao encontrada:', tableId);
                            return;
                        }

                        const tbody = table.getElementsByTagName('tbody')[0];
                        if (!tbody) {
                            console.error('tbody nao encontrado na tabela:', tableId);
                            return;
                        }

                        tbody.innerHTML = '';

                        data.forEach((script, index) => {
                            console.log(`Adicionando linha ${index}:`, script.Script, script.Total);

                            const row = document.createElement('tr');

                            const scriptCell = document.createElement('td');
                            scriptCell.textContent = script.Script;
                            row.appendChild(scriptCell);

                            const totalCell = document.createElement('td');
                            totalCell.textContent = script.Total;
                            row.appendChild(totalCell);

                            const principalCell = document.createElement('td');
                            principalCell.textContent = script.Principal;
                            row.appendChild(principalCell);

                            const subchamadaCell = document.createElement('td');
                            subchamadaCell.textContent = script.Subchamada;
                            row.appendChild(subchamadaCell);

                            const lastCell = document.createElement('td');
                            lastCell.textContent = script.Ultima_Execucao ?
                                new Date(script.Ultima_Execucao).toLocaleString() : 'Nunca';
                            row.appendChild(lastCell);

                            tbody.appendChild(row);
                        });

                        console.log('Tabela atualizada com sucesso:', tableId);
                    }

                    // Atualiza o grafico de pizza de distribuicao de scripts
                    function updateScriptPieChart(scriptsArray) {
                        console.log('Criando grafico de pizza com', scriptsArray.length, 'scripts');

                        // Filtra apenas scripts que foram usados
                        const usedScripts = scriptsArray.filter(script => script.Total > 0);
                        console.log('Scripts usados:', usedScripts.length);

                        if (usedScripts.length === 0) {
                            // Se nao ha scripts usados, mostra uma mensagem
                            const layout = {
                                title: 'Distribuicao de Uso de Scripts',
                                height: 500,
                                annotations: [{
                                    text: 'Nenhum script foi executado no periodo selecionado',
                                    showarrow: false,
                                    x: 0.5,
                                    y: 0.5,
                                    font: { size: 16 }
                                }],
                                xaxis: { visible: false },
                                yaxis: { visible: false }
                            };
                            Plotly.react('script-distribution-pie', [], layout);
                            return;
                        }

                        // Ordena por uso (decrescente)
                        const sortedScripts = usedScripts.sort((a, b) => b.Total - a.Total);

                        // Prepara dados para o grafico de pizza
                        const topScripts = sortedScripts.slice(0, 10);
                        const outrosScripts = sortedScripts.slice(10);
                        const outrosTotal = outrosScripts.reduce((sum, script) => sum + script.Total, 0);

                        let labels = topScripts.map(s => s.Script);
                        let values = topScripts.map(s => s.Total);

                        if (outrosTotal > 0) {
                            labels.push('Outros');
                            values.push(outrosTotal);
                        }

                        console.log('Labels:', labels);
                        console.log('Values:', values);

                        const trace = {
                            labels: labels,
                            values: values,
                            type: 'pie',
                            hole: 0.4,
                            textinfo: 'label+percent',
                            textposition: 'auto',
                            hovertemplate: '<b>%{label}</b><br>Execucoes: %{value}<br>Percentual: %{percent}<extra></extra>',
                            marker: {
                                colors: [
                                    '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                                    '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf',
                                    '#aec7e8'
                                ],
                                line: {
                                    color: '#ffffff',
                                    width: 2
                                }
                            }
                        };

                        const layout = {
                            title: {
                                text: 'Distribuicao de Uso de Scripts',
                                font: { size: 16 }
                            },
                            height: 500,
                            margin: { t: 60, b: 50, l: 50, r: 120 },
                            showlegend: true,
                            legend: {
                                orientation: 'v',
                                x: 1.02,
                                y: 0.5,
                                font: { size: 12 }
                            }
                        };

                        console.log('Renderizando grafico...');

                        // USAR newPlot em vez de react para garantir renderizacao limpa
                        Plotly.newPlot('script-distribution-pie', [trace], layout, {responsive: true})
                            .then(() => {
                                console.log('Grafico de pizza renderizado com sucesso');
                                // Forca un resize após renderizacao
                                setTimeout(() => {
                                    Plotly.Plots.resize('script-distribution-pie');
                                }, 100);
                            })
                            .catch(err => {
                                console.error('Erro ao renderizar grafico:', err);
                                // Em caso de erro, mostra mensagem
                                const errorLayout = {
                                    title: 'Erro ao carregar grafico',
                                    height: 500,
                                    annotations: [{
                                        text: 'Erro ao carregar o grafico de distribuicao',
                                        showarrow: false,
                                        x: 0.5,
                                        y: 0.5,
                                        font: { size: 16, color: 'red' }
                                    }],
                                    xaxis: { visible: false },
                                    yaxis: { visible: false }
                                };
                                Plotly.newPlot('script-distribution-pie', [], errorLayout);
                            });
                    }

                    // ====================================================
                    // FUNCÕES PARA ESTATISTICAS DE SCRIPT + FUNCAO
                    // ====================================================

                    // Funcao para atualizar estatisticas de script + funcao
                    function updateScriptFuncaoStats(data) {
                        console.log('Iniciando updateScriptFuncaoStats com', data.length, 'registros');

                        // Agrupa dados por script e funcao
                        const scriptFuncaoCounts = {};

                        data.forEach(log => {
                            const key = log.Script + '::' + log.Funcao;
                            if (!scriptFuncaoCounts[key]) {
                                scriptFuncaoCounts[key] = {
                                    Script: log.Script,
                                    Funcao: log.Funcao,
                                    Total: 0,
                                    Principal: 0,
                                    Subchamada: 0,
                                    Ultima_Execucao: null
                                };
                            }

                            scriptFuncaoCounts[key].Total++;

                            if (log.Tipo === 'Principal') {
                                scriptFuncaoCounts[key].Principal++;
                            } else {
                                scriptFuncaoCounts[key].Subchamada++;
                            }

                            const logDate = new Date(log.Timestamp);
                            if (!scriptFuncaoCounts[key].Ultima_Execucao || logDate > scriptFuncaoCounts[key].Ultima_Execucao) {
                                scriptFuncaoCounts[key].Ultima_Execucao = logDate;
                            }
                        });

                        // Converte para array
                        let scriptFuncaoArray = Object.values(scriptFuncaoCounts);

                        console.log('Combinacoes script-funcao processadas:', scriptFuncaoArray.length);

                        // Ordena por total (decrescente)
                        scriptFuncaoArray.sort((a, b) => b.Total - a.Total);

                        // Top 10 combinacoes mais usadas
                        const top10 = scriptFuncaoArray.slice(0, 10);

                        // Top 10 combinacoes menos usadas (apenas as que tem execucoes)
                        const bottom10 = scriptFuncaoArray
                            .filter(item => item.Total > 0)
                            .sort((a, b) => a.Total - b.Total)
                            .slice(0, 10);

                        console.log('Combinacoes mais usadas:', top10.length);
                        console.log('Combinacoes menos usadas:', bottom10.length);

                        // Atualiza tabelas
                        updateScriptFuncaoTable('top-script-funcao-table', top10);
                        updateScriptFuncaoTable('bottom-script-funcao-table', bottom10);

                        // Atualiza grafico de pizza
                        updateScriptFuncaoPieChart(scriptFuncaoArray);
                    }

                    // Atualiza a tabela de script + funcao
                    function updateScriptFuncaoTable(tableId, data) {
                        console.log('Atualizando tabela:', tableId, 'com', data.length, 'registros');

                        const table = document.getElementById(tableId);
                        if (!table) {
                            console.error('Tabela nao encontrada:', tableId);
                            return;
                        }

                        const tbody = table.getElementsByTagName('tbody')[0];
                        if (!tbody) {
                            console.error('tbody nao encontrado na tabela:', tableId);
                            return;
                        }

                        tbody.innerHTML = '';

                        data.forEach((item, index) => {
                            console.log(`Adicionando linha ${index}:`, item.Script, item.Funcao, item.Total);

                            const row = document.createElement('tr');

                            const scriptCell = document.createElement('td');
                            scriptCell.textContent = item.Script;
                            row.appendChild(scriptCell);

                            const funcaoCell = document.createElement('td');
                            funcaoCell.textContent = item.Funcao;
                            row.appendChild(funcaoCell);

                            const totalCell = document.createElement('td');
                            totalCell.textContent = item.Total;
                            row.appendChild(totalCell);

                            const principalCell = document.createElement('td');
                            principalCell.textContent = item.Principal;
                            row.appendChild(principalCell);

                            const subchamadaCell = document.createElement('td');
                            subchamadaCell.textContent = item.Subchamada;
                            row.appendChild(subchamadaCell);

                            const lastCell = document.createElement('td');
                            lastCell.textContent = item.Ultima_Execucao ?
                                new Date(item.Ultima_Execucao).toLocaleString() : 'Nunca';
                            row.appendChild(lastCell);

                            tbody.appendChild(row);
                        });

                        console.log('Tabela atualizada com sucesso:', tableId);
                    }

                    // Atualiza o grafico de pizza de distribuicao de script + funcao
                    function updateScriptFuncaoPieChart(data) {
                        console.log('Criando grafico de pizza com', data.length, 'combinacoes script-funcao');

                        // Filtra apenas combinacoes que foram usadas
                        const usedData = data.filter(item => item.Total > 0);
                        console.log('Combinacoes usadas:', usedData.length);

                        if (usedData.length === 0) {
                            // Se nao ha combinacoes usadas, mostra uma mensagem
                            const layout = {
                                title: 'Distribuicao de Uso por Script e Funcao',
                                height: 500,
                                annotations: [{
                                    text: 'Nenhuma combinacao script-funcao foi executada no periodo',
                                    showarrow: false,
                                    x: 0.5,
                                    y: 0.5,
                                    font: { size: 16 }
                                }],
                                xaxis: { visible: false },
                                yaxis: { visible: false }
                            };
                            Plotly.react('script-funcao-distribution-pie', [], layout);
                            return;
                        }

                        // Ordena por uso (decrescente)
                        const sortedData = usedData.sort((a, b) => b.Total - a.Total);

                        // Prepara dados para o grafico de pizza
                        const top10 = sortedData.slice(0, 10);
                        const outros = sortedData.slice(10);
                        const outrosTotal = outros.reduce((sum, item) => sum + item.Total, 0);

                        let labels = top10.map(item => item.Script + '::' + item.Funcao);
                        let values = top10.map(item => item.Total);

                        if (outrosTotal > 0) {
                            labels.push('Outros');
                            values.push(outrosTotal);
                        }

                        console.log('Labels:', labels);
                        console.log('Values:', values);

                        const trace = {
                            labels: labels,
                            values: values,
                            type: 'pie',
                            hole: 0.4,
                            textinfo: 'label+percent',
                            textposition: 'auto',
                            hovertemplate: '<b>%{label}</b><br>Execucoes: %{value}<br>Percentual: %{percent}<extra></extra>',
                            marker: {
                                colors: [
                                    '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                                    '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf',
                                    '#aec7e8'
                                ],
                                line: {
                                    color: '#ffffff',
                                    width: 2
                                }
                            }
                        };

                        const layout = {
                            title: {
                                text: 'Distribuicao de Uso por Script and Funcao',
                                font: { size: 16 }
                            },
                            height: 500,
                            margin: { t: 60, b: 50, l: 50, r: 120 },
                            showlegend: true,
                            legend: {
                                orientation: 'v',
                                x: 1.02,
                                y: 0.5,
                                font: { size: 10 }
                            }
                        };

                        console.log('Renderizando grafico...');

                        Plotly.newPlot('script-funcao-distribution-pie', [trace], layout, {responsive: true})
                            .then(() => {
                                console.log('Grafico de pizza renderizado com sucesso');
                                setTimeout(() => {
                                    Plotly.Plots.resize('script-funcao-distribution-pie');
                                }, 100);
                            })
                            .catch(err => {
                                console.error('Erro ao renderizar grafico:', err);
                                const errorLayout = {
                                    title: 'Erro ao carregar grafico',
                                    height: 500,
                                    annotations: [{
                                        text: 'Erro ao carregar o grafico de distribuicao',
                                        showarrow: false,
                                        x: 0.5,
                                        y: 0.5,
                                        font: { size: 16, color: 'red' }
                                    }],
                                    xaxis: { visible: false },
                                    yaxis: { visible: false }
                                };
                                Plotly.newPlot('script-funcao-distribution-pie', [], errorLayout);
                            });
                    }

                    // ====================================================
                    // INICIALIZACAO
                    // ====================================================

                    // Inicializar ao carregar a pagina
                    document.addEventListener('DOMContentLoaded', function() {
                        // Configurar datas iniciais
                        document.getElementById('start-date').value = '2025-08-04';
                        document.getElementById('end-date').value = '2025-08-27';

                        // Inicializar graficos com dados completos
                        aplicarFiltro();
                    });
                </script>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Relatorio de Uso de Scripts</h1>
                        <div class="info-card">
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <h3>Total Execucoes</h3>
                                    <p style="font-size: 24px; font-weight: bold;" id="total-execucoes">1495</p>
                                </div>
                                <div class="stat-card">
                                    <h3>Execucoes Principais</h3>
                                    <p style="font-size: 24px; font-weight: bold;" id="total-principal">577</p>
                                </div>
                                <div class="stat-card">
                                    <h3>Subchamadas</h3>
                                    <p style="font-size: 24px; font-weight: bold;" id="total-subchamada">918</p>
                                </div>
                                <div class="stat-card">
                                    <h3>Usuarios Unicos</h3>
                                    <p style="font-size: 24px; font-weight: bold;" id="usuarios-unicos">3</p>
                                </div>
                            </div>
                            <p>Periodo: 2025-08-04 - 2025-08-27 |
                               Ultima atualizacao: 2025-08-27 13:00:07</p>
                        </div>
                    </div>

                    <div class="filtro-data">
                        <h3>Filtro por Data</h3>
                        <div class="date-inputs">
                            <div>
                                <label for="start-date">Data Inicial:</label>
                                <input type="date" id="start-date">
                            </div>
                            <div>
                                <label for="end-date">Data Final:</label>
                                <input type="date" id="end-date">
                            </div>
                            <button onclick="aplicarFiltro()">Aplicar Filtro</button>
                            <button onclick="resetarFiltro()">Resetar</button>
                        </div>
                    </div>

                    <!-- Sistema de abas -->
                    <div class="tabs">
                        <div class="tab active" onclick="openTab(event, 'tab-ultimas-execucoes')">Ultimas Execucoes</div>
                        <div class="tab" onclick="openTab(event, 'tab-scripts')">Scripts</div>
                        <div class="tab" onclick="openTab(event, 'tab-script-funcao')">Script + Funcao</div>
                        <div class="tab" onclick="openTab(event, 'tab-geral')">Estatisticas Gerais</div>
                        <div class="tab" onclick="openTab(event, 'tab-usuario')">Estatisticas por Usuario</div>
                        <div class="tab" onclick="openTab(event, 'tab-principal')">Execucoes Principais</div>
                        <div class="tab" onclick="openTab(event, 'tab-subchamada')">Subchamadas</div>
                    </div>

                    <!-- Conteúdo das abas -->
                    <div id="tab-ultimas-execucoes" class="tab-content active">
                        <div class="table-container">
                            <h2>Ultimas Execucoes</h2>
                            <table border="1" class="dataframe table table-striped">
  <thead>
    <tr style="text-align: right;">
      <th>Script</th>
      <th>Tipo</th>
      <th>Ultima Execucao</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>ajustadorPN.rev2.py</td>
      <td>Principal</td>
      <td>2025-08-27 10:21:40</td>
    </tr>
    <tr>
      <td>cmg_out_helper.py</td>
      <td>Subchamada</td>
      <td>2025-08-27 09:43:38</td>
    </tr>
    <tr>
      <td>ensemble.py</td>
      <td>Principal</td>
      <td>2025-08-27 09:45:19</td>
    </tr>
    <tr>
      <td>ensemble_report.py</td>
      <td>Subchamada</td>
      <td>2025-08-27 09:43:37</td>
    </tr>
    <tr>
      <td>limitesPN.py</td>
      <td>Principal</td>
      <td>2025-08-08 11:29:26</td>
    </tr>
    <tr>
      <td>limitesPN.py</td>
      <td>Subchamada</td>
      <td>2025-08-27 10:04:29</td>
    </tr>
    <tr>
      <td>processar_PO.py</td>
      <td>Principal</td>
      <td>2025-08-27 09:58:21</td>
    </tr>
    <tr>
      <td>teste1.py</td>
      <td>Principal</td>
      <td>2025-08-25 14:56:32</td>
    </tr>
    <tr>
      <td>teste1.py</td>
      <td>Subchamada</td>
      <td>2025-08-07 10:16:52</td>
    </tr>
    <tr>
      <td>teste2.py</td>
      <td>Principal</td>
      <td>2025-08-06 11:19:31</td>
    </tr>
    <tr>
      <td>teste2.py</td>
      <td>Subchamada</td>
      <td>2025-08-25 14:56:32</td>
    </tr>
    <tr>
      <td>teste3.py</td>
      <td>Principal</td>
      <td>2025-08-07 10:16:52</td>
    </tr>
  </tbody>
</table>
                        </div>
                    </div>

                    <div id="tab-scripts" class="tab-content">
                        <div class="script-stats">
                            <h2>Estatisticas por Script</h2>

                            <div class="script-grid">
                                <div>
                                    <h3>Top 10 Scripts Mais Utilizados</h3>
                                    <div class="table-container">
                                        <table class="table table-striped" id="most-used-table">
                                            <thead>
                                                <tr>
                                                    <th>Script</th>
                                                    <th>Total</th>
                                                    <th>Principal</th>
                                                    <th>Subchamada</th>
                                                    <th>Ultima Execucao</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div>
                                    <h3>Top 10 Scripts Menos Utilizados</h3>
                                    <div class="table-container">
                                        <table class="table table-striped" id="least-used-table">
                                            <thead>
                                                <tr>
                                                    <th>Script</th>
                                                    <th>Total</th>
                                                    <th>Principal</th>
                                                    <th>Subchamada</th>
                                                    <th>Ultima Execucao</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="chart full-width" id="script-distribution-pie"></div>
                        </div>
                    </div>

                    <div id="tab-script-funcao" class="tab-content">
                        <div class="script-funcao-stats">
                            <h2>Estatisticas por Script e Funcao</h2>

                            <div class="script-funcao-grid">
                                <div>
                                    <h3>Top 10 Combinacoes (Script + Funcao) Mais Utilizadas</h3>
                                    <div class="table-container">
                                        <table class="table table-striped" id="top-script-funcao-table">
                                            <thead>
                                                <tr>
                                                    <th>Script</th>
                                                    <th>Funcao</th>
                                                    <th>Total</th>
                                                    <th>Principal</th>
                                                    <th>Subchamada</th>
                                                    <th>Ultima Execucao</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div>
                                    <h3>Top 10 Combinacoes (Script + Funcao) Menos Utilizadas</h3>
                                    <div class="table-container">
                                        <table class="table table-striped" id="bottom-script-funcao-table">
                                            <thead>
                                                <tr>
                                                    <th>Script</th>
                                                    <th>Funcao</th>
                                                    <th>Total</th>
                                                    <th>Principal</th>
                                                    <th>Subchamada</th>
                                                    <th>Ultima Execucao</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="chart full-width" id="script-funcao-distribution-pie"></div>
                        </div>
                    </div>

                    <div id="tab-geral" class="tab-content">
                        <h2>Estatisticas Gerais</h2>
                        <div class="grid">
                            <div class="chart" id="grafico-geral-dia"></div>
                            <div class="chart" id="grafico-geral-pizza"></div>
                            <div class="chart" id="grafico-geral-mes"></div>
                            <div class"chart" id="grafico-geral-hora"></div>
                            <div class="chart full-width" id="grafico-evolucao-tipo"></div>
                        </div>
                    </div>

                    <div id="tab-usuario" class="tab-content">
                        <h2>Estatisticas por Usuario</h2>
                        <div class="grid">
                            <div class="chart" id="grafico-usuario-barras"></div>
                            <div class="chart" id="grafico-usuario-pizza"></div>
                            <div class="chart full-width" id="grafico-evolucao-usuario"></div>
                        </div>
                    </div>

                    <div id="tab-principal" class="tab-content">
                        <h2>Execucoes Principais</h2>
                        <div class="grid">
                            <div class="chart" id="grafico-principal-dia"></div>
                            <div class="chart" id="grafico-principal-mes"></div>
                        </div>
                    </div>

                    <div id="tab-subchamada" class="tab-content">
                        <h2>Subchamadas</h2>
                        <div class="grid">
                            <div class="chart" id="grafico-subchamada-dia"></div>
                            <div class="chart" id="grafico-subchamada-mes"></div>
                        </div>
                    </div>
                </div>
            </body>
        </html>
        