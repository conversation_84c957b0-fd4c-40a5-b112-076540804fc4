import os
import re
import json
import plotly.graph_objs as go
from datetime import datetime
from collections import defaultdict
from ensemble_report import (
    read_wcoord_file,
)
from Auxiliares.modules.logging_module import log_execution

@log_execution
def extrair_hierarquia(filepath_in):
    filepath = os.path.splitext(filepath_in)[0] + ".out"
    with open(filepath, 'r') as file:
        texto_extraido = {}
        coletando = False
        buffer = []

        for linha in file:
            linha = linha.strip('\n')  # Remove a quebra de linha

            # Detecta a linha chave
            if linha == 'The current group-well hierarchy is:':
                coletando = True
                buffer.clear()  # Limpa o buffer de texto armazenado anteriormente
                continue

            # Inicia a coleta de texto após a linha chave
            if coletando:
                if linha == ' ':  # Linha contendo apenas espaço
                    coletando = False  # Finaliza a coleta do bloco
                else:
                    buffer.append(linha)
                    continue

            # Após o bloco, busca a linha que contém o padrão de data
            if not coletando and buffer:
                # print(f"{coletando}{len(buffer)}: {linha}")
                # input()
                # Tenta encontrar o padrão da data no formato YYYY.MM.DD
                match = re.search(r'\s+(\d{4}\.\d{2}\.\d{2})\s+', linha)
                match2 = re.search(r'\s+(\d{4}:\d{2}:\d{2})\s+', linha)
                if match:
                    chave = match.group(1)  # Extrai a data encontrada
                    texto_bloco = '\n'.join(buffer)
                    texto_extraido[chave] = texto_bloco  # Salva o bloco com a data como chave
                    buffer.clear()  # Limpa o buffer após salvar o bloco
                elif match2:
                    chave = match2.group(1).replace(":",".")  # Extrai a data encontrada
                    texto_bloco = '\n'.join(buffer)
                    texto_extraido[chave] = texto_bloco  # Salva o bloco com a data como chave
                    buffer.clear()  # Limpa o buffer após salvar o bloco
                # else:
                #     texto_extraido[f"sem_data_{len(texto_extraido) + 1}"] = '\n'.join(buffer)

                # buffer.clear()  # Limpa o buffer após salvar o bloco

    return texto_extraido

@log_execution
def processar_bloco(texto):
    """
    Retorna um dicionário onde cada chave é o nome de uma entidade,
    e o valor é a lista das entidades-filhas conectadas a ela.
    """
    conexoes = defaultdict(list)
    pilha_entidades = []

    for linha in texto.split('\n'):
        # Remove apenas \r e \n do final, mantendo possíveis pipes/spaces no início
        linha = linha.rstrip('\r').rstrip('\n')

        # Ignora linhas totalmente vazias
        if not linha.strip():
            continue

        # Procura '+--' em qualquer lugar da linha
        busca = re.search(r'\+--\s*(.*)$', linha)
        if busca:
            # Posição absoluta onde '+--' começa
            pos = busca.start()

            # Conteúdo que vem depois de '+--'
            conteudo = busca.group(1).strip()

            # Extrai todos os itens entre aspas simples
            itens_entre_aspas = re.findall(r"'([^']+)'", conteudo)

            # "Sobras" fora das aspas (se existir algo, é o nome principal da entidade)
            # Remove o que já foi capturado entre aspas, deixando o que estiver fora
            conteudo_sem_aspas = re.sub(r"'[^']+'", '', conteudo).strip()

            # Define o nível de hierarquia
            nivel = pos // 4

            # Ajusta a pilha caso a linha atual seja menos/mais profunda
            while len(pilha_entidades) > nivel:
                pilha_entidades.pop()

            # Se houve algum texto fora das aspas, consideramos como a entidade "principal" da linha
            if conteudo_sem_aspas:
                entidade_atual = conteudo_sem_aspas

                # Se existir um pai na pilha, ligamos essa entidade a ele
                if pilha_entidades:
                    entidade_pai = pilha_entidades[-1]
                    conexoes[entidade_pai].append(entidade_atual)

                # Agora empilhamos essa nova entidade
                pilha_entidades.append(entidade_atual)

                # Caso também existam itens entre aspas, eles são filhos dessa entidade_atual
                for item in itens_entre_aspas:
                    conexoes[entidade_atual].append(item)

            else:
                # Não sobrou texto fora das aspas, ou seja,
                # a linha é apenas algo como: +--'A' 'B' 'C'
                # Todos esses itens são filhos diretos do pai atual (se existir)
                if pilha_entidades:
                    entidade_pai = pilha_entidades[-1]
                    for item in itens_entre_aspas:
                        conexoes[entidade_pai].append(item)
                else:
                    # Se não há pai na pilha, você pode decidir
                    # criar uma raiz "oculta" ou simplesmente ignorar
                    pass

    return conexoes

@log_execution
def imprimir_conexoes(conexoes):
    """
    Imprime as conexões em um formato simples:
    Entidade está interligada com:
      - Filho1
      - Filho2
      ...
    """
    if not conexoes:
        print("Nenhuma conexão encontrada.")
        return

    for entidade, filhos in conexoes.items():
        print(f"{entidade} está interligada com:")
        for filho in filhos:
            print(f"  - {filho}")
        print()

@log_execution
def carregar_coordenadas(dir_path, uep_config):
    """
    Lê poços via read_wcoord_file + adiciona manualmente as UEPs.
    Para cada entidade, pode haver:
      - 'icon': None
      - 'icon': 'diamond-tall'   (símbolo do Plotly)
      - 'icon': r'C:/path/to/file.png' (imagem)
    """
    df_wells = read_wcoord_file(dir_path)  # supõe colunas: ['Poço','x','y']

    coords = {}
    for idx, row in df_wells.iterrows():
        nome_poco = row["Poço"]
        coords[nome_poco] = {
            'x': row["x"],
            'y': row["y"],
            'icon': None,
            'type': None
        }

    # Mescla UEPs
    for nome_uep, dados in uep_config.items():
        coords[nome_uep] = {
            'x':   dados['x'],
            'y':   dados['y'],
            'icon': dados.get('icon', None),
            'type': dados.get('type', None)
        }

    return coords

@log_execution
def flatten_children(parent, conexoes, coords):
    """
    Retorna a lista final de filhos que TÊM coordenadas,
    achatando qualquer filho que não tenha coords.
    """
    filhos_ajustados = []
    for filho in conexoes.get(parent, []):
        if filho in coords:
            # Filho possui coordenadas -> mantemos
            filhos_ajustados.append(filho)
        else:
            # Filho não tem coord -> buscar "netos"
            # e conectá-los diretamente ao pai
            netos = flatten_children(filho, conexoes, coords)
            filhos_ajustados.extend(netos)
    return filhos_ajustados


@log_execution

def filtrar_por_uep(uep_poco, uep_config):
    """
    Para cada data em `uep_poco`, colapsa os pais que não são UEP,
    subindo seus filhos até encontrarmos um pai UEP ou 'FIELD'.

    Exemplo de entrada (por data):
      {
        "2025.01.01": {
          "ManifoldX": [("WELL-10","PRO"), ("WELL-12","INJ")],
          "UEP1":      [("WELL-11","OUTRO")]
        },
        "2025.02.01": {
          "ManifoldY": [("WELL-10","PRO")],
          ...
        }
      }

    Retorna um novo dicionário no mesmo formato, porém somente com UEP ou 'FIELD'
    como pais:
      {
        "2025.01.01": {
          "UEP1": [("WELL-11","OUTRO"), ("WELL-10","PRO"), ("WELL-12","INJ")]
        },
        "2025.02.01": {
          "FIELD": [("WELL-10","PRO")]
        }
      }
    """
    new_uep_poco = {}

    for date_key, day_dict in uep_poco.items():
        # 'day_dict' é algo como: { father: [(child1,...)], father2: [...] }
        collapsed = colapsar_pais_para_uep_ou_field(day_dict, uep_config)

        # Se não quiser guardar entradas vazias (ex: "FIELD": []?), pode filtrar
        # Se preferir manter, basta deixar como está:
        # Filtra apenas chaves cujos filhos não estejam vazios:
        collapsed_filtered = {}
        for father, filhos in collapsed.items():
            if len(filhos) > 0:
                collapsed_filtered[father] = filhos

        new_uep_poco[date_key] = collapsed_filtered

    return new_uep_poco

@log_execution
def colapsar_pais_para_uep_ou_field(uep_dict_dia, uep_config):
    """
    'Colapsa' pais que não sejam UEP, subindo seus filhos até encontrarmos
    um pai que seja do tipo 'UEP' no uep_config ou então 'FIELD' (se chegarmos ao topo).

    Parâmetros:
      - uep_dict_dia (dict): ex:
          {
             "ManifoldX": [(wellA, 'PRO'), (wellB, 'INJ')],
             "UEP1":      [(wellC, 'OUTRO')],
             ...
          }
      - uep_config (dict): ex:
          {
             "UEP1": {"type":"UEP", ...},
             "ManifoldX": {"type":"Manifold", ...},
             ...
          }

    Retorno:
      - new_dict (dict): mesmo formato, porém apenas com chaves UEP ou 'FIELD'.
        ex:
          {
             "UEP1": [(wellC, 'OUTRO'), (wellA,'PRO'), (wellB,'INJ')],
             "FIELD": [...],
          }
    """

    # 1) Construir father_of[child] = father
    #    Observação: cada pai pode ter vários filhos, mas assumimos que cada "filho" tem só 1 pai
    #    (não há duplicidade de paternidade).
    father_of = {}
    for father, list_of_children in uep_dict_dia.items():
        for (child, classification) in list_of_children:
            father_of[child] = father

    # 2) Função recursiva que encontra o 'pai definitivo' de uma entidade
    #    Se em uep_config for UEP => retorna a própria entidade
    #    Se não existir em father_of => top-level => se for UEP => ela mesma; senão => "FIELD"
    @log_execution
    def get_uep_or_field(entity):
        # Se 'entity' não tem pai, está no topo => Checar se é UEP
        if entity not in father_of:
            if (entity in uep_config) and (uep_config[entity].get("type","").upper() == "UEP"):
                return entity
            else:
                return "FIELD"

        # Se tem pai:
        father = father_of[entity]
        # Se esse pai é UEP, retornamos o pai
        if (father in uep_config) and (uep_config[father].get("type","").upper() == "UEP"):
            return father
        else:
            # pai não é UEP => subimos mais um nível
            return get_uep_or_field(father)

    # 3) Montar dicionário final
    new_dict = defaultdict(list)

    # Para cada 'pai' original e seus filhos
    for father, list_of_children in uep_dict_dia.items():
        # Descobrimos quem é o "pai definitivo"
        ultimate_father = get_uep_or_field(father)
        # Adicionamos todos os filhos a esse 'ultimate_father'
        for (child, classification) in list_of_children:
            new_dict[ultimate_father].append( (child, classification) )

    # Retorna como dict normal
    return dict(new_dict)

@log_execution
def filtrar_conexoes_por_coordenadas(conexoes, coords):
    """
    Devolve novo dicionário de conexões apenas com nós que existem em coords.
    Entidades sem coords são 'puladas' e seus filhos vão direto para o avô.
    """
    from collections import defaultdict
    new_conexoes = defaultdict(list)

    # Percorre cada "pai" do dicionário original
    for parent in conexoes:
        if parent in coords:
            # "Achatamos" os filhos
            filhos_validos = flatten_children(parent, conexoes, coords)
            if filhos_validos:
                new_conexoes[parent].extend(filhos_validos)
    return dict(new_conexoes)

@log_execution
def calcular_bounding_box(coords, folga_perc=0.05):
    """
    Recebe o dicionário coords {nome: {'x':..., 'y':..., 'icon':...}}
    e retorna (minX, maxX, minY, maxY).
    """
    xs = [info['x'] for info in coords.values()]
    ys = [info['y'] for info in coords.values()]
    min_x, max_x = min(xs), max(xs)
    min_y, max_y = min(ys), max(ys)

    dx = max_x - min_x
    dy = max_y - min_y

    dx = dx + dx*folga_perc*2
    dy = dy + dy*folga_perc*2

    # Centro
    cx = (min_x + max_x) / 2
    cy = (min_y + max_y) / 2

    x_range = [cx - dx/2, cx + dx/2]
    y_range = [cy - dy/2, cy + dy/2]

    return x_range, y_range

@log_execution
def bounding_box_quadrada(coords, folga_perc=0.05):
    """
    Retorna x_range e y_range para plot
    de modo que fique um quadrado contendo todos os pontos.
    folga_perc é a fração extra, ex: 0.05 => 5% de borda.
    """
    xs = [info['x'] for info in coords.values()]
    ys = [info['y'] for info in coords.values()]
    min_x, max_x = min(xs), max(xs)
    min_y, max_y = min(ys), max(ys)

    dx = max_x - min_x
    dy = max_y - min_y
    d = max(dx, dy)

    # Se quiser folga
    margem = d * folga_perc
    d_com_folga = d + 2*margem  # folga em ambos os lados

    # Centro
    cx = (min_x + max_x) / 2
    cy = (min_y + max_y) / 2

    # Faixas
    x_range = [cx - d_com_folga/2, cx + d_com_folga/2]
    y_range = [cy - d_com_folga/2, cy + d_com_folga/2]
    return x_range, y_range

@log_execution
def plotar_conexoes_no_mapa_mix(conexoes_filtradas, coords, pai_de_resultado):
    """
    Plota o grafo de conexões no mapa em Plotly, usando coordenadas 'coords'
    e o dicionário pai_de_resultado para definir a cor dos nós.
    """
    import os
    import plotly.graph_objs as go
    from collections import defaultdict

    # Lista de símbolos conhecidos do Plotly (simplificada).
    PLOTLY_SYMBOLS = {
        'circle', 'square', 'diamond', 'diamond-tall', 'diamond-wide',
        'cross', 'x', 'triangle-up', 'triangle-down', 'triangle-left',
        'triangle-right', 'triangle-ne', 'triangle-se', 'triangle-sw',
        'triangle-nw', 'pentagon', 'hexagon', 'hexagon2', 'octagon',
        'star', 'hexagram', 'star-triangle-up', 'star-triangle-down',
        'star-square', 'star-diamond', 'hourglass', 'bowtie'
    }

    # 1) Monta as arestas
    edge_traces = []
    for pai, filhos in conexoes_filtradas.items():
        if pai not in coords:
            continue
        x0 = coords[pai]['x']
        y0 = coords[pai]['y']

        for filho in filhos:
            if filho not in coords:
                continue
            x1 = coords[filho]['x']
            y1 = coords[filho]['y']

            edge_traces.append(
                go.Scatter(
                    x=[x0, x1],
                    y=[y0, y1],
                    mode='lines',
                    line=dict(width=1, color='#888'),
                    hoverinfo='text',
                    hovertext=f"{pai} <- {filho}"
                )
            )

    # 2) Separar nós por tipo de ícone
    normal_nodes = {'x':[], 'y':[], 'hover':[], 'text':[], 'color':[]}
    symbol_nodes = defaultdict(lambda: {
        'x':[], 'y':[], 'hover':[], 'text':[], 'color':[], 'symbol_name':''
    })
    layout_images = []
    image_nodes = {'x':[], 'y':[], 'hover':[], 'text':[], 'color':[]}

    # Coleção de todos os nós (pais + filhos)
    all_nodes = set(conexoes_filtradas.keys())
    for lista_filhos in conexoes_filtradas.values():
        all_nodes.update(lista_filhos)

    for node in all_nodes:
        if node not in coords:
            continue

        x = coords[node]['x']
        y = coords[node]['y']
        icon_val = coords[node]['icon']

        # Texto de hover
        hover_txt = node
        if node in pai_de_resultado:
            hover_txt += f"<br>Pai: {pai_de_resultado[node]}"

        # Definir cor via ancestral
        node_color = get_node_color_ancestry(node, pai_de_resultado)

        # Verifica se 'icon_val' é caminho de arquivo (imagem),
        # símbolo do Plotly ou None (círculo normal).
        if isinstance(icon_val, str) and os.path.isfile(icon_val):
            # -> IMAGEM
            layout_images.append(dict(
                source=icon_val,
                x=x, y=y,
                xref="x", yref="y",
                xanchor="center", yanchor="middle",
                sizex=500, sizey=500,  # ajuste conforme sua escala
                sizing="stretch",
                opacity=1.0,
                layer="above"
            ))
            image_nodes['x'].append(x)
            image_nodes['y'].append(y)
            image_nodes['hover'].append(hover_txt)
            image_nodes['text'].append(node)
            image_nodes['color'].append(node_color)

        elif isinstance(icon_val, str) and icon_val.lower() in PLOTLY_SYMBOLS:
            # -> SÍMBOLO PLOTLY
            symbol_nodes[icon_val]['x'].append(x)
            symbol_nodes[icon_val]['y'].append(y)
            symbol_nodes[icon_val]['hover'].append(hover_txt)
            symbol_nodes[icon_val]['text'].append(node)
            symbol_nodes[icon_val]['color'].append(node_color)
            symbol_nodes[icon_val]['symbol_name'] = icon_val

        else:
            # -> CÍRCULO NORMAL
            normal_nodes['x'].append(x)
            normal_nodes['y'].append(y)
            normal_nodes['hover'].append(hover_txt)
            normal_nodes['text'].append(node)
            normal_nodes['color'].append(node_color)

    # 3) Construir os traces de nós
    fig_data = edge_traces  # começa com as arestas

    # a) Scatter normal
    if normal_nodes['x']:
        fig_data.append(
            go.Scatter(
                x=normal_nodes['x'],
                y=normal_nodes['y'],
                mode='markers+text',
                text=normal_nodes['text'],
                textposition='top center',
                hoverinfo='text',
                hovertext=normal_nodes['hover'],
                marker=dict(
                    symbol='circle',
                    size=10,
                    color=normal_nodes['color'],
                    line_width=1
                ),
                name='Normal (círculo)'
            )
        )

    # b) Scatter para cada símbolo
    for symb, data_dict in symbol_nodes.items():
        if not data_dict['x']:
            continue
        fig_data.append(
            go.Scatter(
                x=data_dict['x'],
                y=data_dict['y'],
                mode='markers+text',
                text=data_dict['text'],
                textposition='top center',
                hoverinfo='text',
                hovertext=data_dict['hover'],
                marker=dict(
                    symbol=symb,
                    size=12,
                    color=data_dict['color'],
                    line_width=1
                ),
                name=f"Símbolo {symb}"
            )
        )

    # c) Scatter "invisível" p/ nós com imagem (mas ainda mostra hover/text)
    if image_nodes['x']:
        fig_data.append(
            go.Scatter(
                x=image_nodes['x'],
                y=image_nodes['y'],
                mode='markers+text',
                text=image_nodes['text'],
                textposition='top center',
                hoverinfo='text',
                hovertext=image_nodes['hover'],
                marker=dict(
                    color=image_nodes['color'],
                    size=2,
                    line_width=0
                ),
                name='Imagem'
            )
        )

    # 4) Montar a figura final
    fig = go.Figure(
        data=fig_data,
        layout=go.Layout(
            title='Interligações',
            showlegend=False,
            hovermode='closest',
            margin=dict(b=20, l=5, r=5, t=40),
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=True),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=True),
        )
    )

    # Adiciona as imagens no layout
    for img_obj in layout_images:
        fig.add_layout_image(img_obj)

    # Ajuste de escala (defina aqui se quer quadrado ou retangular)
    # Exemplo: bounding_box_quadrada
    # x_range, y_range = bounding_box_quadrada(coords, folga_perc=0.05)
    from math import isclose
    x_range, y_range = calcular_bounding_box(coords, folga_perc=0.05)

    fig.update_xaxes(range=x_range, autorange=False,
                     scaleanchor="y", scaleratio=1)
    fig.update_yaxes(range=y_range, autorange=False)

    return fig

@log_execution
def classify_node_ancestry(node, pai_de_resultado):
    """
    Retorna 'PRO' se na cadeia de ancestrais do nó constar 'PRO',
    'INJ' se constar 'INJ', ou 'OUTRO' caso contrário.
    """
    ancestrais = get_ancestors(node, pai_de_resultado)
    txt_upper = " ".join(ancestrais).upper()

    if "PRO" in txt_upper:
        return "PRO"
    elif "INJ" in txt_upper:
        return "INJ"
    else:
        return "OUTRO"

@log_execution
def filtrar_conexoes_e_classificar(conexoes, coords, pai_de_resultado):
    """
    Combina filtrar_conexoes_por_coordenadas + adiciona a classificação
    de cada FILHO como 'PRO', 'INJ' ou 'OUTRO', de acordo com a cadeia
    de ancestrais no dicionário pai_de_resultado.

    Retorna um dicionário no formato:
        {
          pai1: [(filho1, 'PRO'), (filho2, 'INJ'), ...],
          pai2: ...
        }
    onde cada filho vem com um rótulo de classificação.
    """
    from collections import defaultdict

    # 1) Primeiro, filtra as conexões com a função original
    new_conexoes = filtrar_conexoes_por_coordenadas(conexoes, coords)

    # 2) Cria um dicionário novo, que irá armazenar
    #    (filho, classificacao) para cada pai
    conexoes_class = defaultdict(list)

    # 3) Para cada pai e sua lista de filhos válidos em new_conexoes
    for pai, filhos in new_conexoes.items():
        for filho in filhos:
            # Aplica a classificação
            classe = classify_node_ancestry(filho, pai_de_resultado)
            conexoes_class[pai].append((filho, classe))

    # 4) Retorna como dicionário normal
    return dict(conexoes_class)

@log_execution
def obter_conexoes_classificadas_por_data(arquivo):
    """
    Para cada data no dicionário `resultado`:
        - Processa o bloco de hierarquia.
        - Gera o dicionário pai_de_resultado (pai de cada nó).
        - Filtra as conexões com coordenadas válidas.
        - Classifica cada filho como 'PRO', 'INJ' ou 'OUTRO'.

    Retorna um dicionário:
        {
            '2024.10.01': {pai1: [(filho1, 'PRO'), (filho2, 'INJ'), ...], ...},
            '2024.10.02': {...},
            ...
        }
    """
    resultado = extrair_hierarquia(arquivo)
    uep_config = ler_uep_config()
    coords = carregar_coordenadas(arquivo, uep_config)
    # print('arquivo')
    # print(arquivo)
    # print('resultado')
    # print(resultado)

    conexoes_por_data = {}

    for data_key, bloco_texto in resultado.items():
        # 1) Processa o bloco de texto para obter as conexões
        conexoes_resultado = processar_bloco(bloco_texto)
        # print('conexoes_resultado')
        # print(conexoes_resultado)

        # 2) Monta o dicionário de ancestrais (pai_de_resultado)
        pai_de_resultado = {}
        for pai, filhos in conexoes_resultado.items():
            for filho in filhos:
                pai_de_resultado[filho] = pai

        # 3) Filtra as conexões e classifica cada filho
        conexoes_classificadas = filtrar_conexoes_e_classificar(
            conexoes_resultado, coords, pai_de_resultado
        )
        # print('conexoes_classificadas')
        # print(conexoes_classificadas)

        # 4) Salva no dicionário usando a data como chave
        conexoes_por_data[data_key] = conexoes_classificadas

    return conexoes_por_data

from datetime import datetime

@log_execution
def encontrar_uep_conectada(conexoes_por_data, entidade, data_in):
    """
    Retorna a UEP à qual uma entidade está conectada em uma data específica.
    Se a data não existir no dicionário, usa a data imediatamente anterior.

    Parâmetros:
        - conexoes_por_data (dict): Saída da função `obter_conexoes_classificadas_por_data`.
        - entidade (str): Nome da entidade cujo caminho deve ser rastreado.
        - data_in (str ou datetime): Data no formato 'YYYY.MM.DD' ou objeto datetime.

    Retorno:
        - str: Nome da UEP conectada ou 'FIELD' se não encontrar um pai.
    """

    # 1) Converter data_in em datetime se vier como string
    if isinstance(data_in, str):
        try:
            data_busca = datetime.strptime(data_in, "%Y.%m.%d")
        except ValueError as e:
            print(f"Data inválida no formato 'YYYY.MM.DD': {data_in}")
            return None
    elif isinstance(data_in, datetime):
        data_busca = data_in
    else:
        print(f"Tipo de data_in inválido: {type(data_in)}. Deve ser str ou datetime.")
        return None

    # 2) Coletar todas as datas disponíveis no dicionário e converter em datetime
    datas_disponiveis = sorted([datetime.strptime(d, "%Y.%m.%d") for d in conexoes_por_data.keys()])

    # 3) Buscar a data exata ou a imediatamente anterior
    data_selecionada = None
    for data in datas_disponiveis:
        if data <= data_busca:
            data_selecionada = data
        else:
            break

    if data_selecionada is None:
        print("Nenhuma data anterior encontrada.")
        return None

    # 4) Converte de volta para string para acessar o dicionário
    data_selecionada_str = data_selecionada.strftime("%Y.%m.%d")

    # 5) Função interna para encontrar o pai de uma entidade
    @log_execution
    def encontrar_pai(ent, conexoes):
        """Busca o pai de 'ent' no dicionário de conexões (conexoes = {pai: [(filho, classificacao), ...], ...})."""
        for pai, filhos in conexoes.items():
            for (filho, _) in filhos:
                if filho == ent:
                    return pai
        return None

    # 6) Itera para subir na hierarquia até não haver mais pai
    conexoes_do_dia = conexoes_por_data[data_selecionada_str]
    atual = entidade

    while True:
        pai = encontrar_pai(atual, conexoes_do_dia)
        if pai is None:
            # Quando não há mais pai, em vez de retornar 'atual',
            # retornamos "FIELD" conforme solicitado
            return "FIELD"
        atual = pai

@log_execution
def get_ancestors(node, pai_de_resultado):
    """
    Sobe a cadeia no dicionário pai_de_resultado, coletando todos os ancestrais.
    Retorna uma lista de nomes (ordem: do filho até o topo).
    """
    ancestors = []
    atual = node
    # Enquanto existir um 'pai' mapeado para 'atual', subimos um nível.
    while atual in pai_de_resultado:
        pai = pai_de_resultado[atual]
        ancestors.append(pai)
        atual = pai
    return ancestors

@log_execution
def get_node_color_ancestry(node, pai_de_resultado):
    """
    @log_execution
    Define a cor do nó a partir de sua cadeia de ancestrais no pai_de_resultado:
      - Se algum ancestral contiver "PRO" no nome, retorna "green".
      - Se algum ancestral contiver "INJ" no nome, retorna "blue".
      - Caso contrário, uma cor default.
    """
    ancestrais = get_ancestors(node, pai_de_resultado)
    txt_upper = " ".join(ancestrais).upper()

    if "PRO" in txt_upper:
        return "green"
    elif "INJ" in txt_upper:
        return "blue"
    else:
        return "#03A9F4"

@log_execution
def gerar_figuras_por_data(resultado, coords):
    """
    Exemplo que itera sobre o dicionário `resultado` (cada data),
    processa a hierarquia do bloco e plota.
    """
    lista_figuras = []
    for data_key, bloco_texto in resultado.items():
        # 1) Hierarquia bruta
        conexoes_resultado = processar_bloco(bloco_texto)

        # 2) Monta pai_de_resultado
        pai_de_resultado = {}
        for pai, filhos in conexoes_resultado.items():
            for filho in filhos:
                pai_de_resultado[filho] = pai

        # 3) Filtra (removendo nós sem coordenadas)
        conexoes_filtradas = filtrar_conexoes_por_coordenadas(conexoes_resultado, coords)

        # 4) Plota
        fig = plotar_conexoes_no_mapa_mix(conexoes_filtradas, coords, pai_de_resultado)
        fig.update_layout(title=f"Interligações em {data_key}")
        lista_figuras.append(fig)

    return lista_figuras

@log_execution
def gerar_html_navegavel(figures, x_range, y_range, output_html="figuras_navegacao.html"):
    # 1) Converte cada figura em dict, tal qual seu código atual
    figuras_dict = []
    for fig in figures:
        fig_dict = fig.to_dict()
        figuras_dict.append(fig_dict)
    figuras_json = json.dumps(figuras_dict)

    # 2) Converte x_range e y_range para JSON, ex: [390000, 398000]
    x_range_js = json.dumps(x_range)
    y_range_js = json.dumps(y_range)

    # 3) Gera o HTML, injetando as variáveis
    html_conteudo = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>Figuras com Navegação</title>
    <!-- Carrega Plotly pela CDN -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
</head>
<body style="font-family:Arial, sans-serif; margin:10px;">
    <div id="plotDiv" style="width:90vw; height:80vh;"></div>

    <div style="text-align: center;">
        <div style="margin-bottom: 8px;">
            <!-- Quatro botões de navegação -->
            <button id="btnFirst" style="margin-right:5px;">Home</button>
            <button id="btnPrev" style="margin-right:5px;">← Anterior</button>
            <button id="btnNext" style="margin-right:5px;">Próximo →</button>
            <button id="btnLast">End</button>
        </div>
        <p> Teclas para navegação: Home (Início), setas (← e →), End (Fim)
    </div>

    <script>
    // Array de figuras vindas do Python
    window.figures = {figuras_json};

    // Ranges globais, vindos do Python
    window.globalXrange = {x_range_js};
    window.globalYrange = {y_range_js};

    // Índice inicial
    let currentIndex = 0;

    // Função que exibe a figura de 'currentIndex'
    function showCurrentFigure() {{
        // 1) Remove completamente o plot anterior (purga)
        Plotly.purge('plotDiv');

        // 2) Carrega os dados da figura
        let fig = window.figures[currentIndex];
        let data = fig.data || [];
        let layout = fig.layout || {{}};
        let config = fig.config || {{ }};

        // 3) Garante que layout.xaxis e layout.yaxis existam
        if (!layout.xaxis) layout.xaxis = {{}};
        if (!layout.yaxis) layout.yaxis = {{}};

        // 4) Força sempre o mesmo range, sem autorange
        layout.xaxis.range = window.globalXrange;
        layout.xaxis.autorange = false;
        layout.xaxis.scaleanchor = "y";
        layout.xaxis.scaleratio = 1;

        layout.yaxis.range = window.globalYrange;
        layout.yaxis.autorange = false;

        // 5) Desenha a figura do zero, com config e layout forçados
        Plotly.newPlot('plotDiv', data, layout, config);
    }}

    // ------------- Funções para mudar currentIndex -------------
    function goFirst() {{
        currentIndex = 0;
        showCurrentFigure();
    }}
    function goLast() {{
        currentIndex = window.figures.length - 1;
        showCurrentFigure();
    }}
    function goPrev() {{
        currentIndex--;
        if (currentIndex < 0) {{
            currentIndex = window.figures.length - 1; // loop se quiser
        }}
        showCurrentFigure();
    }}
    function goNext() {{
        currentIndex++;
        if (currentIndex >= window.figures.length) {{
            currentIndex = 0; // loop se quiser
        }}
        showCurrentFigure();
    }}

    // ------------- Lida com as teclas do teclado -------------
    document.addEventListener('keydown', (event) => {{
        if (event.key === 'ArrowRight') {{
            goNext();
        }} else if (event.key === 'ArrowLeft') {{
            goPrev();
        }} else if (event.key === 'Home') {{
            goFirst();
        }} else if (event.key === 'End') {{
            goLast();
        }}
    }});

    // ------------- Lida com os cliques nos botões -------------
    document.getElementById('btnFirst').addEventListener('click', goFirst);
    document.getElementById('btnPrev').addEventListener('click', goPrev);
    document.getElementById('btnNext').addEventListener('click', goNext);
    document.getElementById('btnLast').addEventListener('click', goLast);

    // Exibe a primeira figura ao carregar
    showCurrentFigure();
    </script>
</body>
</html>
"""

    # 4) Escreve o HTML para arquivo
    with open(output_html, "w", encoding="utf-8") as f:
        f.write(html_conteudo)

    print(f"HTML gerado em:", output_html)

@log_execution
def ler_uep_config(filename="uep_config.json"):
    """
    Lê o arquivo de configuração das UEPs localizado no mesmo diretório do script.
    Retorna o dicionário com as coordenadas e ícones das UEPs.
    """
    # Obtém o diretório do script atual
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(script_dir, filename)

    # Lê o arquivo JSON
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            uep_config = json.load(file)
            return uep_config
    except FileNotFoundError:
        print(f"Erro: O arquivo '{filename}' não foi encontrado no diretório '{script_dir}'.")
        return {}
    except json.JSONDecodeError as e:
        print(f"Erro ao decodificar o arquivo JSON: {e}")
        return {}

# --------------------------
# Exemplo de uso
# --------------------------
if __name__ == "__main__":
    # Entradas
    arquivo = r'L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\02_PD_Cenario4v250210\Sim\run\ATTEMPT_0066\RMSv14_er03_geoeng_output02_001.out'
    out_path = r"L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\02_PD_Cenario4v250210\Docs\Auxiliar\interligacoes_ICSPB.html"
    uep_config = ler_uep_config()

    # 1) Lê o arquivo .out
    resultado = extrair_hierarquia(arquivo)

    # 2) Carrega coordenadas (poços) + UEPs
    dir_path = os.path.dirname(arquivo)
    coords = carregar_coordenadas(dir_path, uep_config)
    x_range, y_range = calcular_bounding_box(coords, folga_perc=0.05)

    # # 3) Para cada data no dicionário
    # for data_key, bloco_texto in resultado.items():
    # # for data_key, bloco_texto in [list(resultado.items())[-1]]:
    #     print(f"\n--- Processando bloco {data_key} ---")
    #     conexoes_resultado = processar_bloco(bloco_texto)

    #     # A) Montar pai_de_resultado => contém a hierarquia completa (sem filtro)
    #     pai_de_resultado = {}
    #     for pai, filhos in conexoes_resultado.items():
    #         for filho in filhos:
    #             pai_de_resultado[filho] = pai

    #     # B) Imprime as conexões originais
    #     imprimir_conexoes(conexoes_resultado)

    #     # C) Filtra para remover nós sem coords
    #     conexoes_filtradas = filtrar_conexoes_por_coordenadas(conexoes_resultado, coords)

    #     # D) Plota chamando a função que usa pai_de_resultado
    #     fig = plotar_conexoes_no_mapa_mix(conexoes_filtradas, coords, pai_de_resultado)
    #     fig.show()
    #     input()

    # 3) Gera as figuras para cada data
    figuras = gerar_figuras_por_data(resultado, coords)

    # 4) Cria o HTML final com navegação
    gerar_html_navegavel(figuras, x_range, y_range, output_html=out_path)
