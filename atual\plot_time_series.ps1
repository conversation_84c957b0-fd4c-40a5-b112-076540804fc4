#####################################################################################################################################
# Entrada
#####################################################################################################################################

# html de saida: Se for passado um caminho para um arquivo .html a ser criado alem do .html sera salvo esse proprio script que gera o .html
$out_directory = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\04_PN2630_SelectModel\Sim\Docs\Auxiliar"

# Salvar em html?
$out_html_path = "$out_directory\pocos_novos.html"
# $out_html_path = ""

# Salvar em excel?
# $out_excel_path = "$out_directory\teste.xlsx"
$out_excel_path = ""

# Definir diretorios de entrada
$dir_proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\04_PN2630_SelectModel\Sim\run\MP_IPB_PID1_PID2_EVPRO"
# $dir_base = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\04_PN2630_SelectModel\Sim\run\MP"
# $dir_base = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\01_PDi_Cenarios_250110\MP_IPB_PID2_PID1_EVPRO_PID4_ICSPB\run\BASE"
$dir_base = "None"

$p10_realizations = ""
$p50_realizations = ""
$p90_realizations = ""
$user_realizations = "014"

$dpercentile = "5.0"

# Definir entidades em ordem de importancia para definir realizacoes representativas
$percentile_metrics = @(
    "FIELD-PRO,Npe*,m3,-1"
    "IPB_PROD-PRO,Npe*,m3,-1"
    "P58_PROD-PRO,Npe*,m3,-1"
    # "ICSPB_PROD-PRO,Npe*,m3,-1"
    # "FIELD-PRO,Qo,m3/day,2032-06-01"
    # "FIELD-PRO,Qo,m3/day,2032-05-01"
)

# Definir quais figuras serao apresentadas
$plots = @(
    "FIELD-PRO,Npe*,m3"
    "IPB_PROD-PRO,Npe*,m3"
    "P58_PROD-PRO,Npe*,m3"
    "ICSPB_PROD-PRO,Npe*,m3"
    "FIELD-PRO,Npe,m3"
    "IPB_PROD-PRO,Npe,m3"
    "P58_PROD-PRO,Npe,m3"
    "ICSPB_PROD-PRO,Npe,m3"
    "FIELD-PRO,Qo,m3/day"
    "IPB_PROD-PRO,Qo,m3/day"
    "P58_PROD-PRO,Qo,m3/day"
    "ICSPB_PROD-PRO,Qo,m3/day"
    "IPB-P9,Npe,m3"
    "IPB-P9,Qo,m3/day"
    "IPB-P9,BSW,fraction"
    "IPB-P9,RGO,m3/m3"
    "IPB-P9,IP,m3/day/kgf/cm2"
    "IPB-P7,Npe,m3"
    "IPB-P7,Qo,m3/day"
    "IPB-P7,BSW,fraction"
    "IPB-P7,RGO,m3/m3"
    "IPB-P7,IP,m3/day/kgf/cm2"
    "JUBPS-P4,Npe,m3"
    "JUBPS-P4,Qo,m3/day"
    "JUBPS-P4,BSW,fraction"
    "JUBPS-P4,RGO,m3/m3"
    "JUBPS-P4,IP,m3/day/kgf/cm2"
    "7-JUB-38-ESS,Npe,m3"
    "7-JUB-38-ESS,Qo,m3/day"
    "7-JUB-38-ESS,BSW,fraction"
    "7-JUB-38-ESS,RGO,m3/m3"
    "7-JUB-38-ESS,IP,m3/day/kgf/cm2"
    "7-JUB-61D-ESS.INJ,Npe,m3"
    "7-JUB-61D-ESS.INJ,Qo,m3/day"
    "7-JUB-61D-ESS.INJ,BSW,fraction"
    "7-JUB-61D-ESS.INJ,RGO,m3/m3"
    "7-JUB-61D-ESS.INJ,IP,m3/day/kgf/cm2"
    "IPB-P4,Npe,m3"
    "IPB-P4,Qo,m3/day"
    "IPB-P4,BSW,fraction"
    "IPB-P4,RGO,m3/m3"
    "IPB-P4,IP,m3/day/kgf/cm2"
    "IPB-I2,Npe,m3"
    "IPB-I2,Qo,m3/day"
    "IPB-I2,BSW,fraction"
    "IPB-I2,RGO,m3/m3"
    "IPB-I2,IP,m3/day/kgf/cm2"
    "JUBPS-P5,Npe,m3"
    "JUBPS-P5,Qo,m3/day"
    "JUBPS-P5,BSW,fraction"
    "JUBPS-P5,RGO,m3/m3"
    "JUBPS-P5,IP,m3/day/kgf/cm2"
    "7-JUB-69-ESS_1,Npe,m3"
    "7-JUB-69-ESS_1,Qo,m3/day"
    "7-JUB-69-ESS_1,BSW,fraction"
    "7-JUB-69-ESS_1,RGO,m3/m3"
    "7-JUB-69-ESS_1,IP,m3/day/kgf/cm2"
    "IPB-I5,Npe,m3"
    "IPB-I5,Qo,m3/day"
    "IPB-I5,BSW,fraction"
    "IPB-I5,RGO,m3/m3"
    "IPB-I5,IP,m3/day/kgf/cm2"
    "IPB-P1,Npe,m3"
    "IPB-P1,Qo,m3/day"
    "IPB-P1,BSW,fraction"
    "IPB-P1,RGO,m3/m3"
    "IPB-P1,IP,m3/day/kgf/cm2"
    "8-JUB-50D-ESS,Npe,m3"
    "8-JUB-50D-ESS,Qo,m3/day"
    "8-JUB-50D-ESS,BSW,fraction"
    "8-JUB-50D-ESS,RGO,m3/m3"
    "8-JUB-50D-ESS,IP,m3/day/kgf/cm2"
    "8-JUB-47D-ESS,Npe,m3"
    "8-JUB-47D-ESS,Qo,m3/day"
    "8-JUB-47D-ESS,BSW,fraction"
    "8-JUB-47D-ESS,RGO,m3/m3"
    "8-JUB-47D-ESS,IP,m3/day/kgf/cm2"
    "7-BAZ-8-ESS,Npe,m3"
    "7-BAZ-8-ESS,Qo,m3/day"
    "7-BAZ-8-ESS,BSW,fraction"
    "7-BAZ-8-ESS,RGO,m3/m3"
    "7-BAZ-8-ESS,IP,m3/day/kgf/cm2"
    "IPB-I1,Npe,m3"
    "IPB-I1,Qo,m3/day"
    "IPB-I1,BSW,fraction"
    "IPB-I1,RGO,m3/m3"
    "IPB-I1,IP,m3/day/kgf/cm2"
    "IPB-P3,Npe,m3"
    "IPB-P3,Qo,m3/day"
    "IPB-P3,BSW,fraction"
    "IPB-P3,RGO,m3/m3"
    "IPB-P3,IP,m3/day/kgf/cm2"
    "7-JUB-49-ESS,Npe,m3"
    "7-JUB-49-ESS,Qo,m3/day"
    "7-JUB-49-ESS,BSW,fraction"
    "7-JUB-49-ESS,RGO,m3/m3"
    "7-JUB-49-ESS,IP,m3/day/kgf/cm2"
    "7-JUB-44-ESS,Npe,m3"
    "7-JUB-44-ESS,Qo,m3/day"
    "7-JUB-44-ESS,BSW,fraction"
    "7-JUB-44-ESS,RGO,m3/m3"
    "7-JUB-44-ESS,IP,m3/day/kgf/cm2"
    "IPB-I4.PRO,Npe,m3"
    "IPB-I4.PRO,Qo,m3/day"
    "IPB-I4.PRO,BSW,fraction"
    "IPB-I4.PRO,RGO,m3/m3"
    "IPB-I4.PRO,IP,m3/day/kgf/cm2"
)

#####################################################################################################################################
# Execucao
#####################################################################################################################################

# Definir diretorio do script dinamicamente
$script_dir = $PSScriptRoot # Diretorio do repositorio

# Combinar entidades em uma string separada por espacos
$percentile_metrics_array = @()
foreach ($percentile_metric in $percentile_metrics) {
    $percentile_metrics_array += $percentile_metric
}

$plots_array = @()
foreach ($plot in $plots) {
    $plots_array += $plot
}

# Definir variaveis
$python = "L:\res\campos\jubarte\er\er02\00_Programas\python\windows\python-3.12.6.amd64\python.exe"
$script = "$script_dir\ensemble.py"
$command = "plot_time_series"

$p10 = @()
if ($p10_realizations -ne "") {
    $p10 += "--p10"
    $p10 += $p10_realizations
}

$p50 = @()
if ($p50_realizations -ne "") {
    $p50 += "--p50"
    $p50 += $p50_realizations
}

$p90 = @()
if ($p90_realizations -ne "") {
    $p90 += "--p90"
    $p90 += $p90_realizations
}

$user = @()
if ($user_realizations -ne "") {
    $user += "--user"
    $user += $user_realizations
}

$dpercentile_value = @()
if ($dpercentile -ne "") {
    $dpercentile_value += "--dpercentile"
    $dpercentile_value += $dpercentile
}

$out_html_file = @()
if ($out_html_path -ne "") {
    $out_html_file += "--output_html"
    $out_html_file += $out_html_path
}

$out_excel_file = @()
if ($out_excel_path -ne "") {
    $out_excel_file += "--output_excel"
    $out_excel_file += $out_excel_path
}

# Comando a ser executado
Write-Output "$python $script $command $dir_proj $dir_base --metrics $percentile_metrics_array $p10 $p50 $p90 $user $dpercentile_value --time_series $plots_array $out_html_file $out_excel_file"

# Executando
& $python $script $command $dir_proj $dir_base --metrics $percentile_metrics_array $p10 $p50 $p90 $user $dpercentile_value --time_series $plots_array $out_html_file $out_excel_file

#####################################################################################################################################
# Salvando o script atual no diretório de saida
#####################################################################################################################################

if (($out_html_path -ne "") -or ($out_excel_path -ne "")) {
    # Determinar qual variável usar para o nome do arquivo
    $output_path = if ($out_html_path -ne "") { $out_html_path } else { $out_excel_path }

    # Extrair o caminho completo sem a extensão do arquivo
    $file_name_without_extension = [System.IO.Path]::GetFileNameWithoutExtension($output_path)
    $out_directory = Split-Path -Path $output_path -Parent
    $path_without_extension = Join-Path -Path $out_directory -ChildPath $file_name_without_extension

    # Caminho do novo script a ser salvo
    $script_output_path = "$path_without_extension.ps1"

    # Resto do código permanece o mesmo...
    $script_content = Get-Content -Path $MyInvocation.MyCommand.Path
    $script_content = $script_content -replace ' = \$PSScriptRoot # Diretorio do repositorio', " = `"$script_dir`" # Diretorio do repositorio"
    $script_content | Out-File -FilePath $script_output_path -Encoding UTF8

    Write-Output "O script foi salvo em: $script_output_path"
    Write-Output "Segue o comando para executar novamente:"
    Write-Output "powershell.exe -ExecutionPolicy Bypass -File $script_output_path"
}

# Aguarda uma tecla ao final
Write-Host "`nPressione qualquer tecla para finalizar..." -ForegroundColor Yellow
Read-Host