#####################################################################################################################################
# Entrada
#####################################################################################################################################

# Definir diretorios de entrada
$dir_proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\01_PDi_Cenarios_250110\RMSv13_er03_geoeng2_output01\run\ATTEMPT_0069"
$dir_base = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\01_PDi_Cenarios_250110\RMSv13_er03_geoeng2_output01\run\BASE"
#$dir_base = "None"

# Definir quais figuras serao apresentadas
$plots = @(
    "Npe,m3,23,P50"
    "Qo,m3/day,0,P50"
    "Qo,m3/day,2030-01-01,P50"
)

# html de saida: Se for passado um caminho para um arquivo .html a ser criado alem do .html sera salvo esse proprio script que gera o .html
$out_directory = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\01_PDi_Cenarios_250110\RMSv14_er03_geoeng_output02\Docs\Auxiliar"
# $out_path = ""
$out_path = "$out_directory\time_series_plotv2.html"

#####################################################################################################################################
# Execucao
#####################################################################################################################################

# Definir diretorio do script dinamicamente
$script_dir = $PSScriptRoot # Diretorio do repositorio

# Combinar entidades em uma string separada por espacos
$percentile_entities_string = $percentile_entities -join ","

$plots_array = @()
foreach ($plot in $plots) {
    $plots_array += $plot
}

# Definir variaveis
$python = "L:\res\campos\jubarte\er\er02\00_Programas\python\windows\python-3.12.6.amd64\python.exe"
$script = "$script_dir\ensemble.py"
$command = "plot_bubble_charts"

$html = @()
if ($out_path -ne "") {
    $html += "--output_html"
    $html += $out_path
}

# Comando a ser executado
Write-Output "$python $script $command $dir_proj $dir_base $plots_array $html"

# Executando
& $python $script $command $dir_proj $dir_base $plots_array $html

#####################################################################################################################################
# Salvando o script atual no diretorio de saida
#####################################################################################################################################

if ($out_path -ne "") {
    # Extrair o caminho completo sem a extensao do arquivo
    $file_name_without_extension = [System.IO.Path]::GetFileNameWithoutExtension($out_path)
    $out_directory = Split-Path -Path $out_path -Parent
    $path_without_extension = Join-Path -Path $out_directory -ChildPath $file_name_without_extension

    # Caminho do novo script a ser salvo
    $script_output_path = "$path_without_extension.ps1"

    # Obter o conteudo do script atual
    $script_content = Get-Content -Path $MyInvocation.MyCommand.Path

    # Substituir a variavel $script_dir pelo diretorio original
    $script_content = $script_content -replace ' = \$PSScriptRoot # Diretorio do repositorio', " = `"$script_dir`" # Diretorio do repositorio"

    # Salvar o conteudo no diretorio de saida
    $script_content | Out-File -FilePath $script_output_path -Encoding UTF8

    # Exibir mensagem de confirmacao
    Write-Output "O script foi salvo em: $script_output_path"

    Write-Output "Segue o comando para executar novamente:"
    Write-Output "powershell.exe -ExecutionPolicy Bypass -File $script_output_path"
}

# Aguarda uma tecla ao final
Write-Host "`nPressione qualquer tecla para finalizar..." -ForegroundColor Yellow
Read-Host