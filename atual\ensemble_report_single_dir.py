from ensemble_report import *
from Auxiliares.modules.logging_module import log_execution

# ---------------------------
# Main Function
# ---------------------------

@log_execution
def main():
    parser = argparse.ArgumentParser(description="Generate ensemble report")
    parser.add_argument("directory_path", help="Path to the directory containing the data files")
    parser.add_argument("--initial_positions_path", help="Path to the Excel file containing initial positions")
    args = parser.parse_args()

    directory_path = args.directory_path
    initial_positions_path = args.initial_positions_path

    base_path = None

    print("Starting processing...")
    print("Identifying new wells...")
    new_wells = find_new_wells(directory_path, base_path)
    if not new_wells:
        print("No new wells found.")
        return 1

    # Read the distance file
    print("Reading distances file...")
    all_distances = read_dist_file(directory_path)

    print("Reading data files...")
    dataframes = read_data_files(directory_path)

    entities = [
        "FIELD-PRO",
        "IPB_PROD-PRO",
        "P58_PROD-PRO",
        "ICSPB_PROD-PRO",
        "CDAN_PROD-PRO",
    ]
    dnpepvs = []
    for entity in entities:
        dnpepv = {
            'values': {},
            'entity': entity,
            'name': "ΔNpe*",
            'unit': "m3",
        }
        dnpepvs.append(calculate_values(dataframes, None, dnpepv, -1))
    scatter_info = calculate_scatter_info(*dnpepvs)
    print(f"dpercentile = {scatter_info['dpercentile']}")
    print(f"P10 = {scatter_info['simulations']['P10']}")
    print(f"P50 = {scatter_info['simulations']['P50']}")
    print(f"P90 = {scatter_info['simulations']['P90']}")

    scatter_info['simulations']['P50'] = ['164']

    variables = ["Npe*", "Qo"]
    units = ["m3", "m3/day"]
    for entity in entities:
        figs = create_multiple_delta_time_series_plot(dataframes, None, scatter_info, entity, variables, units)
        for fig in figs:
            fig.show()

    print("Processing complete.")

if __name__ == "__main__":
    main()
