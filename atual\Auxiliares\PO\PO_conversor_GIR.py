import pandas as pd
from openpyxl import load_workbook
from datetime import datetime
import os
from typing import Dict, List

def processar_planilha(caminho_entrada: str, 
                      caminho_mapeamento: str,
                      start_row: int = 196, 
                      header_row: int = 1) -> None:

    # Configurações
    ABAS: List[str] = ['Pot_CDAN', 'Pot_P-57', 'Pot_P-58', 'Pot_MQT']
    MAPEAMENTO_TIPOS: Dict[str, str] = {
        'ÓLEO': '1 - Qo Pot',
        'GÁS': '2 - Qg Pot',
        'ÁGUA': '3 - Qw Pot',
        'POT': '4 - Qwi Pot'
    }
    COLUNAS_FINAIS: List[str] = [
        'Versão', 'Cenário', 'Campo', 'Nº da ZP',
        'Plataforma', 'Projeto', 'Poço', 'Data',
        '1 - Qo Pot', '2 - Qg Pot', '3 - Qw Pot',
        '4 - Qwi Pot', '5 - <PERSON><PERSON>t', '6 - GL Pot',
        '7 - Auto inj Pot'
    ]
    CONVERSAO_BBL_M3 = 6.28981  # 1 m³ = 6.28981 barris

    # Configuração do split (NOVO)
    split_config = [
        {'Campo': 'JUB', 'Nº da ZP': 5, 'Proporção': 0.9725},
        {'Campo': 'ANC9', 'Nº da ZP': 1, 'Proporção': 0.0154},
        {'Campo': 'ANC8', 'Nº da ZP': 1, 'Proporção': 0.0035},
        {'Campo': 'ARGO', 'Nº da ZP': 4, 'Proporção': 0.0086}
    ]
    producao_cols = ['1 - Qo Pot', '2 - Qg Pot', '3 - Qw Pot',
                    '4 - Qwi Pot', '5 - Qgi Pot', '6 - GL Pot',
                    '7 - Auto inj Pot']

    # Carregar arquivo de mapeamento
    try:
        mapeamento_df = pd.read_excel(caminho_mapeamento)
        required_cols = ['Plataforma', 'Poço', 'Campo', 'Projeto', 'Nº da ZP', 'Novo_Nome']
        if not all(col in mapeamento_df.columns for col in required_cols):
            print("🚨 Arquivo de mapeamento incompleto. Colunas necessárias:", required_cols)
            return
        print("✅ Arquivo de mapeamento carregado com sucesso")
    except Exception as e:
        print(f"🚨 Erro ao carregar arquivo de mapeamento: {str(e)}")
        return

    try:
        wb = load_workbook(caminho_entrada, data_only=True, read_only=True)
    except FileNotFoundError:
        print(f"🚨 Arquivo não encontrado: {caminho_entrada}")
        return
    except Exception as e:
        print(f"🚨 Erro ao abrir o arquivo: {str(e)}")
        return

    dfs: List[pd.DataFrame] = []

    for plataforma in ABAS:
        if plataforma not in wb.sheetnames:
            print(f"⚠️ Aba não encontrada: {plataforma}")
            continue

        sheet = wb[plataforma]
        print(f"\n📂 Processando aba: {plataforma}")

        try:
            # Extrair dados
            dados = [row for row in sheet.iter_rows(min_row=start_row, values_only=True) 
                    if any(cell is not None for cell in row)]

            if not dados:
                print(f"⚠️ Sem dados na aba {plataforma} a partir da linha {start_row}")
                continue

            # Criar DataFrame
            df = pd.DataFrame(dados)
            df['Data'] = pd.date_range(start=datetime(2025, 1, 1), periods=len(df), freq='MS')

            # Processar colunas
            for col_idx in range(sheet.max_column):
                try:
                    # Obter cabeçalho
                    col_name = sheet.cell(row=header_row, column=col_idx+1).value
                    if not col_name:
                        continue

                    # Validar e mapear tipo
                    partes = str(col_name).split()
                    if not partes:
                        continue

                    tipo = partes[0].strip().upper()
                    if tipo not in MAPEAMENTO_TIPOS:
                        continue

                    # Processar valores numéricos
                    col_data = df[col_idx].apply(pd.to_numeric, errors='coerce')

                    # Aplicar conversão de unidades
                    if tipo in ['ÓLEO', 'ÁGUA', 'POT']:
                        col_data = col_data / CONVERSAO_BBL_M3
                        print(f"⚙️ Conversão aplicada em {col_name}")

                    temp_df = col_data.to_frame(name=MAPEAMENTO_TIPOS[tipo])
                    temp_df['Data'] = df['Data']
                    temp_df['Plataforma'] = plataforma.replace('Pot_', '')
                    temp_df['Poço'] = ' '.join(partes[1:]).strip()

                    dfs.append(temp_df.dropna(subset=[MAPEAMENTO_TIPOS[tipo]]))

                except Exception as col_error:
                    print(f"⚠️ Erro na coluna {col_idx+1}: {str(col_error)}")
                    continue

        except Exception as e:
            print(f"🚨 Erro crítico na aba {plataforma}: {str(e)}")
            continue

    if dfs:
        final_df = pd.concat(dfs, ignore_index=True)
        # Adicionar colunas extras vazias
        for nova_col in ['5 - Qgi Pot', '6 - GL Pot', '7 - Auto inj Pot']:
            final_df[nova_col] = pd.NA

        # Merge com dados de mapeamento
        try:
            final_df = final_df.merge(
                mapeamento_df[['Plataforma', 'Poço', 'Campo', 'Projeto', 'Nº da ZP', 'Novo_Nome']],
                on=['Plataforma', 'Poço'],
                how='left',
                validate='many_to_one'
            )
            final_df['Poço'] = final_df['Novo_Nome']
            final_df = final_df.drop(columns=['Novo_Nome'])
            print("✅ Nomes dos poços atualizados com sucesso")

            nao_mapeados = final_df[final_df['Poço'].isna()]
            if not nao_mapeados.empty:
                print("\n⚠️ Atenção: Poços não mapeados encontrados:")
                print(nao_mapeados[['Plataforma', 'Poço']].drop_duplicates().to_markdown(index=False))
        except Exception as e:
            print(f"🚨 Erro no merge de mapeamento: {str(e)}")
            return

        # Adicionar metadados fixos
        final_df['Versão'] = "PO08.2025"
        final_df['Cenário'] = "P50"

        # Consolidação das linhas
        colunas_grupo = ['Versão', 'Cenário', 'Campo', 'Nº da ZP',
                        'Plataforma', 'Projeto', 'Poço', 'Data']
        final_df = final_df.groupby(colunas_grupo, as_index=False).agg({
            '1 - Qo Pot': 'max',
            '2 - Qg Pot': 'max',
            '3 - Qw Pot': 'max',
            '4 - Qwi Pot': 'max',
            '5 - Qgi Pot': 'max',
            '6 - GL Pot': 'max',
            '7 - Auto inj Pot': 'max'
        })

        # Filtro combinado
        colunas_producao = [
            '1 - Qo Pot', '2 - Qg Pot', '3 - Qw Pot',
            '4 - Qwi Pot', '5 - Qgi Pot', '6 - GL Pot',
            '7 - Auto inj Pot'
        ]
        mask_vazias = (final_df[colunas_producao].isna() | (final_df[colunas_producao] == 0)).all(axis=1)
        mask_poco = final_df['Poço'].notna() & (final_df['Poço'] != '')
        linhas_originais = len(final_df)
        final_df = final_df[~mask_vazias & mask_poco]
        print(f"\n🧹 Linhas totalmente vazias/zeradas removidas: {linhas_originais - len(final_df)}")

        # ========= NOVO: Split ajustado para decimal =========
        print("\n🔀 Processando split das linhas JUB/ZP5...")

        # Converter para string com 1 decimal
        final_df['Nº da ZP_str'] = final_df['Nº da ZP'].astype(str).str.strip()

        # Debug detalhado
        print("\n🔍 Valores únicos de 'Nº da ZP' para JUB:", final_df[final_df['Campo'] == 'JUB']['Nº da ZP_str'].unique())

        # Condição corrigida
        mask_split = (
            (final_df['Campo'].str.strip().str.upper() == 'JUB') & 
            (final_df['Nº da ZP_str'] == '5.0')
        )

        print(f"🔍 Linhas encontradas para split: {sum(mask_split)}")

        if mask_split.any():
            to_split = final_df[mask_split].copy()
            
            # Debug: mostrar metadados das linhas
            print("\n🔍 Metadados das linhas para split:")
            print(to_split[['Plataforma', 'Poço', 'Data']].head())
            
            # Processar split
            new_rows = []
            for _, row in to_split.iterrows():
                for config in split_config:
                    new_row = row.copy()
                    new_row[producao_cols] = new_row[producao_cols] * config['Proporção']
                    new_row['Campo'] = config['Campo']
                    new_row['Nº da ZP'] = str(config['Nº da ZP'])
                    new_rows.append(new_row)
            
            # Remover linhas originais e adicionar novas
            final_df = pd.concat([final_df[~mask_split], pd.DataFrame(new_rows)], ignore_index=True)
            print(f"✅ Split realizado: {len(to_split)} linhas → {len(new_rows)} novas linhas")
            
            # Verificação final
            print("\n🔍 Campos após split:", final_df['Campo'].unique())
        else:
            print("\n⚠️ Nenhuma linha JUB/ZP5.0 encontrada para split!")
        # ========= FIM NOVO =========

        # Padronização de tipos de dados
        text_cols = ['Versão', 'Cenário', 'Campo', 'Nº da ZP', 
                    'Plataforma', 'Projeto', 'Poço']
        numeric_cols = producao_cols
        final_df[text_cols] = final_df[text_cols].astype(str)
        final_df['Data'] = pd.to_datetime(final_df['Data'])
        final_df[numeric_cols] = final_df[numeric_cols].apply(pd.to_numeric, errors='coerce')

        # Ordenar e formatar
        final_df = final_df.reindex(columns=COLUNAS_FINAIS)

        # Gerar nome do arquivo com metadados
        data_atual = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
        nome_arquivo = f"{data_atual}_PO08.2025_P50_GIR.xlsx"
        caminho_completo = os.path.join(os.path.dirname(caminho_entrada), nome_arquivo)

        # Formatação profissional do Excel
        with pd.ExcelWriter(caminho_completo, engine='xlsxwriter') as writer:
            final_df.to_excel(writer, index=False, sheet_name='Dados Produção')
            workbook = writer.book
            worksheet = writer.sheets['Dados Produção']

            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#4472C4',
                'font_color': 'white',
                'border': 1
            })
            for col_num, value in enumerate(final_df.columns.values):
                worksheet.write(0, col_num, value, header_format)

            date_format = workbook.add_format({'num_format': 'dd/mm/yyyy', 'align': 'center'})
            worksheet.set_column('H:H', 12, date_format)

            number_format = workbook.add_format({'num_format': '#,##0.00', 'align': 'right'})
            for col in range(8, 15):
                worksheet.set_column(col, col, 15, number_format)

            text_format = workbook.add_format({'align': 'left'})
            for col in range(0, 7):
                worksheet.set_column(col, col, 18, text_format)

        print(f"\n✅ Arquivo gerado com sucesso: {caminho_completo}")
        print(f"📊 Total de registros válidos: {len(final_df)}")
        print(f"📅 Período coberto: {final_df['Data'].min().date()} a {final_df['Data'].max().date()}")

    else:
        print("\n❌ Nenhum dado válido processado. Verifique:")
        print("- Formato do arquivo de entrada")
        print("- Posição dos cabeçalhos e dados")
        print("- Mapeamento dos tipos de produção")

if __name__ == "__main__":
    processar_planilha(
        caminho_entrada=r"L:\res\campos\jubarte\er\er03\PN\2630\C2\PO8_ConverterGIRv2\Potencial_P50.xlsx",
        caminho_mapeamento=r"L:\res\campos\jubarte\er\er03\PN\2630\C2\PO8_ConverterGIRv2\mapeamento_poços.xlsx"
    )