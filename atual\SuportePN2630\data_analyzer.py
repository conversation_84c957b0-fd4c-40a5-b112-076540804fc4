#!/usr/bin/env python3
"""
Sistema Modular de Análise de Dados para Produção Industrial
Versão 4.0 - Arquitetura Modular e Generalizada

Estrutura:
- config/: Configurações e constantes
- data/: Handlers de dados e validação
- processors/: Processadores de cálculo
- reporters/: Geradores de relatórios
- utils/: Utilitários e helpers
- main: Interface principal

Autor: Sistema de Análise Industrial
Data: 2025
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
from pathlib import Path
from datetime import datetime, date
from calendar import monthrange
import traceback
import logging
import warnings
import os
import sys

import pandas as pd
import numpy as np

# =============================================================================
# CONFIGURAÇÃO E CONSTANTES
# =============================================================================

@dataclass
class ProcessingConfig:
    """Configuração centralizada para processamento de dados"""
    
    # Configurações de logging
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_file: Optional[str] = "data_processor.log"
    
    # Configurações de dados
    date_formats: List[str] = field(default_factory=lambda: [
        '%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%d-%m-%Y', 
        '%Y/%m/%d', '%d.%m.%Y', '%Y.%m.%d'
    ])
    
    encoding: str = "utf-8-sig"
    float_precision: int = 2
    chunk_size: int = 10000
    
    # Configurações de validação
    min_records: int = 1
    max_null_percentage: float = 0.5
    
    # Configurações de saída
    excel_engine: str = "openpyxl"
    csv_separator: str = ","
    datetime_format: str = "%d/%m/%Y"


@dataclass
class ColumnConfig:
    """Configuração de coluna de dados"""
    name: str
    display_name: str
    data_type: str = "numeric"
    unit: str = ""
    required: bool = False
    validation_func: Optional[Callable] = None
    aggregation_funcs: List[str] = field(default_factory=lambda: ["sum", "mean", "max", "min"])


@dataclass
class DatasetConfig:
    """Configuração completa de um dataset"""
    name: str
    description: str
    required_columns: List[str]
    processing_columns: Dict[str, ColumnConfig]
    date_column: str = "Data"
    identifier_column: str = "ID"
    groupby_columns: List[str] = field(default_factory=list)


# =============================================================================
# CONFIGURAÇÕES ESPECÍFICAS PARA PETRÓLEO
# =============================================================================

class PetroleumConfig:
    """Configurações específicas para dados de petróleo"""
    
    @staticmethod
    def get_petroleum_config() -> DatasetConfig:
        """Retorna configuração para dados de petróleo"""
        
        processing_columns = {
            'Qo': ColumnConfig(
                name='1 - Qo Pot',
                display_name='Óleo',
                unit='m³/d',
                aggregation_funcs=["sum", "mean", "max"]
            ),
            'Qg': ColumnConfig(
                name='2 - Qg Pot',
                display_name='Gás',
                unit='m³/d',
                aggregation_funcs=["sum", "mean", "max"]
            ),
            'Qw': ColumnConfig(
                name='3 - Qw Pot',
                display_name='Água',
                unit='m³/d',
                aggregation_funcs=["sum", "mean", "max"]
            ),
            'Qwi': ColumnConfig(
                name='4 - Qwi Pot',
                display_name='Água Injetada',
                unit='m³/d',
                aggregation_funcs=["sum", "mean", "max"]
            ),
            'Qgi': ColumnConfig(
                name='5 - Qgi Pot',
                display_name='Gás Injetado',
                unit='m³/d',
                aggregation_funcs=["sum", "mean", "max"]
            ),
            'GL': ColumnConfig(
                name='6 - GL Pot',
                display_name='Gas Lift',
                unit='m³/d',
                aggregation_funcs=["sum", "mean", "max"]
            ),
            'Auto': ColumnConfig(
                name='7 - Auto inj Pot',
                display_name='Auto Injeção',
                unit='m³/d',
                aggregation_funcs=["sum", "mean", "max"]
            )
        }
        
        return DatasetConfig(
            name="petroleum_production",
            description="Dados de Produção de Petróleo",
            required_columns=["Data", "Poço"],
            processing_columns=processing_columns,
            date_column="Data",
            identifier_column="Poço",
            groupby_columns=["Poço", "Campo", "Plataforma"]
        )


# =============================================================================
# UTILITÁRIOS E HELPERS
# =============================================================================

class Logger:
    """Gerenciador centralizado de logging"""
    
    _loggers: Dict[str, logging.Logger] = {}
    
    @classmethod
    def get_logger(cls, name: str, config: ProcessingConfig = None) -> logging.Logger:
        """Obtém logger configurado"""
        if name not in cls._loggers:
            if config is None:
                config = ProcessingConfig()
            
            logger = logging.getLogger(name)
            logger.setLevel(getattr(logging, config.log_level.upper()))
            
            # Remove handlers existentes
            for handler in logger.handlers[:]:
                logger.removeHandler(handler)
            
            # Handler para console
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(logging.Formatter(config.log_format))
            logger.addHandler(console_handler)
            
            # Handler para arquivo se especificado
            if config.log_file:
                file_handler = logging.FileHandler(config.log_file, encoding='utf-8')
                file_handler.setFormatter(logging.Formatter(config.log_format))
                logger.addHandler(file_handler)
            
            cls._loggers[name] = logger
        
        return cls._loggers[name]


class DateUtils:
    """Utilitários para manipulação de datas"""
    
    @staticmethod
    def convert_dates_robust(date_series: pd.Series, formats: List[str]) -> pd.Series:
        """Converte datas usando múltiplos formatos"""
        # Primeiro tenta conversão automática
        try:
            converted = pd.to_datetime(date_series, errors='coerce', dayfirst=True)
            if not converted.isna().all():
                return converted
        except:
            pass
        
        # Tenta formatos específicos
        for fmt in formats:
            try:
                converted = pd.to_datetime(date_series, format=fmt, errors='coerce')
                if not converted.isna().all():
                    return converted
            except:
                continue
        
        # Conversão forçada como último recurso
        return pd.to_datetime(date_series, errors='coerce', infer_datetime_format=True)
    
    @staticmethod
    def get_days_in_month(date_series: pd.Series) -> pd.Series:
        """Retorna número de dias no mês para cada data"""
        def days_in_month(date_val):
            try:
                if pd.isna(date_val):
                    return 30
                return monthrange(date_val.year, date_val.month)[1]
            except:
                return 30
        
        return date_series.apply(days_in_month)


class NumericUtils:
    """Utilitários para processamento numérico"""
    
    @staticmethod
    def clean_numeric_column(series: pd.Series, column_name: str = "") -> pd.Series:
        """Limpa e converte coluna numérica"""
        try:
            # Converte para string
            str_series = series.astype(str)
            
            # Limpeza de caracteres
            cleaned = (str_series
                      .str.replace(' ', '', regex=False)
                      .str.replace(',', '.', regex=False)
                      .str.replace('−', '-', regex=False)
                      .str.replace('–', '-', regex=False)
                      .str.replace('—', '-', regex=False))
            
            # Converte para numérico
            numeric = pd.to_numeric(cleaned, errors='coerce')
            
            # Preenche NaN com 0
            return numeric.fillna(0)
            
        except Exception as e:
            logger = Logger.get_logger("NumericUtils")
            logger.error(f"Erro ao processar coluna {column_name}: {e}")
            return pd.Series([0] * len(series), index=series.index)


# =============================================================================
# VALIDAÇÃO DE DADOS
# =============================================================================

@dataclass
class ValidationResult:
    """Resultado de validação"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metrics: Dict[str, Any] = field(default_factory=dict)


class DataValidator:
    """Validador genérico de dados"""
    
    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.logger = Logger.get_logger(self.__class__.__name__, config)
    
    def validate_dataframe(self, df: pd.DataFrame, dataset_config: DatasetConfig) -> ValidationResult:
        """Valida DataFrame completo"""
        result = ValidationResult(is_valid=True)
        
        # Validações básicas
        self._validate_basic_structure(df, dataset_config, result)
        self._validate_required_columns(df, dataset_config, result)
        self._validate_data_quality(df, dataset_config, result)
        self._validate_date_column(df, dataset_config, result)
        
        # Métricas gerais
        result.metrics.update({
            'total_records': len(df),
            'total_columns': len(df.columns),
            'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024 / 1024,
            'null_percentage': (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100
        })
        
        # Log dos resultados
        if result.errors:
            self.logger.error(f"Validação falhou: {len(result.errors)} erros encontrados")
            for error in result.errors:
                self.logger.error(f"  - {error}")
        
        if result.warnings:
            self.logger.warning(f"Avisos de validação: {len(result.warnings)}")
            for warning in result.warnings:
                self.logger.warning(f"  - {warning}")
        
        return result
    
    def _validate_basic_structure(self, df: pd.DataFrame, config: DatasetConfig, result: ValidationResult):
        """Valida estrutura básica"""
        if len(df) < self.config.min_records:
            result.errors.append(f"Dataset tem apenas {len(df)} registros (mínimo: {self.config.min_records})")
            result.is_valid = False
        
        if len(df.columns) == 0:
            result.errors.append("Dataset não possui colunas")
            result.is_valid = False
    
    def _validate_required_columns(self, df: pd.DataFrame, config: DatasetConfig, result: ValidationResult):
        """Valida colunas obrigatórias"""
        missing_columns = [col for col in config.required_columns if col not in df.columns]
        if missing_columns:
            result.errors.append(f"Colunas obrigatórias ausentes: {missing_columns}")
            result.is_valid = False
    
    def _validate_data_quality(self, df: pd.DataFrame, config: DatasetConfig, result: ValidationResult):
        """Valida qualidade dos dados"""
        # Percentual de nulos por coluna
        null_percentages = (df.isnull().sum() / len(df)) * 100
        
        for col, percentage in null_percentages.items():
            if percentage > self.config.max_null_percentage * 100:
                result.warnings.append(f"Coluna '{col}' tem {percentage:.1f}% de valores nulos")
        
        # Duplicatas
        duplicates = df.duplicated().sum()
        if duplicates > 0:
            result.warnings.append(f"Encontradas {duplicates} linhas duplicadas")
    
    def _validate_date_column(self, df: pd.DataFrame, config: DatasetConfig, result: ValidationResult):
        """Valida coluna de data"""
        if config.date_column in df.columns:
            date_col = df[config.date_column]
            
            # Verifica se há datas válidas
            try:
                converted_dates = pd.to_datetime(date_col, errors='coerce')
                invalid_dates = converted_dates.isnull().sum()
                
                if invalid_dates > 0:
                    result.warnings.append(f"Coluna de data tem {invalid_dates} valores inválidos")
                
                if invalid_dates == len(df):
                    result.errors.append("Todas as datas são inválidas")
                    result.is_valid = False
                    
            except Exception as e:
                result.errors.append(f"Erro ao validar coluna de data: {str(e)}")
                result.is_valid = False


# =============================================================================
# CARREGAMENTO E MANIPULAÇÃO DE DADOS
# =============================================================================

class DataLoader:
    """Carregador genérico de dados"""
    
    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.logger = Logger.get_logger(self.__class__.__name__, config)
    
    def load_excel(self, file_path: str, sheet_name: Optional[str] = None) -> pd.DataFrame:
        """Carrega arquivo Excel"""
        self.logger.info(f"Carregando arquivo Excel: {file_path}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Arquivo não encontrado: {file_path}")
        
        file_size_mb = os.path.getsize(file_path) / 1024 / 1024
        self.logger.info(f"Tamanho do arquivo: {file_size_mb:.2f} MB")
        
        try:
            # Lista abas disponíveis
            excel_file = pd.ExcelFile(file_path)
            available_sheets = excel_file.sheet_names
            self.logger.info(f"Abas disponíveis: {available_sheets}")
            
            # Carrega dados
            if sheet_name:
                if sheet_name not in available_sheets:
                    raise ValueError(f"Aba '{sheet_name}' não encontrada. Disponíveis: {available_sheets}")
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine=self.config.excel_engine)
                self.logger.info(f"Carregada aba: {sheet_name}")
            else:
                df = pd.read_excel(file_path, sheet_name=0, engine=self.config.excel_engine)
                self.logger.info(f"Carregada primeira aba: {available_sheets[0]}")
            
            self.logger.info(f"Dados carregados: {len(df)} registros, {len(df.columns)} colunas")
            return df
            
        except Exception as e:
            self.logger.error(f"Erro ao carregar Excel: {str(e)}")
            raise
    
    def load_csv(self, file_path: str, **kwargs) -> pd.DataFrame:
        """Carrega arquivo CSV"""
        self.logger.info(f"Carregando arquivo CSV: {file_path}")
        
        default_params = {
            'encoding': self.config.encoding,
            'sep': self.config.csv_separator,
            'low_memory': False
        }
        default_params.update(kwargs)
        
        try:
            df = pd.read_csv(file_path, **default_params)
            self.logger.info(f"CSV carregado: {len(df)} registros, {len(df.columns)} colunas")
            return df
        except Exception as e:
            self.logger.error(f"Erro ao carregar CSV: {str(e)}")
            raise


class DataPreprocessor:
    """Pré-processador genérico de dados"""
    
    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.logger = Logger.get_logger(self.__class__.__name__, config)
    
    def preprocess_dataframe(self, df: pd.DataFrame, dataset_config: DatasetConfig) -> pd.DataFrame:
        """Pré-processa DataFrame"""
        self.logger.info("Iniciando pré-processamento...")
        
        # Cria cópia
        df_processed = df.copy()
        
        # Preserva dados originais importantes
        if dataset_config.date_column in df_processed.columns:
            df_processed[f'{dataset_config.date_column}_Original'] = df_processed[dataset_config.date_column].copy()
        
        # Processa coluna de data
        df_processed = self._process_date_column(df_processed, dataset_config)
        
        # Processa colunas numéricas
        df_processed = self._process_numeric_columns(df_processed, dataset_config)
        
        # Remove registros inválidos
        df_processed = self._clean_invalid_records(df_processed, dataset_config)
        
        # Ordena dados
        df_processed = self._sort_dataframe(df_processed, dataset_config)
        
        self.logger.info(f"Pré-processamento concluído: {len(df_processed)} registros válidos")
        return df_processed
    
    def _process_date_column(self, df: pd.DataFrame, config: DatasetConfig) -> pd.DataFrame:
        """Processa coluna de data"""
        if config.date_column in df.columns:
            self.logger.info(f"Processando coluna de data: {config.date_column}")
            df[config.date_column] = DateUtils.convert_dates_robust(
                df[config.date_column], 
                self.config.date_formats
            )
        return df
    
    def _process_numeric_columns(self, df: pd.DataFrame, config: DatasetConfig) -> pd.DataFrame:
        """Processa colunas numéricas"""
        processed_count = 0
        
        for key, col_config in config.processing_columns.items():
            if col_config.name in df.columns and col_config.data_type == "numeric":
                self.logger.debug(f"Processando coluna numérica: {col_config.name}")
                df[col_config.name] = NumericUtils.clean_numeric_column(
                    df[col_config.name], 
                    col_config.name
                )
                processed_count += 1
        
        self.logger.info(f"Processadas {processed_count} colunas numéricas")
        return df
    
    def _clean_invalid_records(self, df: pd.DataFrame, config: DatasetConfig) -> pd.DataFrame:
        """Remove registros inválidos"""
        initial_count = len(df)
        
        # Remove registros com data inválida
        if config.date_column in df.columns:
            df = df.dropna(subset=[config.date_column])
        
        # Remove registros com identificador inválido
        if config.identifier_column in df.columns:
            df = df.dropna(subset=[config.identifier_column])
        
        removed_count = initial_count - len(df)
        if removed_count > 0:
            self.logger.warning(f"Removidos {removed_count} registros inválidos")
        
        return df
    
    def _sort_dataframe(self, df: pd.DataFrame, config: DatasetConfig) -> pd.DataFrame:
        """Ordena DataFrame"""
        sort_columns = []
        
        if config.identifier_column in df.columns:
            sort_columns.append(config.identifier_column)
        
        if config.date_column in df.columns:
            sort_columns.append(config.date_column)
        
        if sort_columns:
            df = df.sort_values(sort_columns).reset_index(drop=True)
            self.logger.info(f"Dados ordenados por: {sort_columns}")
        
        return df


# =============================================================================
# PROCESSADORES DE CÁLCULO
# =============================================================================

class BaseProcessor(ABC):
    """Processador base abstrato"""
    
    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.logger = Logger.get_logger(self.__class__.__name__, config)
    
    @abstractmethod
    def process(self, df: pd.DataFrame, dataset_config: DatasetConfig) -> pd.DataFrame:
        """Processa dados"""
        pass


class VolumeProcessor(BaseProcessor):
    """Processador de volumes baseado em vazão * tempo"""
    
    def process(self, df: pd.DataFrame, dataset_config: DatasetConfig) -> pd.DataFrame:
        """Calcula volumes mensais e acumulados"""
        self.logger.info("Iniciando cálculo de volumes...")
        start_time = datetime.now()
        
        df_calc = df.copy()
        
        # Calcula dias do mês
        if dataset_config.date_column in df_calc.columns:
            df_calc['_days_in_month'] = DateUtils.get_days_in_month(df_calc[dataset_config.date_column])
        else:
            df_calc['_days_in_month'] = 30  # Valor padrão
        
        volumes_calculated = 0
        
        # Processa cada coluna configurada
        for key, col_config in dataset_config.processing_columns.items():
            if col_config.name in df_calc.columns and col_config.data_type == "numeric":
                # Volume mensal
                volume_col = f'Vol_{key}'
                df_calc[volume_col] = df_calc[col_config.name] * df_calc['_days_in_month']
                
                # Volume acumulado por identificador
                if dataset_config.identifier_column in df_calc.columns:
                    cumsum_col = f'Acum_{key}'
                    df_calc[cumsum_col] = df_calc.groupby(dataset_config.identifier_column)[volume_col].cumsum()
                
                volumes_calculated += 1
                
                total_volume = df_calc[volume_col].sum()
                self.logger.info(f"{col_config.display_name}: Volume total = {total_volume:,.2f}")
        
        # Remove coluna auxiliar
        df_calc = df_calc.drop('_days_in_month', axis=1)
        
        # Restaura datas originais se existirem
        original_date_col = f'{dataset_config.date_column}_Original'
        if original_date_col in df_calc.columns:
            df_calc[dataset_config.date_column] = df_calc[original_date_col]
            df_calc = df_calc.drop(original_date_col, axis=1)
            self.logger.info("Datas originais restauradas")
        
        elapsed_time = (datetime.now() - start_time).total_seconds()
        self.logger.info(f"Processamento concluído em {elapsed_time:.2f}s - {volumes_calculated} tipos de volume")
        
        return df_calc


class AggregationProcessor(BaseProcessor):
    """Processador de agregações genéricas"""
    
    def process(self, df: pd.DataFrame, dataset_config: DatasetConfig) -> Dict[str, pd.DataFrame]:
        """Gera agregações configuradas"""
        self.logger.info("Gerando agregações...")
        
        results = {}
        
        # Agregação por identificador
        if dataset_config.identifier_column in df.columns:
            results['by_identifier'] = self._aggregate_by_identifier(df, dataset_config)
        
        # Agregação temporal
        if dataset_config.date_column in df.columns:
            results['by_period'] = self._aggregate_by_period(df, dataset_config)
        
        # Agregações por grupos personalizados
        for group_col in dataset_config.groupby_columns:
            if group_col in df.columns:
                results[f'by_{group_col.lower()}'] = self._aggregate_by_column(df, dataset_config, group_col)
        
        return results
    
    def _aggregate_by_identifier(self, df: pd.DataFrame, config: DatasetConfig) -> pd.DataFrame:
        """Agrega por identificador"""
        try:
            agg_dict = {config.date_column: ['min', 'max', 'count']} if config.date_column in df.columns else {}
            
            # Adiciona colunas de processamento
            for key, col_config in config.processing_columns.items():
                if col_config.name in df.columns:
                    agg_dict[col_config.name] = col_config.aggregation_funcs
                
                # Colunas de volume se existirem
                vol_col = f'Vol_{key}'
                if vol_col in df.columns:
                    agg_dict[vol_col] = ['sum', 'mean', 'max']
                
                # Colunas acumuladas (apenas máximo)
                acum_col = f'Acum_{key}'
                if acum_col in df.columns:
                    agg_dict[acum_col] = 'max'
            
            result = df.groupby(config.identifier_column).agg(agg_dict).round(self.config.float_precision)
            
            # Achata colunas multi-level
            result.columns = ['_'.join(col).strip() if isinstance(col, tuple) else col for col in result.columns]
            result = result.reset_index()
            
            return result
            
        except Exception as e:
            self.logger.error(f"Erro na agregação por identificador: {str(e)}")
            return pd.DataFrame()
    
    def _aggregate_by_period(self, df: pd.DataFrame, config: DatasetConfig) -> pd.DataFrame:
        """Agrega por período (mensal)"""
        try:
            df_temp = df.copy()
            df_temp['Year_Month'] = df_temp[config.date_column].dt.to_period('M')
            
            agg_dict = {config.identifier_column: 'nunique', config.date_column: 'count'}
            
            # Adiciona colunas de volume
            for key, col_config in config.processing_columns.items():
                vol_col = f'Vol_{key}'
                if vol_col in df_temp.columns:
                    agg_dict[vol_col] = 'sum'
                
                if col_config.name in df_temp.columns:
                    agg_dict[col_config.name] = 'mean'
            
            result = df_temp.groupby('Year_Month').agg(agg_dict).round(self.config.float_precision)
            result = result.reset_index()
            result['Year_Month'] = result['Year_Month'].astype(str)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Erro na agregação temporal: {str(e)}")
            return pd.DataFrame()
    
    def _aggregate_by_column(self, df: pd.DataFrame, config: DatasetConfig, group_column: str) -> pd.DataFrame:
        """Agrega por coluna específica"""
        try:
            agg_dict = {config.identifier_column: 'nunique'}
            
            for key, col_config in config.processing_columns.items():
                if col_config.name in df.columns:
                    agg_dict[col_config.name] = ['sum', 'mean']
                
                vol_col = f'Vol_{key}'
                if vol_col in df.columns:
                    agg_dict[vol_col] = 'sum'
            
            result = df.groupby(group_column).agg(agg_dict).round(self.config.float_precision)
            result.columns = ['_'.join(col).strip() if isinstance(col, tuple) else col for col in result.columns]
            result = result.reset_index()
            
            return result
            
        except Exception as e:
            self.logger.error(f"Erro na agregação por {group_column}: {str(e)}")
            return pd.DataFrame()


# =============================================================================
# SISTEMA DE RELATÓRIOS
# =============================================================================

@dataclass
class ReportData:
    """Dados para geração de relatório"""
    main_data: pd.DataFrame
    aggregations: Dict[str, pd.DataFrame]
    validation_result: ValidationResult
    processing_metadata: Dict[str, Any]
    dataset_config: DatasetConfig


class BaseReporter(ABC):
    """Reporter base abstrato"""
    
    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.logger = Logger.get_logger(self.__class__.__name__, config)
    
    @abstractmethod
    def generate_report(self, report_data: ReportData, output_path: str):
        """Gera relatório"""
        pass


class ExcelReporter(BaseReporter):
    """Gerador de relatórios Excel"""
    
    def generate_report(self, report_data: ReportData, output_path: str):
        """Gera relatório Excel completo"""
        self.logger.info(f"Gerando relatório Excel: {output_path}")
        
        try:
            with pd.ExcelWriter(output_path, engine=self.config.excel_engine) as writer:
                # Dados principais
                main_df = self._format_main_data(report_data.main_data, report_data.dataset_config)
                main_df.to_excel(writer, sheet_name='Dados_Principais', index=False)
                
                # Agregações
                for name, agg_df in report_data.aggregations.items():
                    if not agg_df.empty:
                        sheet_name = f'Resumo_{name.replace("by_", "").title()}'[:31]  # Limite Excel
                        agg_df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # Estatísticas
                stats_df = self._generate_statistics_df(report_data)
                stats_df.to_excel(writer, sheet_name='Estatisticas', index=False)
                
                # Validação
                validation_df = self._generate_validation_df(report_data.validation_result)
                validation_df.to_excel(writer, sheet_name='Validacao', index=False)
                
                # Metadados
                metadata_df = self._generate_metadata_df(report_data.processing_metadata)
                metadata_df.to_excel(writer, sheet_name='Metadados', index=False)
            
            file_size = os.path.getsize(output_path) / 1024 / 1024
            self.logger.info(f"Relatório Excel salvo: {file_size:.2f} MB")
            
        except Exception as e:
            self.logger.error(f"Erro ao gerar relatório Excel: {str(e)}")
            raise
    
    def _format_main_data(self, df: pd.DataFrame, config: DatasetConfig) -> pd.DataFrame:
        """Formata dados principais para Excel"""
        df_formatted = df.copy()
        
        # Formata colunas numéricas
        numeric_columns = [col for col in df_formatted.columns if df_formatted[col].dtype in ['float64', 'int64']]
        for col in numeric_columns:
            if col.startswith(('Vol_', 'Acum_')) or any(col.startswith(f"{i} -") for i in range(1, 8)):
                df_formatted[col] = df_formatted[col].round(self.config.float_precision)
        
        return df_formatted
    
    def _generate_statistics_df(self, report_data: ReportData) -> pd.DataFrame:
        """Gera DataFrame com estatísticas"""
        stats_rows = []
        
        # Estatísticas gerais
        main_df = report_data.main_data
        stats_rows.extend([
            ['Geral', 'Total de Registros', len(main_df)],
            ['Geral', 'Total de Colunas', len(main_df.columns)],
            ['Geral', 'Uso de Memória (MB)', f"{main_df.memory_usage(deep=True).sum() / 1024 / 1024:.2f}"]
        ])
        
        # Estatísticas por coluna processada
        if report_data.dataset_config is not None:
            for key, col_config in report_data.dataset_config.processing_columns.items():
                if col_config.name in main_df.columns:
                    col_data = main_df[col_config.name]
                    stats_rows.extend([
                        [f'{col_config.display_name}', 'Média', f"{col_data.mean():.2f}"],
                        [f'{col_config.display_name}', 'Máximo', f"{col_data.max():.2f}"],
                        [f'{col_config.display_name}', 'Mínimo', f"{col_data.min():.2f}"],
                        [f'{col_config.display_name}', 'Desvio Padrão', f"{col_data.std():.2f}"],
                        [f'{col_config.display_name}', 'Registros > 0', f"{(col_data > 0).sum()}"],
                    ])
                    
                    # Volume total se existir
                    vol_col = f'Vol_{key}'
                    if vol_col in main_df.columns:
                        stats_rows.append([f'{col_config.display_name}', 'Volume Total', f"{main_df[vol_col].sum():.2f}"])
        
        return pd.DataFrame(stats_rows, columns=['Categoria', 'Metrica', 'Valor'])
    
    def _generate_validation_df(self, validation: ValidationResult) -> pd.DataFrame:
        """Gera DataFrame com resultados de validação"""
        validation_rows = [['Item', 'Status', 'Detalhes']]
        
        # Status geral
        validation_rows.append(['Validação Geral', 'OK' if validation.is_valid else 'ERRO', ''])
        
        # Métricas específicas
        for metric, value in validation.metrics.items():
            validation_rows.append(['Métrica', metric, str(value)])
        
        # Erros
        for error in validation.errors:
            validation_rows.append(['Erro', 'CRÍTICO', error])
        
        # Avisos
        for warning in validation.warnings:
            validation_rows.append(['Aviso', 'ATENÇÃO', warning])
        
        return pd.DataFrame(validation_rows[1:], columns=validation_rows[0])
    
    def _generate_metadata_df(self, metadata: Dict[str, Any]) -> pd.DataFrame:
        """Gera DataFrame com metadados"""
        metadata_rows = []
        
        def flatten_dict(d, prefix=''):
            for key, value in d.items():
                full_key = f"{prefix}.{key}" if prefix else key
                if isinstance(value, dict):
                    flatten_dict(value, full_key)
                else:
                    metadata_rows.append([full_key, str(value)])
        
        flatten_dict(metadata)
        return pd.DataFrame(metadata_rows, columns=['Atributo', 'Valor'])


class CSVReporter(BaseReporter):
    """Gerador de relatórios CSV"""
    
    def generate_report(self, report_data: ReportData, output_dir: str):
        """Gera múltiplos arquivos CSV"""
        self.logger.info(f"Gerando relatórios CSV em: {output_dir}")
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        try:
            # Dados principais
            main_file = output_path / "dados_principais.csv"
            report_data.main_data.to_csv(
                main_file, 
                index=False, 
                encoding=self.config.encoding,
                float_format=f'%.{self.config.float_precision}f'
            )
            
            # Agregações
            for name, agg_df in report_data.aggregations.items():
                if not agg_df.empty:
                    filename = f"resumo_{name.replace('by_', '')}.csv"
                    agg_file = output_path / filename
                    agg_df.to_csv(
                        agg_file,
                        index=False,
                        encoding=self.config.encoding,
                        float_format=f'%.{self.config.float_precision}f'
                    )
            
            # Estatísticas
            stats_df = self._generate_statistics_summary(report_data)
            stats_file = output_path / "estatisticas.csv"
            stats_df.to_csv(stats_file, index=False, encoding=self.config.encoding)
            
            self.logger.info(f"Arquivos CSV gerados em: {output_dir}")
            
        except Exception as e:
            self.logger.error(f"Erro ao gerar relatórios CSV: {str(e)}")
            raise
    
    def _generate_statistics_summary(self, report_data: ReportData) -> pd.DataFrame:
        """Gera resumo estatístico para CSV"""
        main_df = report_data.main_data
        stats_data = []
        
        if report_data.dataset_config is not None:
            for key, col_config in report_data.dataset_config.processing_columns.items():
                if col_config.name in main_df.columns:
                    col_data = main_df[col_config.name]
                    vol_col = f'Vol_{key}'
                    
                    row = {
                        'Vazao': col_config.display_name,
                        'Unidade': col_config.unit,
                        'Media': col_data.mean(),
                        'Maximo': col_data.max(),
                        'Minimo': col_data.min(),
                        'Desvio_Padrao': col_data.std(),
                        'Registros_Ativos': (col_data > 0).sum(),
                        'Registros_Zero': (col_data == 0).sum(),
                        'Percentual_Ativo': (col_data > 0).mean() * 100
                    }
                    
                    if vol_col in main_df.columns:
                        row['Volume_Total'] = main_df[vol_col].sum()
                    
                    stats_data.append(row)
        
        return pd.DataFrame(stats_data)


class TextReporter(BaseReporter):
    """Gerador de relatórios em texto"""
    
    def generate_report(self, report_data: ReportData, output_path: str):
        """Gera relatório detalhado em texto"""
        self.logger.info(f"Gerando relatório de texto: {output_path}")
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                self._write_header(f, report_data)
                self._write_general_info(f, report_data)
                self._write_validation_results(f, report_data)
                self._write_statistics(f, report_data)
                self._write_data_summary(f, report_data)
                self._write_footer(f)
            
            self.logger.info("Relatório de texto gerado com sucesso")
            
        except Exception as e:
            self.logger.error(f"Erro ao gerar relatório de texto: {str(e)}")
            raise
    
    def _write_header(self, f, report_data: ReportData):
        """Escreve cabeçalho do relatório"""
        if report_data.dataset_config is not None:
            f.write("=" * 80 + "\n")
            f.write(f"RELATÓRIO DE ANÁLISE DE DADOS - {report_data.dataset_config.description.upper()}\n")
            f.write("=" * 80 + "\n\n")
            f.write(f"Data de Geração: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}\n")
            f.write(f"Dataset: {report_data.dataset_config.name}\n")
            f.write(f"Versão do Sistema: 4.0 - Modular\n\n")
    
    def _write_general_info(self, f, report_data: ReportData):
        """Escreve informações gerais"""
        main_df = report_data.main_data
        
        f.write("INFORMAÇÕES GERAIS\n")
        f.write("-" * 40 + "\n")
        f.write(f"Total de Registros: {len(main_df):,}\n")
        f.write(f"Total de Colunas: {len(main_df.columns)}\n")
        f.write(f"Uso de Memória: {main_df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB\n")
        if report_data.dataset_config is not None:
            if report_data.dataset_config.date_column in main_df.columns:
                date_col = main_df[report_data.dataset_config.date_column]
                f.write(f"Período: {date_col.min().strftime('%d/%m/%Y')} a {date_col.max().strftime('%d/%m/%Y')}\n")
                f.write(f"Dias Totais: {(date_col.max() - date_col.min()).days}\n")
            
            if report_data.dataset_config.identifier_column in main_df.columns:
                unique_ids = main_df[report_data.dataset_config.identifier_column].nunique()
                f.write(f"Identificadores Únicos: {unique_ids}\n")
        
        f.write("\n")
    
    def _write_validation_results(self, f, report_data: ReportData):
        """Escreve resultados da validação"""
        validation = report_data.validation_result
        
        f.write("VALIDAÇÃO DOS DADOS\n")
        f.write("-" * 40 + "\n")
        f.write(f"Status Geral: {'✓ APROVADO' if validation.is_valid else '✗ REPROVADO'}\n")
        
        if validation.errors:
            f.write(f"\nErros Encontrados ({len(validation.errors)}):\n")
            for error in validation.errors:
                f.write(f"  ✗ {error}\n")
        
        if validation.warnings:
            f.write(f"\nAvisos ({len(validation.warnings)}):\n")
            for warning in validation.warnings:
                f.write(f"  ⚠ {warning}\n")
        
        f.write(f"\nMétricas de Validação:\n")
        for metric, value in validation.metrics.items():
            f.write(f"  {metric}: {value}\n")
        
        f.write("\n")
    
    def _write_statistics(self, f, report_data: ReportData):
        """Escreve estatísticas detalhadas"""
        main_df = report_data.main_data
        
        f.write("ESTATÍSTICAS DETALHADAS\n")
        f.write("-" * 40 + "\n")
        if report_data.dataset_config is not None:
            for key, col_config in report_data.dataset_config.processing_columns.items():
                if col_config.name in main_df.columns:
                    col_data = main_df[col_config.name]
                    
                    f.write(f"\n{col_config.display_name} ({col_config.unit}):\n")
                    f.write(f"  Média: {col_data.mean():,.2f}\n")
                    f.write(f"  Mediana: {col_data.median():,.2f}\n")
                    f.write(f"  Máximo: {col_data.max():,.2f}\n")
                    f.write(f"  Mínimo: {col_data.min():,.2f}\n")
                    f.write(f"  Desvio Padrão: {col_data.std():,.2f}\n")
                    f.write(f"  Registros Ativos: {(col_data > 0).sum():,} ({(col_data > 0).mean() * 100:.1f}%)\n")
                    f.write(f"  Registros Zero: {(col_data == 0).sum():,}\n")
                    
                    # Volume se disponível
                    vol_col = f'Vol_{key}'
                    if vol_col in main_df.columns:
                        volume_total = main_df[vol_col].sum()
                        f.write(f"  Volume Total: {volume_total:,.2f}\n")
                        f.write(f"  Volume Médio: {main_df[vol_col].mean():,.2f}\n")
            
        f.write("\n")
    
    def _write_data_summary(self, f, report_data: ReportData):
        """Escreve resumo dos dados por agregação"""
        f.write("RESUMOS DE AGREGAÇÃO\n")
        f.write("-" * 40 + "\n")
        
        for name, agg_df in report_data.aggregations.items():
            if not agg_df.empty:
                f.write(f"\n{name.replace('_', ' ').title()}:\n")
                f.write(f"  Registros: {len(agg_df)}\n")
                f.write(f"  Colunas: {len(agg_df.columns)}\n")
                
                # Mostra primeiras linhas se for pequeno
                if len(agg_df) <= 10:
                    f.write("  Dados:\n")
                    for _, row in agg_df.head().iterrows():
                        f.write(f"    {dict(row)}\n")
        
        f.write("\n")
    
    def _write_footer(self, f):
        """Escreve rodapé do relatório"""
        f.write("=" * 80 + "\n")
        f.write("Fim do Relatório\n")
        f.write("Sistema Modular de Análise de Dados v4.0\n")
        f.write("=" * 80 + "\n")


# =============================================================================
# SISTEMA PRINCIPAL
# =============================================================================

class DataAnalysisEngine:
    """Engine principal de análise de dados"""
    
    def __init__(self, config: ProcessingConfig = None):
        self.config = config or ProcessingConfig()
        self.logger = Logger.get_logger(self.__class__.__name__, self.config)
        
        # Componentes
        self.loader = DataLoader(self.config)
        self.validator = DataValidator(self.config)
        self.preprocessor = DataPreprocessor(self.config)
        self.processors = []
        self.reporters = {}
        
        # Estado
        self.raw_data = None
        self.processed_data = None
        self.aggregations = {}
        self.validation_result = None
        self.processing_metadata = {}
        self.current_dataset_config = config
    
    def add_processor(self, processor: BaseProcessor):
        """Adiciona processador à pipeline"""
        self.processors.append(processor)
        self.logger.info(f"Processador adicionado: {processor.__class__.__name__}")
    
    def add_reporter(self, name: str, reporter: BaseReporter):
        """Adiciona reporter"""
        self.reporters[name] = reporter
        self.logger.info(f"Reporter adicionado: {name}")
    
    def load_data(self, file_path: str, file_type: str = "excel", **kwargs) -> pd.DataFrame:
        """Carrega dados de arquivo"""
        self.logger.info(f"Carregando dados de: {file_path}")
        
        start_time = datetime.now()
        
        if file_type.lower() == "excel":
            self.raw_data = self.loader.load_excel(file_path, **kwargs)
        elif file_type.lower() == "csv":
            self.raw_data = self.loader.load_csv(file_path, **kwargs)
        else:
            raise ValueError(f"Tipo de arquivo não suportado: {file_type}")
        
        # Metadados de carregamento
        self.processing_metadata.update({
            'file_path': file_path,
            'file_type': file_type,
            'load_time': datetime.now(),
            'load_duration_seconds': (datetime.now() - start_time).total_seconds(),
            'original_shape': self.raw_data.shape,
            'original_columns': list(self.raw_data.columns)
        })
        
        return self.raw_data
    
    def process_data(self, dataset_config: DatasetConfig) -> pd.DataFrame:
        """Processa dados completos"""
        if self.raw_data is None:
            raise ValueError("Dados não carregados. Use load_data() primeiro.")
        
        self.logger.info("Iniciando processamento completo...")
        start_time = datetime.now()
        
        # Validação inicial
        self.validation_result = self.validator.validate_dataframe(self.raw_data, dataset_config)
        
        if not self.validation_result.is_valid:
            self.logger.error("Validação inicial falhou. Interrompendo processamento.")
            raise ValueError("Dados não passaram na validação inicial")
        
        # Pré-processamento
        self.processed_data = self.preprocessor.preprocess_dataframe(self.raw_data, dataset_config)
        
        # Executa processadores
        for processor in self.processors:
            self.logger.info(f"Executando: {processor.__class__.__name__}")
            if isinstance(processor, AggregationProcessor):
                # Processador de agregação retorna dicionário
                self.aggregations.update(processor.process(self.processed_data, dataset_config))
            else:
                # Processadores normais modificam os dados
                self.processed_data = processor.process(self.processed_data, dataset_config)
        
        # Validação final
        final_validation = self.validator.validate_dataframe(self.processed_data, dataset_config)
        
        # Metadados de processamento
        self.processing_metadata.update({
            'processing_time': datetime.now(),
            'processing_duration_seconds': (datetime.now() - start_time).total_seconds(),
            'final_shape': self.processed_data.shape,
            'final_columns': list(self.processed_data.columns),
            'processors_used': [p.__class__.__name__ for p in self.processors],
            'aggregations_generated': list(self.aggregations.keys()),
            'final_validation': final_validation.is_valid
        })
        
        self.logger.info(f"Processamento concluído em {(datetime.now() - start_time).total_seconds():.2f}s")
        return self.processed_data
    
    def generate_reports(self, output_configs: Dict[str, str]):
        """Gera relatórios usando reporters configurados"""
        if self.processed_data is None:
            raise ValueError("Dados não processados. Use process_data() primeiro.")
        
        report_data = ReportData(
            main_data=self.processed_data,
            aggregations=self.aggregations,
            validation_result=self.validation_result,
            processing_metadata=self.processing_metadata,
            dataset_config=self.current_dataset_config  # Precisa ser definido
        )
        
        for reporter_name, output_path in output_configs.items():
            if reporter_name in self.reporters:
                self.logger.info(f"Gerando relatório: {reporter_name} -> {output_path}")
                self.reporters[reporter_name].generate_report(report_data, output_path)
            else:
                self.logger.warning(f"Reporter não encontrado: {reporter_name}")
    
    def get_summary(self) -> Dict[str, Any]:
        """Retorna resumo do processamento"""
        if self.processed_data is None:
            return {"status": "not_processed"}
        
        return {
            "status": "processed",
            "original_records": self.processing_metadata.get('original_shape', (0, 0))[0],
            "final_records": len(self.processed_data),
            "columns": len(self.processed_data.columns),
            "processing_time": self.processing_metadata.get('processing_duration_seconds', 0),
            "validation_passed": self.validation_result.is_valid if self.validation_result else False,
            "aggregations": len(self.aggregations),
            "processors_used": self.processing_metadata.get('processors_used', [])
        }


# =============================================================================
# FACTORY PARA CONFIGURAÇÕES PRÉ-DEFINIDAS
# =============================================================================

class AnalysisFactory:
    """Factory para criar engines pré-configurados"""
    
    @staticmethod
    def create_petroleum_engine(config: ProcessingConfig = None) -> Tuple[DataAnalysisEngine, DatasetConfig]:
        """Cria engine para análise de dados de petróleo"""
        engine = DataAnalysisEngine(config)
        dataset_config = PetroleumConfig.get_petroleum_config()
        
        # Adiciona processadores específicos
        engine.add_processor(VolumeProcessor(engine.config))
        engine.add_processor(AggregationProcessor(engine.config))
        
        # Adiciona reporters
        engine.add_reporter('excel', ExcelReporter(engine.config))
        engine.add_reporter('csv', CSVReporter(engine.config))
        engine.add_reporter('text', TextReporter(engine.config))
        
        return engine, dataset_config
    
    @staticmethod
    def create_generic_engine(dataset_config: DatasetConfig, config: ProcessingConfig = None) -> DataAnalysisEngine:
        """Cria engine genérico"""
        engine = DataAnalysisEngine(config)
        
        # Processadores básicos
        engine.add_processor(AggregationProcessor(engine.config))
        
        # Reporters padrão
        engine.add_reporter('excel', ExcelReporter(engine.config))
        engine.add_reporter('csv', CSVReporter(engine.config))
        
        return engine


# =============================================================================
# INTERFACE DE LINHA DE COMANDO
# =============================================================================

def create_argument_parser():
    """Cria parser de argumentos da linha de comando"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='Sistema Modular de Análise de Dados v4.0',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemplos de uso:
  python data_analyzer.py dados.xlsx --tipo petroleum
  python data_analyzer.py dados.csv --tipo petroleum --excel resultado.xlsx --csv ./csvs
  python data_analyzer.py dados.xlsx --tipo petroleum --texto relatorio.txt --verbose
        """
    )
    
    # Argumentos principais
    parser.add_argument('arquivo', help='Arquivo de dados (Excel ou CSV)')
    parser.add_argument('--tipo', choices=['petroleum', 'generic'], default='petroleum',
                       help='Tipo de análise (default: petroleum)')
    
    # Parâmetros de entrada
    parser.add_argument('--aba', help='Nome da aba do Excel (opcional)')
    parser.add_argument('--formato', choices=['excel', 'csv'], default='excel',
                       help='Formato do arquivo de entrada')
    
    # Parâmetros de saída
    parser.add_argument('--excel', help='Caminho do relatório Excel')
    parser.add_argument('--csv', help='Diretório para arquivos CSV')
    parser.add_argument('--texto', help='Caminho do relatório em texto')
    
    # Opções de controle
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Logging detalhado')
    parser.add_argument('--resumo', action='store_true',
                       help='Exibe resumo no console')
    
    return parser


def main():
    """Função principal da linha de comando"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # Configuração
    config = ProcessingConfig()
    if args.verbose:
        config.log_level = "DEBUG"
    
    try:
        # Cria engine baseado no tipo
        if args.tipo == 'petroleum':
            engine, dataset_config = AnalysisFactory.create_petroleum_engine(config)
        else:
            # Para generic, seria necessário definir dataset_config
            raise ValueError("Tipo 'generic' requer configuração específica")
        
        engine.current_dataset_config = dataset_config  # Para uso nos relatórios
        
        # Carrega dados
        load_kwargs = {}
        if args.aba:
            load_kwargs['sheet_name'] = args.aba
        
        engine.load_data(args.arquivo, args.formato, **load_kwargs)
        
        # Processa dados
        engine.process_data(dataset_config)
        
        # Gera relatórios
        output_configs = {}
        if args.excel:
            output_configs['excel'] = args.excel
        if args.csv:
            output_configs['csv'] = args.csv
        if args.texto:
            output_configs['text'] = args.texto
        
        # Se nenhum output especificado, usa padrão
        if not output_configs:
            output_configs['excel'] = 'resultado_analise.xlsx'
        
        engine.generate_reports(output_configs)
        
        # Exibe resumo se solicitado
        if args.resumo:
            summary = engine.get_summary()
            print("\n" + "=" * 60)
            print("RESUMO DO PROCESSAMENTO")
            print("=" * 60)
            for key, value in summary.items():
                print(f"{key.replace('_', ' ').title()}: {value}")
        
        print("✓ Processamento concluído com sucesso!")
        
    except Exception as e:
        logger = Logger.get_logger("main")
        logger.error(f"Erro durante processamento: {str(e)}")
        logger.debug(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("Sistema Modular de Análise de Dados v4.0")
        print("Use --help para ver opções disponíveis")
        
        # Exemplo de uso programático
        print("\nExemplo de uso programático:")
        print("""
from data_analyzer import AnalysisFactory

# Cria engine para petróleo
engine, config = AnalysisFactory.create_petroleum_engine()

# Carrega e processa dados
engine.load_data('dados.xlsx')
engine.process_data(config)

# Gera relatórios
engine.generate_reports({
    'excel': 'resultado.xlsx',
    'csv': './csv_output',
    'text': 'relatorio.txt'
})
        """)
    else:
        main()

# =============================================================================
# REQUIREMENTS
# =============================================================================
"""
# requirements.txt
pandas>=1.5.0
numpy>=1.20.0
openpyxl>=3.0.0
pathlib2>=2.3.0
dataclasses>=0.6; python_version<"3.7"
"""