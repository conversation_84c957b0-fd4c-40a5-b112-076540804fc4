import pandas as pd
import os
import sys
import datetime
from Auxiliares.modules.logging_module import log_execution

# Importe aqui o dicionário de limites
from limitesPN import limites

@log_execution
def ler_excel(caminho_arquivo):
    """Lê o arquivo Excel e retorna um DataFrame"""
    print(f"Lendo arquivo Excel: {caminho_arquivo}")
    return pd.read_excel(caminho_arquivo)

@log_execution
def ler_ordem_projetos(caminho_arquivo):
    """Lê o arquivo de texto com a ordem dos projetos"""
    print(f"Lendo ordem de projetos: {caminho_arquivo}")
    resultado = []
    with open(caminho_arquivo, 'r') as arquivo:
        for linha in arquivo:
            linha_limpa = linha.strip()
            if linha_limpa and not linha_limpa.startswith('#'):
                resultado.append(linha_limpa)
    return resultado

@log_execution
def str_to_date(date_str):
    """Converte string para date object"""
    try:
        return datetime.datetime.strptime(date_str.strip(), "%Y-%m-%d").date()
    except:
        return None

@log_execution
def get_limit_for_date(limite_valor, data_consulta):
    """
    Retorna o limite numérico correspondente à data_consulta,
    dado que limite_valor pode ser:
      - Um inteiro/float fixo
      - Um dicionário com chaves que podem definir intervalos de data
    """
    # Garante que é datetime.date
    if isinstance(data_consulta, pd.Timestamp):
        data_consulta = data_consulta.date()

    # Se o valor for numérico (int ou float), simplesmente retorne
    if isinstance(limite_valor, (int, float)):
        return limite_valor

    # Caso seja um dicionário, precisamos verificar cada faixa
    # Exemplo de chaves possíveis:
    #   ":2029-01-01"   -> até 2029-01-01 (inclusive)
    #   "2029-02-01:"   -> a partir de 2029-02-01 (inclusive)
    #   "2029-01-01:2029-12-01" -> entre 2029-01-01 e 2029-12-01 (inclusive)
    #   "2030-01-01"    -> somente na data 2030-01-01
    #   etc.
    for intervalo_str, valor_max in limite_valor.items():
        # Tentar detectar se é um intervalo ou data única
        if ":" in intervalo_str:
            partes = intervalo_str.split(":")
            # Exemplos de partes:
            #   ["", "2029-01-01"] -> significa :2029-01-01
            #   ["2029-02-01", ""] -> significa 2029-02-01:
            #   ["2029-01-01", "2029-12-01"] -> 2029-01-01:2029-12-01
            start_str, end_str = partes[0].strip(), partes[1].strip()

            start_date = str_to_date(start_str) if start_str else None
            end_date   = str_to_date(end_str)   if end_str else None

            # Verifica se data_consulta está dentro do intervalo
            # Se start_date for None, significa sem limite inferior
            # Se end_date for None, significa sem limite superior
            if (start_date is None or data_consulta >= start_date) and \
               (end_date is None   or data_consulta <= end_date):
                return valor_max

        else:
            # Pode ser uma data exata (ex: "2030-01-01")
            data_limite = str_to_date(intervalo_str)
            if data_limite == data_consulta:
                return valor_max

    # Se não encontrar nenhum intervalo correspondente, pode retornar None
    # ou algum valor default. Aqui retornaremos None, indicando "não definido".
    return None

@log_execution
def apply_priority_sorting(df, sort_by, priority=None):
    """
    Aplica ordenação prioritária ao DataFrame com base nas regras fornecidas.
    Ordena primeiro por sort_by (decrescente), depois por prioridades definidas (ascendente).

    Suporta múltiplos valores em uma mesma coluna aplicando a prioridade mais alta (menor número)
    quando um item está associado a múltiplos valores da coluna de prioridade.
    """
    # Criar cópia para evitar warnings de modificação
    df_sorted = df.copy()

    # Se não houver prioridade, ordenar apenas por sort_for
    if not priority:
        return df_sorted.sort_values(sort_by, ascending=False)

    # Processar regras de prioridade
    sort_columns = sort_by.copy()
    ascending = [False] * len(sort_by)

    for col, priority_str in priority.items():
        # Verificar se a coluna está presente no DataFrame
        if col not in df_sorted.columns:
            continue

        # Criar dicionário de prioridades para a coluna
        priority_dict = {}
        for pair in priority_str.split(','):
            key, val = pair.strip().split('=')
            priority_dict[key] = int(val)

        # Função para atribuir a prioridade mais alta (número mais baixo)
        # se houver múltiplos valores
        @log_execution
        def get_highest_priority(value):
            # Se for uma lista ou conjunto, encontrar a prioridade mais alta
            if isinstance(value, (list, set)):
                priorities = [priority_dict.get(val, 5000) for val in value]
                return min(priorities) if priorities else 5000
            # Se for um valor único
            else:
                return priority_dict.get(value, 5000)

        # Adicionar coluna temporária com valores de prioridade
        priority_col = f"{col}_priority"
        df_sorted[priority_col] = df_sorted[col].apply(get_highest_priority)

        # Adicionar à lista de colunas para ordenação
        sort_columns.insert(0, priority_col)
        ascending.insert(0, True)  # Prioridades menores primeiro

    # Ordenar e remover colunas temporárias
    df_sorted = df_sorted.sort_values(sort_columns, ascending=ascending)
    df_sorted = df_sorted.drop(columns=[c for c in df_sorted.columns if '_priority' in c])

    return df_sorted

@log_execution
def calcular_rateio_campo_zp(subDF, data_row, variavel_coluna):
    """
    Calcula o rateio para Campo e ZP baseado na proporção da variável especificada.
    Retorna um dicionário com as proporções para cada combinação Campo/ZP.
    Lida com casos onde Campo ou ZP podem ser listas.
    """
    # Filtrar dados para a data específica
    mask_data = subDF["Date"] == data_row
    dados_data = subDF.loc[mask_data].copy()

    # Função auxiliar para obter valores únicos de uma coluna que pode conter listas
    @log_execution
    def get_unique_values(column_data):
        unique_values = set()
        for item in column_data:
            if isinstance(item, list):
                unique_values.update(item)
            else:
                unique_values.add(item)
        return list(unique_values)

    # Verificar quantos campos únicos existem nesta data
    campos_unicos = get_unique_values(dados_data["Campo"])

    if len(campos_unicos) > 1:
        # Múltiplos campos - calcular rateio por Campo

        # Expandir dados se Campo contiver listas
        expanded_data = []
        for _, row in dados_data.iterrows():
            campo_values = row["Campo"] if isinstance(row["Campo"], list) else [row["Campo"]]
            for campo in campo_values:
                new_row = row.copy()
                new_row["Campo"] = campo
                expanded_data.append(new_row)

        # Criar DataFrame expandido
        if expanded_data:
            expanded_df = pd.DataFrame(expanded_data)
            agrupamento = expanded_df.groupby("Campo")[variavel_coluna].sum()
        else:
            agrupamento = dados_data.groupby("Campo")[variavel_coluna].sum()

        total = agrupamento.sum()

        if total > 0:
            proporcoes = (agrupamento / total).to_dict()
            return {'tipo': 'Campo', 'proporcoes': proporcoes}
        else:
            # Se total é zero, dividir igualmente
            proporcoes = {campo: 1.0/len(campos_unicos) for campo in agrupamento.index}
            return {'tipo': 'Campo', 'proporcoes': proporcoes}

    else:
        # Apenas um campo - verificar ZP
        zps_unicos = get_unique_values(dados_data["ZP"])

        if len(zps_unicos) > 1:
            # Múltiplas ZPs - calcular rateio por ZP

            # Expandir dados se ZP contiver listas
            expanded_data = []
            for _, row in dados_data.iterrows():
                zp_values = row["ZP"] if isinstance(row["ZP"], list) else [row["ZP"]]
                for zp in zp_values:
                    new_row = row.copy()
                    new_row["ZP"] = zp
                    expanded_data.append(new_row)

            # Criar DataFrame expandido
            if expanded_data:
                expanded_df = pd.DataFrame(expanded_data)
                agrupamento = expanded_df.groupby("ZP")[variavel_coluna].sum()
            else:
                agrupamento = dados_data.groupby("ZP")[variavel_coluna].sum()

            total = agrupamento.sum()

            if total > 0:
                proporcoes = (agrupamento / total).to_dict()
                return {'tipo': 'ZP', 'proporcoes': proporcoes}
            else:
                # Se total é zero, dividir igualmente
                proporcoes = {zp: 1.0/len(zps_unicos) for zp in agrupamento.index}
                return {'tipo': 'ZP', 'proporcoes': proporcoes}
        else:
            # Apenas uma ZP - sem rateio necessário
            return {'tipo': 'None', 'proporcoes': {}}

@log_execution
def criar_linhas_ajuste_rateadas(row_poco, data_row, reduce_amount, rateio_info, classe_ajuste,
                                projeto_value, iupi_value, plataforma, teorCO2=0.0, campo_zp_nzp_mapping=None):
    """
    Cria linhas de ajuste baseadas no rateio calculado.
    """
    adjustment_rows = []

    # Função auxiliar para extrair valor de lista se necessário
    @log_execution
    def extract_value(value):
        if isinstance(value, list):
            return value[0] if value else ""
        return value

    if rateio_info['tipo'] == 'None':
        # Sem rateio - criar apenas uma linha
        new_line = criar_linha_ajuste_base(row_poco, data_row, reduce_amount, classe_ajuste,
                                         projeto_value, iupi_value, plataforma, teorCO2)

        # Verificar se new_line não é None antes de continuar
        if new_line is not None:
            # Extrair valores de listas
            for key in ["Campo", "ZP", "Versao", "Cenario"]:
                if key in new_line and isinstance(new_line[key], list):
                    new_line[key] = extract_value(new_line[key])

            # Garantir que NZP corresponda à combinação Campo-ZP
            campo = new_line.get("Campo")
            zp = new_line.get("ZP")
            if campo_zp_nzp_mapping and (campo, zp) in campo_zp_nzp_mapping:
                new_line["NZP"] = campo_zp_nzp_mapping[(campo, zp)]

            adjustment_rows.append(new_line)

    else:
        # Com rateio - criar uma linha para cada proporção
        for chave, proporcao in rateio_info['proporcoes'].items():
            reduce_rateado = reduce_amount * proporcao

            # Criar linha base
            new_line = criar_linha_ajuste_base(row_poco, data_row, reduce_rateado, classe_ajuste,
                                             projeto_value, iupi_value, plataforma, teorCO2)

            # Verificar se new_line não é None antes de continuar
            if new_line is not None:
                # Extrair valores de listas
                for key in ["Campo", "ZP", "Versao", "Cenario"]:
                    if key in new_line and isinstance(new_line[key], list):
                        new_line[key] = extract_value(new_line[key])

                # Ajustar Campo ou ZP conforme o tipo de rateio
                if rateio_info['tipo'] == 'Campo':
                    new_line["Campo"] = chave
                    # Se ZP já for um valor único, podemos tentar definir NZP
                    if not isinstance(new_line["ZP"], list):
                        campo = chave
                        zp = new_line["ZP"]
                        if campo_zp_nzp_mapping and (campo, zp) in campo_zp_nzp_mapping:
                            new_line["NZP"] = campo_zp_nzp_mapping[(campo, zp)]

                elif rateio_info['tipo'] == 'ZP':
                    new_line["ZP"] = chave
                    # Se Campo já for um valor único, podemos tentar definir NZP
                    if not isinstance(new_line["Campo"], list):
                        campo = new_line["Campo"]
                        zp = chave
                        if campo_zp_nzp_mapping and (campo, zp) in campo_zp_nzp_mapping:
                            new_line["NZP"] = campo_zp_nzp_mapping[(campo, zp)]

                adjustment_rows.append(new_line)

    return adjustment_rows

@log_execution
def criar_linha_ajuste_base(row_poco, data_row, reduce_amount, classe_ajuste,
                          projeto_value, iupi_value, plataforma, teorCO2=0.0):
    """
    Cria a estrutura base de uma linha de ajuste.
    """
    # Função auxiliar para extrair valor de lista se necessário
    @log_execution
    def extract_value(value):
        if isinstance(value, list):
            return value[0] if value else ""
        return value

    # Obter valores para Versao, Cenario, Campo, ZP, NZP
    versao = extract_value(row_poco.get("Versao", ""))
    cenario = extract_value(row_poco.get("Cenario", ""))
    campo = extract_value(row_poco.get("Campo", ""))
    zp = extract_value(row_poco.get("ZP", ""))
    nzp = extract_value(row_poco.get("NZP", ""))

    # Caso: Ajuste de água produzida (Qw)
    if classe_ajuste == "Ajuste Qw":
        BSW_i = row_poco.get("BSW", 0.0)
        RGO_i = row_poco.get("RGO", 0.0)

        Qo_reduce = reduce_amount * (1 - BSW_i) / BSW_i if BSW_i != 0 and BSW_i != 1.0 else 0
        Qg_reduce = Qo_reduce * RGO_i
        Qgco2_reduce = teorCO2 * Qg_reduce
        Qghc_reduce = Qg_reduce - Qgco2_reduce

        return {
            "Versao": versao,
            "Cenario": cenario,
            "Campo": campo,
            "ZP": zp,
            "NZP": nzp,
            "UEP": plataforma,
            "Projeto": projeto_value,
            "IUPI": iupi_value,
            "Well": extract_value(row_poco.get("Well", "")),
            "Date": data_row,
            "Classe": classe_ajuste,
            "Qo Pot(m3/day)": -Qo_reduce,
            "Qw Pot(m3/day)": -reduce_amount,
            "Qg Pot(mil m3/day)": -Qg_reduce,
            "Qgco2 Pot(mil m3/day)": -Qgco2_reduce,
            "Qghc Pot(mil m3/day)": -Qghc_reduce,
            "Qwi Pot(m3/day)": 0.0,
            "Qgi Pot(mil m3/day)": 0.0,
            "Qgl Pot(mil m3/day)": 0.0,
            "Qgco2i Pot(mil m3/day)": 0.0,
            "Qghci Pot(mil m3/day)": 0.0,
        }

    # Caso: Ajuste de gás produzido (Qg)
    elif classe_ajuste == "Ajuste Qg":
        RGO_i = row_poco.get("RGO", 0.0)

        Qo_reduce = reduce_amount / RGO_i if RGO_i > 0 else 0
        Qw_reduce = Qo_reduce * (row_poco.get("Qw Pot(m3/day)", 0) / row_poco.get("Qo Pot(m3/day)", 1)) if row_poco.get("Qo Pot(m3/day)", 0) > 0 else 0
        Qgco2_reduce = reduce_amount * teorCO2
        Qghc_reduce = reduce_amount - Qgco2_reduce

        return {
            "Versao": versao,
            "Cenario": cenario,
            "Campo": campo,
            "ZP": zp,
            "NZP": nzp,
            "UEP": plataforma,
            "Projeto": projeto_value,
            "IUPI": iupi_value,
            "Well": extract_value(row_poco.get("Well", "")),
            "Date": data_row,
            "Classe": classe_ajuste,
            "Qo Pot(m3/day)": -Qo_reduce,
            "Qw Pot(m3/day)": -Qw_reduce,
            "Qg Pot(mil m3/day)": -reduce_amount,
            "Qgco2 Pot(mil m3/day)": -Qgco2_reduce,
            "Qghc Pot(mil m3/day)": -Qghc_reduce,
            "Qwi Pot(m3/day)": 0.0,
            "Qgi Pot(mil m3/day)": 0.0,
            "Qgl Pot(mil m3/day)": 0.0,
            "Qgco2i Pot(mil m3/day)": 0.0,
            "Qghci Pot(mil m3/day)": 0.0,
        }

    # Caso: Ajuste de líquido produzido (Ql)
    elif classe_ajuste == "Ajuste Ql":
        BSW_i = row_poco.get("BSW", 0.0)
        RGO_i = row_poco.get("RGO", 0.0)

        # Para Ql, reduzimos proporcionalmente Qo e Qw baseado no BSW
        Qo_reduce = reduce_amount * (1 - BSW_i)
        Qw_reduce = reduce_amount * BSW_i
        Qg_reduce = Qo_reduce * RGO_i
        Qgco2_reduce = teorCO2 * Qg_reduce
        Qghc_reduce = Qg_reduce - Qgco2_reduce

        return {
            "Versao": versao,
            "Cenario": cenario,
            "Campo": campo,
            "ZP": zp,
            "NZP": nzp,
            "UEP": plataforma,
            "Projeto": projeto_value,
            "IUPI": iupi_value,
            "Well": extract_value(row_poco.get("Well", "")),
            "Date": data_row,
            "Classe": classe_ajuste,
            "Qo Pot(m3/day)": -Qo_reduce,
            "Qw Pot(m3/day)": -Qw_reduce,
            "Qg Pot(mil m3/day)": -Qg_reduce,
            "Qgco2 Pot(mil m3/day)": -Qgco2_reduce,
            "Qghc Pot(mil m3/day)": -Qghc_reduce,
            "Qwi Pot(m3/day)": 0.0,
            "Qgi Pot(mil m3/day)": 0.0,
            "Qgl Pot(mil m3/day)": 0.0,
            "Qgco2i Pot(mil m3/day)": 0.0,
            "Qghci Pot(mil m3/day)": 0.0,
        }

    # Caso: Ajuste de óleo produzido (Qo)
    elif classe_ajuste == "Ajuste Qo":
        BSW_i = row_poco.get("BSW", 0.0)
        RGO_i = row_poco.get("RGO", 0.0)

        # Calculando Qw_reduce com base em BSW e Qo
        # Se BSW = 0, não há água a ser reduzida
        # Se BSW > 0, calculamos Qw_reduce = Qo_reduce * BSW / (1-BSW)
        Qw_reduce = reduce_amount * BSW_i / (1 - BSW_i) if BSW_i < 1.0 else 0
        Qg_reduce = reduce_amount * RGO_i
        Qgco2_reduce = teorCO2 * Qg_reduce
        Qghc_reduce = Qg_reduce - Qgco2_reduce

        return {
            "Versao": versao,
            "Cenario": cenario,
            "Campo": campo,
            "ZP": zp,
            "NZP": nzp,
            "UEP": plataforma,
            "Projeto": projeto_value,
            "IUPI": iupi_value,
            "Well": extract_value(row_poco.get("Well", "")),
            "Date": data_row,
            "Classe": classe_ajuste,
            "Qo Pot(m3/day)": -reduce_amount,
            "Qw Pot(m3/day)": -Qw_reduce,
            "Qg Pot(mil m3/day)": -Qg_reduce,
            "Qgco2 Pot(mil m3/day)": -Qgco2_reduce,
            "Qghc Pot(mil m3/day)": -Qghc_reduce,
            "Qwi Pot(m3/day)": 0.0,
            "Qgi Pot(mil m3/day)": 0.0,
            "Qgl Pot(mil m3/day)": 0.0,
            "Qgco2i Pot(mil m3/day)": 0.0,
            "Qghci Pot(mil m3/day)": 0.0,
        }

    # Caso: Ajuste de injeção de água (Qwi)
    elif classe_ajuste == "Ajuste Qwi":
        return {
            "Versao": versao,
            "Cenario": cenario,
            "Campo": campo,
            "ZP": zp,
            "NZP": nzp,
            "UEP": plataforma,
            "Projeto": projeto_value,
            "IUPI": iupi_value,
            "Well": extract_value(row_poco.get("Well", "")),
            "Date": data_row,
            "Classe": classe_ajuste,
            "Qo Pot(m3/day)": 0.0,
            "Qw Pot(m3/day)": 0.0,
            "Qg Pot(mil m3/day)": 0.0,
            "Qgco2 Pot(mil m3/day)": 0.0,
            "Qghc Pot(mil m3/day)": 0.0,
            "Qwi Pot(m3/day)": -reduce_amount,  # Ajuste na injeção de água
            "Qgi Pot(mil m3/day)": 0.0,
            "Qgl Pot(mil m3/day)": 0.0,
            "Qgco2i Pot(mil m3/day)": 0.0,
            "Qghci Pot(mil m3/day)": 0.0,
        }

    # Caso: Ajuste de injeção de gás (Qgi) - Adicionado para completude
    elif classe_ajuste == "Ajuste Qgi":
        return {
            "Versao": versao,
            "Cenario": cenario,
            "Campo": campo,
            "ZP": zp,
            "NZP": nzp,
            "UEP": plataforma,
            "Projeto": projeto_value,
            "IUPI": iupi_value,
            "Well": extract_value(row_poco.get("Well", "")),
            "Date": data_row,
            "Classe": classe_ajuste,
            "Qo Pot(m3/day)": 0.0,
            "Qw Pot(m3/day)": 0.0,
            "Qg Pot(mil m3/day)": 0.0,
            "Qgco2 Pot(mil m3/day)": 0.0,
            "Qghc Pot(mil m3/day)": 0.0,
            "Qwi Pot(m3/day)": 0.0,
            "Qgi Pot(mil m3/day)": -reduce_amount,  # Ajuste na injeção de gás
            "Qgl Pot(mil m3/day)": 0.0,
            "Qgco2i Pot(mil m3/day)": -reduce_amount * teorCO2,  # Ajuste no CO2 injetado
            "Qghci Pot(mil m3/day)": -reduce_amount * (1 - teorCO2),  # Ajuste no HC injetado
        }

    # Se nenhum caso for correspondido, retornar None e emitir alerta
    else:
        print(f"AVISO: Tipo de ajuste não reconhecido: {classe_ajuste}")
        return None

@log_execution
def flatten_and_unique(x):
    """
    Função para achatar e obter valores únicos de uma série,
    lidando corretamente com casos onde os valores já são listas.
    """
    result = []
    for item in x:
        if isinstance(item, list):
            result.extend(item)
        else:
            result.append(item)
    return list(set(result))

@log_execution
def create_campo_zp_nzp_mapping(df):
    """
    Cria um mapeamento de combinações (Campo, ZP) para seus valores NZP correspondentes.
    """
    mapping = {}
    for _, row in df.iterrows():
        campo = row.get("Campo")
        zp = row.get("ZP")
        nzp = row.get("NZP")

        # Extrair valores de listas se necessário
        if isinstance(campo, list) and campo:
            campo = campo[0]
        if isinstance(zp, list) and zp:
            zp = zp[0]
        if isinstance(nzp, list) and nzp:
            nzp = nzp[0]

        if campo and zp and nzp:  # Garantir que todos os valores existam
            key = (campo, zp)
            mapping[key] = nzp

    return mapping

@log_execution
def check_limits_for_platform_qw(df_plataforma, plataforma, projeto_in, coluna, priority=None):
    """
    Agrupa df_plataforma por 'Date', soma as colunas numéricas e compara
    com os limites de Qw. Ajustes são aplicados por poço (Well), mantendo consistência
    entre diferentes Campos/ZPs onde o mesmo poço aparece.
    """

    # Criar mapeamento Campo-ZP para NZP
    campo_zp_nzp_mapping = create_campo_zp_nzp_mapping(df_plataforma)

    if plataforma not in limites:
        print(f"UEP '{plataforma}' não encontrada em limitesPN.py")
        return df_plataforma

    # Garante que a coluna 'Date' é datetime.date
    if not pd.api.types.is_datetime64_any_dtype(df_plataforma['Date']):
        df_plataforma['Date'] = pd.to_datetime(df_plataforma['Date']).dt.date

    # Se a coluna "Classe" não existir, crie e marque tudo como Original
    if "Classe" not in df_plataforma.columns:
        df_plataforma["Classe"] = "Original"

    # Colunas de interesse para verificar o limite (Qw)
    cols_existentes = [col for col in ["Qw Pot(m3/day)"] if col in df_plataforma.columns]
    if not cols_existentes:
        print(f"Nenhuma coluna 'Qw Pot(m3/day)' encontrada para a plataforma {plataforma}.")
        return df_plataforma

    # Agrupa por Date e soma as colunas de interesse
    df_agg = df_plataforma.groupby("Date")[cols_existentes].sum().reset_index()

    # Para cada data, verifica se Qw excede o limite
    for idx, row in df_agg.iterrows():
        data_row = row["Date"]

        limit_Qw = get_limit_for_date(limites[plataforma].get("Qw"), data_row)
        if limit_Qw is None:
            print(f"Não foi encontrado limite para a plataforma '{plataforma}' na data {data_row}")
            continue

        valor_somado_Qw = row.get("Qw Pot(m3/day)", 0.0)
        exceed = valor_somado_Qw - limit_Qw

        if exceed > 0.01:
            # 1) Filtra as linhas para a data em questão
            mask_data = (df_plataforma["Date"] == data_row)
            subDF = df_plataforma.loc[mask_data].copy()

            # 2) Primeiro, agrupa por poço para calcular BSW total do poço
            # Incluímos uma agregação especial para Campo e ZP para preservar todos os valores
            agg_dict = {
                "Qo Pot(m3/day)": "sum",
                "Qw Pot(m3/day)": "sum",
                "Qg Pot(mil m3/day)": "sum",
                "Qgco2 Pot(mil m3/day)": "sum"
            }

            # Adiciona Versao e Cenario à agregação
            for col in ["Campo", "ZP", "Versao", "Cenario"]:
                if col in subDF.columns:
                    agg_dict[col] = flatten_and_unique
            # # Adicionamos agregação para Campo e ZP se existirem
            # if "Campo" in subDF.columns:
            #     agg_dict["Campo"] = flatten_and_unique
            # if "ZP" in subDF.columns:
            #     agg_dict["ZP"] = flatten_and_unique

            well_agg = subDF.groupby(["UEP", "Well", "Date"]).agg(agg_dict).reset_index()

            # 3) Calcula BSW e RGO por poço
            well_agg["Qo_i"] = well_agg["Qo Pot(m3/day)"]
            well_agg["Qw_i"] = well_agg["Qw Pot(m3/day)"]
            well_agg["Qg_i"] = well_agg["Qg Pot(mil m3/day)"]
            well_agg["Qgco2_i"] = well_agg["Qgco2 Pot(mil m3/day)"]

            well_agg["BSW"] = well_agg.apply(
                lambda x: x["Qw_i"] / (x["Qo_i"] + x["Qw_i"]) if (x["Qo_i"] + x["Qw_i"]) > 0 else 0.0,
                axis=1
            )
            well_agg["RGO"] = well_agg.apply(
                lambda x: x["Qg_i"] / x["Qo_i"] if x["Qo_i"] > 0 else 0.0,
                axis=1
            )

            # 4) Ordena por BSW para priorizar cortes, agora com suporte a múltiplos valores de Campo/ZP
            well_agg = apply_priority_sorting(well_agg, ['BSW'], priority)

            # 5) Calcula quanto cortar de cada poço
            adjustment_rows = []
            still_to_cut = exceed

            for _, row_poco in well_agg.iterrows():
                if still_to_cut <= 0.01:
                    break

                well_name = row_poco["Well"]
                Qw_i = row_poco["Qw_i"]

                if Qw_i <= 0:
                    continue

                reduce_amount = min(Qw_i, still_to_cut)

                # Para este poço, calcular o rateio entre seus Campos/ZPs
                well_mask = (subDF["Well"] == well_name)
                well_data = subDF.loc[well_mask].copy()

                # Calcular o rateio para este poço específico
                rateio_info = calcular_rateio_campo_zp(well_data, data_row, "Qw Pot(m3/day)")

                # Determina Projeto e IUPI com base em coluna
                try:
                    if coluna == "Projeto":
                        projeto_value = projeto_in
                        iupi_value = df_plataforma.loc[df_plataforma['Projeto'] == projeto_in, 'IUPI'].iloc[0]
                    else:
                        iupi_value = projeto_in
                        projeto_value = df_plataforma.loc[df_plataforma['IUPI'] == projeto_in, 'Projeto'].iloc[0]
                except IndexError:
                    print("ERROR: Nao foi possivel determinar o IUPI ou/e Projeto.")
                    return df_plataforma

                # Calcular teorCO2
                teorCO2 = row_poco["Qgco2_i"] / row_poco["Qg_i"] if row_poco["Qg_i"] > 0 else 0.0

                # Convertemos row_poco para dicionário para compatibilidade
                row_poco_dict = row_poco.to_dict()

                # Criar linhas de ajuste com rateio para este poço
                new_lines = criar_linhas_ajuste_rateadas(
                    row_poco_dict, data_row, reduce_amount, rateio_info, "Ajuste Qw",
                    projeto_value, iupi_value, plataforma, teorCO2, campo_zp_nzp_mapping
                )

                adjustment_rows.extend(new_lines)
                still_to_cut -= reduce_amount

            if adjustment_rows:
                # Garante que todas as colunas existem no DataFrame
                for col in ["Versao", "Cenario", "Campo", "ZP", "NZP", "UEP", "Well", "Date", "Classe", "Projeto", "IUPI",
                           "Qo Pot(m3/day)", "Qw Pot(m3/day)", "Qg Pot(mil m3/day)", "Qgco2 Pot(mil m3/day)",
                           "Qghc Pot(mil m3/day)", "Qwi Pot(m3/day)", "Qgi Pot(mil m3/day)", "Qgl Pot(mil m3/day)",
                           "Qgco2i Pot(mil m3/day)", "Qghci Pot(mil m3/day)"]:
                    if col not in df_plataforma.columns:
                        df_plataforma[col] = 0.0

                df_plataforma = pd.concat(
                    [df_plataforma, pd.DataFrame(adjustment_rows)],
                    ignore_index=True
                )

    return df_plataforma

@log_execution
def check_limits_for_platform_qg(df_plataforma, plataforma, projeto_in, coluna, priority=None):
    """
    Agrupa df_plataforma por 'Date', soma as colunas numéricas e compara
    com os limites de Qg. Ajustes são aplicados por poço (Well), mantendo consistência
    entre diferentes Campos/ZPs onde o mesmo poço aparece.
    """

    # Criar mapeamento Campo-ZP para NZP
    campo_zp_nzp_mapping = create_campo_zp_nzp_mapping(df_plataforma)

    if plataforma not in limites:
        print(f"UEP '{plataforma}' não encontrada em limitesPN.py")
        return df_plataforma

    # Converter coluna Date se necessário
    if not pd.api.types.is_datetime64_any_dtype(df_plataforma['Date']):
        df_plataforma['Date'] = pd.to_datetime(df_plataforma['Date']).dt.date

    # Criar coluna Classe se não existir
    if "Classe" not in df_plataforma.columns:
        df_plataforma["Classe"] = "Original"

    # Agrupar e verificar limites
    df_agg = df_plataforma.groupby("Date").agg(
        Qg=("Qg Pot(mil m3/day)", "sum"),
        Qgl=("Qgl Pot(mil m3/day)", "sum")
    ).reset_index()

    df_agg["Qgt"] = df_agg["Qg"] + df_agg["Qgl"]

    for idx, row in df_agg.iterrows():
        data_row = row["Date"]
        limit_Qg = get_limit_for_date(limites[plataforma].get("Qg"), data_row)

        if limit_Qg is None:
            print(f"Sem limite Qg para {plataforma} em {data_row}")
            continue

        valor_Qgt = row.get("Qgt", 0.0)
        exceed = valor_Qgt - limit_Qg

        if exceed > 0.01:
            # 1) Filtra as linhas para a data em questão
            mask_data = (df_plataforma["Date"] == data_row)
            subDF = df_plataforma.loc[mask_data].copy()

            # 2) Primeiro, agrupa por poço para calcular RGO total do poço
            # Incluímos uma agregação especial para Campo e ZP para preservar todos os valores
            agg_dict = {
                "Qo Pot(m3/day)": "sum",
                "Qw Pot(m3/day)": "sum",
                "Qg Pot(mil m3/day)": "sum",
                "Qgco2 Pot(mil m3/day)": "sum",
                "Qghc Pot(mil m3/day)": "sum"
            }

            # Adiciona Versao e Cenario à agregação
            for col in ["Campo", "ZP", "Versao", "Cenario"]:
                if col in subDF.columns:
                    agg_dict[col] = flatten_and_unique
            # # Adicionamos agregação para Campo e ZP se existirem
            # if "Campo" in subDF.columns:
            #     agg_dict["Campo"] = flatten_and_unique
            # if "ZP" in subDF.columns:
            #     agg_dict["ZP"] = flatten_and_unique

            well_agg = subDF.groupby(["UEP", "Well", "Date"]).agg(agg_dict).reset_index()

            # 3) Calcula RGO por poço
            well_agg["Qo_i"] = well_agg["Qo Pot(m3/day)"]
            well_agg["Qg_i"] = well_agg["Qg Pot(mil m3/day)"]
            well_agg["Qgco2_i"] = well_agg["Qgco2 Pot(mil m3/day)"]

            # Calcular RGO
            well_agg["RGO"] = well_agg.apply(
                lambda x: x["Qg_i"] / x["Qo_i"] if x["Qo_i"] > 0 else float('inf'),
                axis=1
            )

            # 4) Ordena por RGO para priorizar cortes, agora com suporte a múltiplos valores de Campo/ZP
            well_agg = apply_priority_sorting(well_agg, ['RGO'], priority)

            # 5) Calcula quanto cortar de cada poço
            adjustment_rows = []
            still_to_cut = exceed

            for _, row_poco in well_agg.iterrows():
                if still_to_cut <= 0.01:
                    break

                well_name = row_poco["Well"]
                Qg_i = row_poco["Qg_i"]

                if Qg_i <= 0:
                    continue

                reduce_amount = min(Qg_i, still_to_cut)

                # Para este poço, calcular o rateio entre seus Campos/ZPs
                well_mask = (subDF["Well"] == well_name)
                well_data = subDF.loc[well_mask].copy()

                # Calcular o rateio para este poço específico
                rateio_info = calcular_rateio_campo_zp(well_data, data_row, "Qg Pot(mil m3/day)")

                # Determina Projeto e IUPI com base em coluna
                try:
                    if coluna == "Projeto":
                        projeto_value = projeto_in
                        iupi_value = df_plataforma.loc[df_plataforma['Projeto'] == projeto_in, 'IUPI'].iloc[0]
                    else:
                        iupi_value = projeto_in
                        projeto_value = df_plataforma.loc[df_plataforma['IUPI'] == projeto_in, 'Projeto'].iloc[0]
                except IndexError:
                    print("ERROR: Nao foi possivel determinar o IUPI ou/e Projeto.")
                    return df_plataforma

                # Calcular teorCO2
                teorCO2 = row_poco["Qgco2_i"] / row_poco["Qg_i"] if row_poco["Qg_i"] > 0 else 0.0

                # Convertemos row_poco para dicionário para compatibilidade
                row_poco_dict = row_poco.to_dict()

                # Criar linhas de ajuste com rateio para este poço
                new_lines = criar_linhas_ajuste_rateadas(
                    row_poco_dict, data_row, reduce_amount, rateio_info, "Ajuste Qg",
                    projeto_value, iupi_value, plataforma, teorCO2, campo_zp_nzp_mapping
                )

                adjustment_rows.extend(new_lines)
                still_to_cut -= reduce_amount

            if adjustment_rows:
                # Garante que todas as colunas existem no DataFrame
                for col in ["Versao", "Cenario", "Campo", "ZP", "NZP", "UEP", "Well", "Date", "Classe", "Projeto", "IUPI",
                           "Qo Pot(m3/day)", "Qw Pot(m3/day)", "Qg Pot(mil m3/day)", "Qgco2 Pot(mil m3/day)",
                           "Qghc Pot(mil m3/day)", "Qwi Pot(m3/day)", "Qgi Pot(mil m3/day)", "Qgl Pot(mil m3/day)",
                           "Qgco2i Pot(mil m3/day)", "Qghci Pot(mil m3/day)"]:
                    if col not in df_plataforma.columns:
                        df_plataforma[col] = 0.0

                df_plataforma = pd.concat(
                    [df_plataforma, pd.DataFrame(adjustment_rows)],
                    ignore_index=True
                )

    return df_plataforma

@log_execution
def check_limits_for_platform_ql(df_plataforma, plataforma, projeto_in, coluna, priority=None):
    """
    Agrupa df_plataforma por 'Date', soma Qo + Qw e compara com o limite de Ql.
    Ajustes são aplicados por poço (Well), mantendo consistência entre diferentes Campos/ZPs
    onde o mesmo poço aparece.
    """

    # Criar mapeamento Campo-ZP para NZP
    campo_zp_nzp_mapping = create_campo_zp_nzp_mapping(df_plataforma)

    if plataforma not in limites:
        print(f"UEP '{plataforma}' não encontrada em limitesPN.py")
        return df_plataforma

    # Converter coluna Date se necessário
    if not pd.api.types.is_datetime64_any_dtype(df_plataforma['Date']):
        df_plataforma['Date'] = pd.to_datetime(df_plataforma['Date']).dt.date

    # Criar coluna Classe se não existir
    if "Classe" not in df_plataforma.columns:
        df_plataforma["Classe"] = "Original"

    # Agrupar e verificar limites
    df_agg = df_plataforma.groupby("Date").agg(
        Qo=("Qo Pot(m3/day)", "sum"),
        Qw=("Qw Pot(m3/day)", "sum")
    ).reset_index()

    df_agg["Ql"] = df_agg["Qo"] + df_agg["Qw"]

    for idx, row in df_agg.iterrows():
        data_row = row["Date"]
        limit_Ql = get_limit_for_date(limites[plataforma].get("Ql"), data_row)

        if limit_Ql is None:
            print(f"Sem limite Ql para {plataforma} em {data_row}")
            continue

        exceed = row["Ql"] - limit_Ql
        if exceed > 0.01:
            # Filtrar e processar dados da data
            mask = df_plataforma["Date"] == data_row
            subDF = df_plataforma.loc[mask].copy()

            # Agrupar por poço para calcular BSW total do poço
            # Incluímos uma agregação especial para Campo e ZP para preservar todos os valores
            agg_dict = {
                "Qo Pot(m3/day)": "sum",
                "Qw Pot(m3/day)": "sum",
                "Qg Pot(mil m3/day)": "sum",
                "Qgco2 Pot(mil m3/day)": "sum",
                "Qghc Pot(mil m3/day)": "sum"
            }

            # Adiciona Versao e Cenario à agregação
            for col in ["Campo", "ZP", "Versao", "Cenario"]:
                if col in subDF.columns:
                    agg_dict[col] = flatten_and_unique
            # # Adicionamos agregação para Campo e ZP se existirem
            # if "Campo" in subDF.columns:
            #     agg_dict["Campo"] = flatten_and_unique
            # if "ZP" in subDF.columns:
            #     agg_dict["ZP"] = flatten_and_unique

            well_agg = subDF.groupby(["UEP", "Well", "Date"]).agg(agg_dict).reset_index()

            # Calcular variáveis auxiliares
            well_agg["Ql_i"] = well_agg["Qo Pot(m3/day)"] + well_agg["Qw Pot(m3/day)"]
            well_agg["BSW"] = well_agg.apply(
                lambda x: x["Qw Pot(m3/day)"] / x["Ql_i"] if x["Ql_i"] > 0 else 0.0,
                axis=1
            )
            well_agg["RGO"] = well_agg.apply(
                lambda x: x["Qg Pot(mil m3/day)"] / x["Qo Pot(m3/day)"] if x["Qo Pot(m3/day)"] > 0 else 0.0,
                axis=1
            )

            # Ordenar por BSW, agora com suporte a múltiplos valores de Campo/ZP
            well_agg = apply_priority_sorting(well_agg, ['BSW'], priority)

            # Calcular quanto cortar de cada poço
            adjustment_rows = []
            still_to_cut = exceed

            for _, row_poco in well_agg.iterrows():
                if still_to_cut <= 0.01:
                    break

                well_name = row_poco["Well"]
                ql_i = row_poco["Ql_i"]

                if ql_i <= 0:
                    continue

                reduce_amount = min(ql_i, still_to_cut)

                # Para este poço, calcular o rateio entre seus Campos/ZPs
                well_mask = (subDF["Well"] == well_name)
                well_data = subDF.loc[well_mask].copy()

                # Calcular o rateio para este poço específico
                # Para Ql, podemos usar a soma de Qo + Qw como base do rateio
                well_data["Ql_temp"] = well_data["Qo Pot(m3/day)"] + well_data["Qw Pot(m3/day)"]
                rateio_info = calcular_rateio_campo_zp(well_data, data_row, "Ql_temp")

                # Determina Projeto e IUPI com base em coluna
                try:
                    if coluna == "Projeto":
                        projeto_value = projeto_in
                        iupi_value = df_plataforma.loc[df_plataforma['Projeto'] == projeto_in, 'IUPI'].iloc[0]
                    else:
                        iupi_value = projeto_in
                        projeto_value = df_plataforma.loc[df_plataforma['IUPI'] == projeto_in, 'Projeto'].iloc[0]
                except IndexError:
                    print("ERROR: Nao foi possivel determinar o IUPI ou/e Projeto.")
                    return df_plataforma

                # Calcular teorCO2
                teorCO2 = row_poco["Qgco2 Pot(mil m3/day)"] / row_poco["Qg Pot(mil m3/day)"] if row_poco["Qg Pot(mil m3/day)"] > 0 else 0.0

                # Convertemos row_poco para dicionário para compatibilidade
                row_poco_dict = row_poco.to_dict()

                # Criar linhas de ajuste com rateio para este poço
                new_lines = criar_linhas_ajuste_rateadas(
                    row_poco_dict, data_row, reduce_amount, rateio_info, "Ajuste Ql",
                    projeto_value, iupi_value, plataforma, teorCO2, campo_zp_nzp_mapping
                )

                adjustment_rows.extend(new_lines)
                still_to_cut -= reduce_amount

            if adjustment_rows:
                # Garante que todas as colunas existem no DataFrame
                for col in ["Versao", "Cenario", "Campo", "ZP", "NZP", "UEP", "Well", "Date", "Classe", "Projeto", "IUPI",
                           "Qo Pot(m3/day)", "Qw Pot(m3/day)", "Qg Pot(mil m3/day)", "Qgco2 Pot(mil m3/day)",
                           "Qghc Pot(mil m3/day)", "Qwi Pot(m3/day)", "Qgi Pot(mil m3/day)", "Qgl Pot(mil m3/day)",
                           "Qgco2i Pot(mil m3/day)", "Qghci Pot(mil m3/day)"]:
                    if col not in df_plataforma.columns:
                        df_plataforma[col] = 0.0

                df_plataforma = pd.concat(
                    [df_plataforma, pd.DataFrame(adjustment_rows)],
                    ignore_index=True
                )

    return df_plataforma

@log_execution
def check_limits_for_platform_qo(df_plataforma, plataforma, projetos_analisados, coluna, priority=None):
    """
    Verifica limite de Qo, ajustando primeiro poços novos do projeto atual.
    Ajustes são aplicados por poço (Well), mantendo consistência entre diferentes Campos/ZPs
    onde o mesmo poço aparece.
    """

    # Criar mapeamento Campo-ZP para NZP
    campo_zp_nzp_mapping = create_campo_zp_nzp_mapping(df_plataforma)

    projeto_in = projetos_analisados[-1]
    projetos_anteriores = projetos_analisados[:-1]

    if plataforma not in limites:
        print(f"UEP '{plataforma}' não encontrada em limitesPN.py")
        return df_plataforma

    # Converter coluna Date se necessário
    if not pd.api.types.is_datetime64_any_dtype(df_plataforma['Date']):
        df_plataforma['Date'] = pd.to_datetime(df_plataforma['Date']).dt.date

    # Criar coluna Classe se não existir
    if "Classe" not in df_plataforma.columns:
        df_plataforma["Classe"] = "Original"

    # Agrupar e verificar limites
    df_agg = df_plataforma.groupby("Date")["Qo Pot(m3/day)"].sum().reset_index()

    for idx, row in df_agg.iterrows():
        data_row = row["Date"]
        limit_Qo = get_limit_for_date(limites[plataforma].get("Qo"), data_row)

        if limit_Qo is None:
            print(f"Sem limite Qo para {plataforma} em {data_row}")
            continue

        exceed = row["Qo Pot(m3/day)"] - limit_Qo
        if exceed > 0.01:
            # Filtrar e processar dados da data
            mask = df_plataforma["Date"] == data_row
            subDF = df_plataforma.loc[mask].copy()

            # Identificar poços novos (não presentes em projetos anteriores)
            pocos_anteriores = set()
            for proj in projetos_anteriores:
                mask_proj = (df_plataforma[coluna] == proj)
                pocos_anteriores.update(df_plataforma.loc[mask_proj, "Well"].unique())

            # Agrupar por poço para calcular RGO total e marcar poços novos
            # Incluímos uma agregação especial para Campo e ZP para preservar todos os valores
            agg_dict = {
                "Qo Pot(m3/day)": "sum",
                "Qw Pot(m3/day)": "sum",
                "Qg Pot(mil m3/day)": "sum",
                "Qgco2 Pot(mil m3/day)": "sum",
                "Qghc Pot(mil m3/day)": "sum"
            }

            # Adiciona Versao e Cenario à agregação
            for col in ["Campo", "ZP", "Versao", "Cenario"]:
                if col in subDF.columns:
                    agg_dict[col] = flatten_and_unique
            # # Adicionamos agregação para Campo e ZP se existirem
            # if "Campo" in subDF.columns:
            #     agg_dict["Campo"] = flatten_and_unique
            # if "ZP" in subDF.columns:
            #     agg_dict["ZP"] = flatten_and_unique

            well_agg = subDF.groupby(["UEP", "Well", "Date"]).agg(agg_dict).reset_index()

            # Marcar poços novos
            well_agg["Poco_Novo"] = ~well_agg["Well"].isin(pocos_anteriores)

            # Calcular variáveis auxiliares
            well_agg["RGO"] = well_agg.apply(
                lambda x: x["Qg Pot(mil m3/day)"] / x["Qo Pot(m3/day)"] if x["Qo Pot(m3/day)"] > 0 else 0.0,
                axis=1
            )
            well_agg["BSW"] = well_agg.apply(
                lambda x: x["Qw Pot(m3/day)"] / (x["Qo Pot(m3/day)"] + x["Qw Pot(m3/day)"]) if (x["Qo Pot(m3/day)"] + x["Qw Pot(m3/day)"]) > 0 else 0.0,
                axis=1
            )

            # Ordenar por poços novos primeiro e depois RGO, agora com suporte a múltiplos valores de Campo/ZP
            well_agg = apply_priority_sorting(well_agg, ["Poco_Novo", "RGO"], priority)

            # Calcular quanto cortar de cada poço
            adjustment_rows = []
            still_to_cut = exceed

            for _, row_poco in well_agg.iterrows():
                if still_to_cut <= 0.01:
                    break

                well_name = row_poco["Well"]
                qo_i = row_poco["Qo Pot(m3/day)"]

                if qo_i <= 0:
                    continue

                reduce_amount = min(qo_i, still_to_cut)

                # Para este poço, calcular o rateio entre seus Campos/ZPs
                well_mask = (subDF["Well"] == well_name)
                well_data = subDF.loc[well_mask].copy()

                # Calcular o rateio para este poço específico usando Qo como base
                rateio_info = calcular_rateio_campo_zp(well_data, data_row, "Qo Pot(m3/day)")

                # Determina Projeto e IUPI com base em coluna
                try:
                    if coluna == "Projeto":
                        projeto_value = projeto_in
                        iupi_value = df_plataforma.loc[df_plataforma['Projeto'] == projeto_in, 'IUPI'].iloc[0]
                    else:
                        iupi_value = projeto_in
                        projeto_value = df_plataforma.loc[df_plataforma['IUPI'] == projeto_in, 'Projeto'].iloc[0]
                except IndexError:
                    print("ERROR: Nao foi possivel determinar o IUPI ou/e Projeto.")
                    return df_plataforma

                # Calcular teorCO2
                teorCO2 = row_poco["Qgco2 Pot(mil m3/day)"] / row_poco["Qg Pot(mil m3/day)"] if row_poco["Qg Pot(mil m3/day)"] > 0 else 0.0

                # Convertemos row_poco para dicionário para compatibilidade
                row_poco_dict = row_poco.to_dict()

                # Criar linhas de ajuste com rateio para este poço
                new_lines = criar_linhas_ajuste_rateadas(
                    row_poco_dict, data_row, reduce_amount, rateio_info, "Ajuste Qo",
                    projeto_value, iupi_value, plataforma, teorCO2, campo_zp_nzp_mapping
                )

                adjustment_rows.extend(new_lines)
                still_to_cut -= reduce_amount

            if adjustment_rows:
                # Garante que todas as colunas existem no DataFrame
                for col in ["Versao", "Cenario", "Campo", "ZP", "NZP", "UEP", "Well", "Date", "Classe", "Projeto", "IUPI",
                           "Qo Pot(m3/day)", "Qw Pot(m3/day)", "Qg Pot(mil m3/day)", "Qgco2 Pot(mil m3/day)",
                           "Qghc Pot(mil m3/day)", "Qwi Pot(m3/day)", "Qgi Pot(mil m3/day)", "Qgl Pot(mil m3/day)",
                           "Qgco2i Pot(mil m3/day)", "Qghci Pot(mil m3/day)"]:
                    if col not in df_plataforma.columns:
                        df_plataforma[col] = 0.0

                df_plataforma = pd.concat(
                    [df_plataforma, pd.DataFrame(adjustment_rows)],
                    ignore_index=True
                )

    return df_plataforma

@log_execution
def check_limits_for_platform_qwi(df_plataforma, plataforma, projetos_analisados, coluna, priority=None):
    """
    Verifica limite de Qwi, ajustando primeiro poços novos do projeto atual.
    Ajustes são aplicados por poço (Well), mantendo consistência entre diferentes Campos/ZPs
    onde o mesmo poço aparece.
    """

    # Criar mapeamento Campo-ZP para NZP
    campo_zp_nzp_mapping = create_campo_zp_nzp_mapping(df_plataforma)

    projeto_in = projetos_analisados[-1]
    projetos_anteriores = projetos_analisados[:-1]

    if plataforma not in limites:
        print(f"UEP '{plataforma}' não encontrada em limitesPN.py")
        return df_plataforma

    # Converter coluna Date se necessário
    if not pd.api.types.is_datetime64_any_dtype(df_plataforma['Date']):
        df_plataforma['Date'] = pd.to_datetime(df_plataforma['Date']).dt.date

    # Criar coluna Classe se não existir
    if "Classe" not in df_plataforma.columns:
        df_plataforma["Classe"] = "Original"

    # Agrupar e verificar limites
    df_agg = df_plataforma.groupby("Date")["Qwi Pot(m3/day)"].sum().reset_index()

    for idx, row in df_agg.iterrows():
        data_row = row["Date"]
        limit_Qwi = get_limit_for_date(limites[plataforma].get("Qwi"), data_row)

        if limit_Qwi is None:
            print(f"Sem limite Qwi para {plataforma} em {data_row}")
            continue

        exceed = row["Qwi Pot(m3/day)"] - limit_Qwi
        if exceed > 0.01:
            # Filtrar e processar dados da data
            mask = df_plataforma["Date"] == data_row
            subDF = df_plataforma.loc[mask].copy()

            # Identificar poços novos (não presentes em projetos anteriores)
            pocos_anteriores = set()
            for proj in projetos_anteriores:
                mask_proj = (df_plataforma[coluna] == proj)
                pocos_anteriores.update(df_plataforma.loc[mask_proj, "Well"].unique())

            # Agrupar por poço para calcular Qwi total e marcar poços novos
            # Incluímos uma agregação especial para Campo e ZP para preservar todos os valores
            agg_dict = {
                "Qwi Pot(m3/day)": "sum"
            }

            # Adicionamos agregação para Campo e ZP se existirem
            if "Campo" in subDF.columns:
                agg_dict["Campo"] = flatten_and_unique
            if "ZP" in subDF.columns:
                agg_dict["ZP"] = flatten_and_unique

            # Agregamos também outras colunas que podem ser necessárias para criar_linhas_ajuste_rateadas
            for col in ["Versao", "Cenario", "NZP"]:
                if col in subDF.columns:
                    agg_dict[col] = "first"

            well_agg = subDF.groupby(["UEP", "Well", "Date"]).agg(agg_dict).reset_index()

            # Marcar poços novos
            well_agg["Poco_Novo"] = ~well_agg["Well"].isin(pocos_anteriores)

            # Ordenar por poços novos primeiro e depois por Qwi (maior para menor)
            well_agg = apply_priority_sorting(well_agg, ["Poco_Novo", "Qwi Pot(m3/day)"], priority)

            # Calcular quanto cortar de cada poço
            adjustment_rows = []
            still_to_cut = exceed

            for _, row_poco in well_agg.iterrows():
                if still_to_cut <= 0.01:
                    break

                well_name = row_poco["Well"]
                qwi_i = row_poco["Qwi Pot(m3/day)"]

                if qwi_i <= 0:
                    continue

                reduce_amount = min(qwi_i, still_to_cut)

                # Para este poço, calcular o rateio entre seus Campos/ZPs
                well_mask = (subDF["Well"] == well_name)
                well_data = subDF.loc[well_mask].copy()

                # Calcular o rateio para este poço específico usando Qwi como base
                rateio_info = calcular_rateio_campo_zp(well_data, data_row, "Qwi Pot(m3/day)")

                # Determina Projeto e IUPI com base em coluna
                try:
                    if coluna == "Projeto":
                        projeto_value = projeto_in
                        iupi_value = df_plataforma.loc[df_plataforma['Projeto'] == projeto_in, 'IUPI'].iloc[0]
                    else:
                        iupi_value = projeto_in
                        projeto_value = df_plataforma.loc[df_plataforma['IUPI'] == projeto_in, 'Projeto'].iloc[0]
                except IndexError:
                    print("ERROR: Nao foi possivel determinar o IUPI ou/e Projeto.")
                    return df_plataforma

                # Para Qwi, não precisamos de teorCO2, mas mantemos o parâmetro para compatibilidade
                teorCO2 = 0.0

                # Convertemos row_poco para dicionário para compatibilidade
                row_poco_dict = row_poco.to_dict()

                # Garantir que todas as colunas necessárias estão presentes
                # Adicionando colunas que podem estar faltando com valores zero/default
                for col in ["Qo Pot(m3/day)", "Qw Pot(m3/day)", "Qg Pot(mil m3/day)",
                           "Qgco2 Pot(mil m3/day)", "Qghc Pot(mil m3/day)"]:
                    if col not in row_poco_dict:
                        row_poco_dict[col] = 0.0

                # Criar linhas de ajuste com rateio para este poço
                new_lines = criar_linhas_ajuste_rateadas(
                    row_poco_dict, data_row, reduce_amount, rateio_info, "Ajuste Qwi",
                    projeto_value, iupi_value, plataforma, teorCO2, campo_zp_nzp_mapping
                )

                adjustment_rows.extend(new_lines)
                still_to_cut -= reduce_amount

            if adjustment_rows:
                # Garante que todas as colunas existem no DataFrame
                for col in ["Versao", "Cenario", "Campo", "ZP", "NZP", "UEP", "Well", "Date", "Classe", "Projeto", "IUPI",
                           "Qo Pot(m3/day)", "Qw Pot(m3/day)", "Qg Pot(mil m3/day)", "Qgco2 Pot(mil m3/day)",
                           "Qghc Pot(mil m3/day)", "Qwi Pot(m3/day)", "Qgi Pot(mil m3/day)", "Qgl Pot(mil m3/day)",
                           "Qgco2i Pot(mil m3/day)", "Qghci Pot(mil m3/day)"]:
                    if col not in df_plataforma.columns:
                        df_plataforma[col] = 0.0

                df_plataforma = pd.concat(
                    [df_plataforma, pd.DataFrame(adjustment_rows)],
                    ignore_index=True
                )

    return df_plataforma

@log_execution
def verificar_ajustes_negativos(df):
    """
    Verifica se existem valores negativos após ajustes e cria linhas de compensação
    para garantir que nenhuma variável fique negativa.
    """
    # Identificar poços com valores negativos após ajustes
    colunas_verificar = [
        "Qo Pot(m3/day)", "Qw Pot(m3/day)", "Qg Pot(mil m3/day)",
        "Qgco2 Pot(mil m3/day)", "Qghc Pot(mil m3/day)",
        "Qwi Pot(m3/day)", "Qgi Pot(mil m3/day)", "Qgl Pot(mil m3/day)",
        "Qgco2i Pot(mil m3/day)", "Qghci Pot(mil m3/day)"
    ]

    # Criar cópia para evitar modificações durante iteração
    df_corrigido = df.copy()

    # Agrupar por poço e data para verificar somas
    grupo_colunas = ["Well", "Date", "UEP"]

    # Agrupar dados para identificar valores negativos por poço/data
    df_agrupado = df.groupby(grupo_colunas)[colunas_verificar].sum().reset_index()

    # Linhas de compensação a serem adicionadas
    linhas_compensacao = []

    # Para cada poço/data com valores negativos
    for _, row in df_agrupado.iterrows():
        well = row["Well"]
        data = row["Date"]
        uep = row["UEP"]

        # Verificar se há valores negativos
        valores_negativos = {}
        for col in colunas_verificar:
            if row[col] < -0.01:  # Tolerância para evitar problemas de arredondamento
                valores_negativos[col] = abs(row[col])  # Valor necessário para compensar

        # Se houver valores negativos
        if valores_negativos:
            # Filtrar dados originais deste poço/data
            mask_poco_data = (df["Well"] == well) & (df["Date"] == data) & (df["UEP"] == uep)
            dados_poco = df[mask_poco_data].copy()

            # Filtrar apenas os registros originais (não ajustes)
            dados_originais = dados_poco[dados_poco["Classe"] == "Original"]

            # Se não houver dados originais, usar todos os dados do poço
            if dados_originais.empty:
                dados_originais = dados_poco

            # Calcular proporcionalidades
            for col, valor_compensar in valores_negativos.items():
                # Determinar qual tipo de ajuste é necessário com base na coluna
                if col == "Qo Pot(m3/day)":
                    classe_ajuste = "Compensação Qo"
                elif col == "Qw Pot(m3/day)":
                    classe_ajuste = "Compensação Qw"
                elif col in ["Qg Pot(mil m3/day)", "Qgco2 Pot(mil m3/day)", "Qghc Pot(mil m3/day)"]:
                    classe_ajuste = "Compensação Qg"
                elif col == "Qwi Pot(m3/day)":
                    classe_ajuste = "Compensação Qwi"
                elif col in ["Qgi Pot(mil m3/day)", "Qgco2i Pot(mil m3/day)", "Qghci Pot(mil m3/day)"]:
                    classe_ajuste = "Compensação Qgi"
                else:
                    classe_ajuste = "Compensação"

                # Calcular o rateio para este poço específico
                if "Qo" in col or "Qw" in col:
                    coluna_rateio = "Qo Pot(m3/day)" if "Qo" in col else "Qw Pot(m3/day)"
                elif "Qg" in col:
                    coluna_rateio = "Qg Pot(mil m3/day)"
                elif "Qwi" in col:
                    coluna_rateio = "Qwi Pot(m3/day)"
                elif "Qgi" in col:
                    coluna_rateio = "Qgi Pot(mil m3/day)"
                else:
                    coluna_rateio = col

                # Calcular rateio entre Campos/ZPs
                rateio_info = calcular_rateio_campo_zp(dados_originais, data, coluna_rateio)

                # Obter projeto e IUPI
                projeto_value = dados_originais["Projeto"].iloc[0] if "Projeto" in dados_originais.columns else ""
                iupi_value = dados_originais["IUPI"].iloc[0] if "IUPI" in dados_originais.columns else ""

                # Calcular teorCO2 se necessário
                teorCO2 = 0.0
                if "Qgco2 Pot(mil m3/day)" in dados_originais.columns and "Qg Pot(mil m3/day)" in dados_originais.columns:
                    qg_total = dados_originais["Qg Pot(mil m3/day)"].sum()
                    qgco2_total = dados_originais["Qgco2 Pot(mil m3/day)"].sum()
                    teorCO2 = qgco2_total / qg_total if qg_total > 0 else 0.0

                # Calcular BSW e RGO
                bsw = 0.0
                rgo = 0.0
                if "Qo Pot(m3/day)" in dados_originais.columns and "Qw Pot(m3/day)" in dados_originais.columns:
                    qo_total = dados_originais["Qo Pot(m3/day)"].sum()
                    qw_total = dados_originais["Qw Pot(m3/day)"].sum()
                    bsw = qw_total / (qo_total + qw_total) if (qo_total + qw_total) > 0 else 0.0

                if "Qo Pot(m3/day)" in dados_originais.columns and "Qg Pot(mil m3/day)" in dados_originais.columns:
                    qo_total = dados_originais["Qo Pot(m3/day)"].sum()
                    qg_total = dados_originais["Qg Pot(mil m3/day)"].sum()
                    rgo = qg_total / qo_total if qo_total > 0 else 0.0

                # Criar linha base com informações do poço
                row_poco_dict = {
                    "Well": well,
                    "BSW": bsw,
                    "RGO": rgo,
                    "Campo": dados_originais["Campo"].iloc[0] if "Campo" in dados_originais.columns else "",
                    "ZP": dados_originais["ZP"].iloc[0] if "ZP" in dados_originais.columns else "",
                    "NZP": dados_originais["NZP"].iloc[0] if "NZP" in dados_originais.columns else "",
                    "Versao": dados_originais["Versao"].iloc[0] if "Versao" in dados_originais.columns else "",
                    "Cenario": dados_originais["Cenario"].iloc[0] if "Cenario" in dados_originais.columns else ""
                }

                # Criar mapeamento Campo-ZP para NZP
                campo_zp_nzp_mapping = create_campo_zp_nzp_mapping(dados_originais)

                # Criar linhas de compensação com valores positivos
                new_lines = criar_linhas_ajuste_rateadas(
                    row_poco_dict, data, valor_compensar, rateio_info, classe_ajuste,
                    projeto_value, iupi_value, uep, teorCO2, campo_zp_nzp_mapping
                )

                # Inverter os sinais para compensação (valores positivos)
                for new_line in new_lines:
                    for col_num in colunas_verificar:
                        if col_num in new_line:
                            new_line[col_num] = abs(new_line[col_num])

                linhas_compensacao.extend(new_lines)

    # Adicionar linhas de compensação ao DataFrame
    if linhas_compensacao:
        # Garante que todas as colunas existem no DataFrame
        for col in ["Versao", "Cenario", "Campo", "ZP", "NZP", "UEP", "Well", "Date", "Classe", "Projeto", "IUPI"] + colunas_verificar:
            if col not in df_corrigido.columns:
                df_corrigido[col] = 0.0

        df_corrigido = pd.concat(
            [df_corrigido, pd.DataFrame(linhas_compensacao)],
            ignore_index=True
        )

        print(f"Foram adicionadas {len(linhas_compensacao)} linhas de compensação para corrigir valores negativos.")

    return df_corrigido

@log_execution
def analisar_plataforma(df_plataforma, plataforma, projetos_analisados, coluna, priority=None):
    """Analisa os dados de uma plataforma específica"""
    print(f"Processando plataforma: {plataforma}")

    # Faz a checagem de limites de Qg e captura o df atualizado
    df_plataforma_atualizado = check_limits_for_platform_qg(df_plataforma, plataforma, projetos_analisados[-1], coluna, priority)
    df_plataforma_atualizado = verificar_ajustes_negativos(df_plataforma_atualizado)

    # Faz a checagem de limites de Qw e captura o df atualizado
    df_plataforma_atualizado = check_limits_for_platform_qw(df_plataforma_atualizado, plataforma, projetos_analisados[-1], coluna, priority)
    df_plataforma_atualizado = verificar_ajustes_negativos(df_plataforma_atualizado)

    # Faz a checagem de limites de Ql e captura o df atualizado
    df_plataforma_atualizado = check_limits_for_platform_ql(df_plataforma_atualizado, plataforma, projetos_analisados[-1], coluna, priority)
    df_plataforma_atualizado = verificar_ajustes_negativos(df_plataforma_atualizado)

    # Faz a checagem de limites de Qo e captura o df atualizado
    df_plataforma_atualizado = check_limits_for_platform_qo(df_plataforma_atualizado, plataforma, projetos_analisados, coluna, priority)
    df_plataforma_atualizado = verificar_ajustes_negativos(df_plataforma_atualizado)

    # Faz a checagem de limites de Qwi e captura o df atualizado
    df_plataforma_atualizado = check_limits_for_platform_qwi(df_plataforma_atualizado, plataforma, projetos_analisados, coluna, priority)
    df_plataforma_atualizado = verificar_ajustes_negativos(df_plataforma_atualizado)

    return df_plataforma_atualizado


@log_execution

def analisar_projeto_cumulativo(df, projetos_analisados, coluna, priority=None):
    """Analisa os dados de projetos de forma cumulativa"""
    print(f"\nProcessando projeto: {projetos_analisados[-1]}")

    # Verificar e criar a coluna 'Classe' se necessário
    if 'Classe' not in df.columns:
        df['Classe'] = 'Original'

    # Filtra o DataFrame para obter apenas as linhas dos projetos acumulados até agora
    df_filtrado = df[df[coluna].isin(projetos_analisados)]

    # Verificar se a coluna 'UEP' existe no DataFrame
    if 'UEP' in df_filtrado.columns:
        # Obter valores únicos de plataforma
        plataformas = df_filtrado['UEP'].unique()

        # Analisar cada plataforma separadamente
        for plataforma in plataformas:
            # Filtrar dados para esta plataforma
            df_plataforma = df_filtrado[df_filtrado['UEP'] == plataforma]
            # recebe o df com ajustes
            df_plataforma_ajustado = analisar_plataforma(df_plataforma, plataforma, projetos_analisados, coluna, priority)

            # Agora, precisamos "substituir" essa parte em df_filtrado
            # ou concatenar de volta. Exemplo simples:
            # 1) Remove as linhas antigas dessa plataforma
            df_filtrado = df_filtrado[df_filtrado['UEP'] != plataforma]
            # 2) Concatena com as linhas novas
            df_filtrado = pd.concat([df_filtrado, df_plataforma_ajustado], ignore_index=True)
    else:
        print("Coluna 'UEP' não encontrada no DataFrame. Análise por plataforma não será realizada.")

    # 1) Apagar do df principal as linhas desses projetos
    df_sem_projeto_analisado = df[~df[coluna].isin(projetos_analisados)]
    # 2) Concatenar com df_filtrado atualizado
    df_atualizado = pd.concat([df_sem_projeto_analisado, df_filtrado], ignore_index=True)
    return df_atualizado

@log_execution
def planilhaGIR(df, file_path):
    """
    Processa o DataFrame para adequação às especificações de colunas, renomeação, cálculos e ordem.
    Retorna o DataFrame processado.
    """
    # Lista de colunas originais necessárias (incluindo as usadas em cálculos)
    colunas_necessarias = [
        'Versao', 'Cenario', 'Campo', 'NZP', 'UEP', 'IUPI', 'Well', 'Date',
        'Qo Pot(m3/day)', 'Qg Pot(mil m3/day)', 'Qgco2 Pot(mil m3/day)',
        'Qw Pot(m3/day)', 'Qwi Pot(m3/day)', 'Qgco2i Pot(mil m3/day)',
        'Qgl Pot(mil m3/day)'
    ]

    # Remove colunas não citadas
    df = df[colunas_necessarias].copy()

    # Dicionário de renomeação
    renomear_colunas = {
        'Versao': 'Versão',
        'Cenario': 'Cenário',
        'Campo': 'Campo',
        'NZP': 'Nº da ZP',
        'UEP': 'Plataforma',
        'IUPI': 'Projeto',
        'Well': 'Poço',
        'Date': 'Data',
        'Qo Pot(m3/day)': '1 - Qo Pot',
        'Qw Pot(m3/day)': '3 - Qw Pot',
        'Qwi Pot(m3/day)': '4 - Qwi Pot',
        'Qgco2i Pot(mil m3/day)': '5 - Qgi Pot',
        'Qgl Pot(mil m3/day)': '6 - GL Pot'
    }
    df = df.rename(columns=renomear_colunas)

    # Calcula '2 - Qg Pot' (Qg Pot - Qgco2 Pot)
    df['2 - Qg Pot'] = df['Qg Pot(mil m3/day)'].fillna(0) - df['Qgco2 Pot(mil m3/day)'].fillna(0)

    # Remove colunas originais usadas no cálculo
    df = df.drop(columns=['Qg Pot(mil m3/day)', 'Qgco2 Pot(mil m3/day)'])

    # Cria coluna '7 - Auto inj Pot' (vazia por padrão)
    df['7 - Auto inj Pot'] = 0.0

    # Ordem final das colunas
    ordem_colunas = [
        'Versão', 'Cenário', 'Campo', 'Nº da ZP', 'Plataforma', 'Projeto', 'Poço', 'Data',
        '1 - Qo Pot', '2 - Qg Pot', '3 - Qw Pot', '4 - Qwi Pot',
        '5 - Qgi Pot', '6 - GL Pot', '7 - Auto inj Pot'
    ]

    df = df[ordem_colunas]

    df.to_excel(file_path, index=False, sheet_name='Planilha1')
    print(f"Arquivo Excel GIR salvo em: {os.path.abspath(file_path)}")

    return df

@log_execution
def pre_ajuste(df):
    # Atualiza a coluna UEP de FIELD para P-58
    mask0 = (df['UEP'] == 'FIELD') & (df['ZP'] == 'CO140-ESS116')
    df.loc[mask0, 'UEP'] = 'P-58'

    # Multiplicar por 0.97/1.35 para "P-58" na coluna UEP e "CO140-ESS116" na coluna ZP
    mask_1 = (df['UEP'] == 'P-58') & (df['ZP'] == 'CO140-ESS116')
    df.loc[mask_1, 'Qgl Pot(mil m3/day)'] *= 0.86 / 1.40

    # Multiplicar por 0.97/1.35 para "P-58" na coluna UEP e "MCB/COQ-ESS103A" na coluna ZP
    mask_2 = (df['UEP'] == 'P-58') & (df['ZP'] == 'MCB/COQ-ESS103A')
    df.loc[mask_2, 'Qgl Pot(mil m3/day)'] *= 0.97 / 1.35

    return df

@log_execution
def main():
    print(f"Nome do script: {sys.argv[0]}")
    excel_path = str(sys.argv[1])
    ordem_projetos_path = r"L:\res\campos\jubarte\er\er02\00_Programas\python\script\atual\ordem_projetos.txt"
    priority = {
        'Campo':'BLA=9999' # 'Campo': 'JUB=0,PRB=1,BLA=9999' -> Com essa entrada OUTROS campo possui prioridade 5000 ou seja BLA é depois dos OUTROS
    }

    # Configurar timestamp para nome do arquivo de log
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M")
    output_excel_path = os.path.splitext(excel_path)[0] + f".{timestamp}.ajustado.xlsx"
    output_excel_GIR_path = os.path.splitext(excel_path)[0] + f".{timestamp}.ajustado.gir.xlsx"

    # Solicitar caminhos dos arquivos (ou usar caminhos padrão se já definidos)
    excel_path = input("Digite o caminho do arquivo Excel: ") if not excel_path else excel_path
    ordem_projetos_path = input("Digite o caminho do arquivo ordem_projetos.txt: ") if not ordem_projetos_path else ordem_projetos_path

    # Verificar se os arquivos existem
    if not os.path.exists(excel_path):
        print(f"Erro: O arquivo Excel '{excel_path}' não existe.")
        return

    if not os.path.exists(ordem_projetos_path):
        print(f"Erro: O arquivo de ordem de projetos '{ordem_projetos_path}' não existe.")
        return

    # Ler os arquivos
    df = ler_excel(excel_path)
    ordem_projetos = ler_ordem_projetos(ordem_projetos_path)

    # print('df.columns')
    # print(df.columns)
    # input()

    if df is None or not ordem_projetos:
        print("Não foi possível continuar devido a erros na leitura dos arquivos.")
        return

    # coluna = 'Projeto'
    coluna = 'IUPI'

    # Verificar se a coluna 'IUPI' existe no DataFrame
    if coluna not in df.columns:
        print(f"Erro: A coluna '{coluna}' não foi encontrada no arquivo Excel.")
        print(f"Colunas disponiveis: {', '.join(df.columns)}")
        return

    df = pre_ajuste(df)

    print(f"Total de projetos a processar: {len(ordem_projetos)}")

    projetos_acumulados = []
    for i, projeto in enumerate(ordem_projetos, 1):
        print(f"\nProcessando projeto {i}/{len(ordem_projetos)}: {projeto}")
        projetos_acumulados.append(projeto)
        df = analisar_projeto_cumulativo(df, projetos_acumulados.copy(), coluna, priority)

    print("Análise concluída!")

    # Verificar e preencher valores vazios na coluna 'Classe'
    if 'Classe' in df.columns:
        # Preencher valores vazios ou NaN com "Original"
        df['Classe'] = df['Classe'].fillna("Original")
        # Verificar se há strings vazias e substituí-las
        df.loc[df['Classe'] == '', 'Classe'] = "Original"
        print(f"Preenchidos {len(df[df['Classe'] == 'Original'])} registros com 'Classe' igual a 'Original'")
        print(f"Preenchidos {len(df[df['Classe'] != 'Original'])} registros com 'Classe' diferente de 'Original'")
    else:
        # Se a coluna não existir, criar e preencher tudo com "Original"
        df['Classe'] = "Original"
        print("Coluna 'Classe' não existia e foi criada com valor padrão 'Original'")

    # return

    # Salvar no Excel final
    df.to_excel(output_excel_path, index=False)
    print(f"Arquivo Excel final salvo em: {os.path.abspath(output_excel_path)}")

    planilhaGIR(df, output_excel_GIR_path)

if __name__ == "__main__":
    main()
