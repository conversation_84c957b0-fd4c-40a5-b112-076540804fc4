import pandas as pd
import os
from datetime import datetime

def carregar_dataset(caminho_arquivo, linhas_cabecalho=3):
    """Carrega o dataset e prepara os cabeçalhos"""
    if not os.path.exists(caminho_arquivo):
        print("\n❌ Arquivo não encontrado!")
        return None
    
    try:
        df = pd.read_csv(caminho_arquivo, sep='\t', header=list(range(linhas_cabecalho)))
        # Combina os cabeçalhos multinível
        df.columns = [' | '.join(map(str, col)).strip() for col in df.columns.values]
        return df
    except Exception as e:
        print(f"\n❌ Erro ao carregar arquivo: {e}")
        return None

def mostrar_colunas(df, filtrar_por=None):
    """Exibe as colunas disponíveis com filtro opcional por substrings"""
    print("\n🔍 Colunas disponíveis:")
    colunas_filtradas = []
    
    for i, col in enumerate(df.columns, 1):
        if filtrar_por is None or any(sub.upper() in col.upper() for sub in filtrar_por):
            print(f"{i:>3}. {col}")
            colunas_filtradas.append(col)
    
    return colunas_filtradas

def selecionar_colunas(df, filtrar_por=None):
    """Permite selecionar colunas com opções flexíveis"""
    colunas_validas = mostrar_colunas(df, filtrar_por)
    
    if filtrar_por and not colunas_validas:
        print(f"\n⚠️ Nenhuma coluna contendo {', '.join(filtrar_por)} encontrada!")
        return None
    
    while True:
        try:
            instrucao = "'T' para todas, 'P' para pular" if filtrar_por else "números das colunas"
            selecao = input(f"\n👉 Digite: {instrucao} ou Enter para voltar: ").strip()
            
            if not selecao:
                return None  # Usuário optou por voltar
                
            if selecao.upper() == 'T':
                return df[colunas_validas].copy()
            elif selecao.upper() == 'P':
                return df.copy()  # Retorna todas as colunas originais
                
            indices = [int(i.strip()) for i in selecao.split(",")]
            colunas = [colunas_validas[i-1] for i in indices if 1 <= i <= len(colunas_validas)]
            
            if not colunas:
                print("\n⚠️ Nenhuma coluna válida selecionada!")
                continue
                
            return df[colunas]
        except ValueError:
            print("\n❌ Entrada inválida! Use números, 'T' ou 'P'")

def filtrar_valores(df):
    """Aplica filtros de valores com opção de pular"""
    for col in df.columns:
        print(f"\n🎯 Coluna: {col}")
        print("📊 Valores únicos (amostra):", df[col].dropna().unique()[:5])
        
        valores = input("🔎 Filtrar por valores (separados por vírgula) ou Enter para pular: ").strip()
        if valores:
            try:
                if pd.api.types.is_numeric_dtype(df[col]):
                    valores = [float(v) if '.' in v else int(v) for v in valores.split(",")]
                else:
                    valores = [v.strip() for v in valores.split(",")]
                
                df = df[df[col].isin(valores)]
            except ValueError:
                print("\n⚠️ Alguns valores não puderam ser convertidos. Filtro aplicado como texto.")
                df = df[df[col].astype(str).isin([v.strip() for v in valores.split(",")])]
    
    return df

def sugerir_nome_arquivo(tipo_filtro=""):
    """Sugere um nome de arquivo baseado na data e filtro aplicado"""
    data_atual = datetime.now().strftime("%d%m%Y_%H%M%S")
    base_name = "dados_filtrados"
    
    sugestoes = {
        "DATE": f"{base_name}_date_{data_atual}",
        "BHP": f"{base_name}_bhp_{data_atual}",
        "DATE_BHP": f"{base_name}_date_bhp_{data_atual}",
        "": f"{base_name}_{data_atual}"
    }
    
    return sugestoes.get(tipo_filtro, f"{base_name}_{data_atual}")

def salvar_resultado(df, tipo_filtro=""):
    """Gerencia o salvamento do arquivo com sugestão automática"""
    if df.empty:
        print("\n⚠️ Nenhum dado para salvar!")
        return
    
    sugestao = sugerir_nome_arquivo(tipo_filtro)
    nome_arquivo = input(f"\n💾 Digite o nome do arquivo (sugestão: {sugestao}) ou Enter para usar sugestão: ").strip()
    
    nome_arquivo = nome_arquivo if nome_arquivo else sugestao
    nome_arquivo += ".csv"
    
    try:
        df.to_csv(nome_arquivo, index=False)
        print(f"\n✅ Arquivo salvo como '{nome_arquivo}'")
    except Exception as e:
        print(f"\n❌ Erro ao salvar arquivo: {e}")

def menu_principal():
    """Interface principal do programa"""
    for item in listaSelecionada:
        caminho_arquivo = item
        df = carregar_dataset(caminho_arquivo)
        
        if df is None:
            return
        
        while True:
            print("\n" + "="*50)
            print("📊 MENU PRINCIPAL".center(50))
            print("="*50)
            print("\n1. Filtrar colunas manualmente")
            print("2. Filtrar colunas contendo 'DATE'")
            print("3. Filtrar colunas contendo 'BHP'")
            print("4. Filtrar colunas contendo 'DATE' ou 'BHP'")
            print("5. Visualizar dados completos")
            print("6. Sair")
            
            opcao = input("\n🔘 Escolha uma opção: ").strip()
            
            tipo_filtro = ""
            df_filtrado = None
            
            if opcao == "1":
                df_filtrado = selecionar_colunas(df)
                if df_filtrado is not None:
                    df_filtrado = filtrar_valores(df_filtrado)
                    print("\n📋 Resultado do filtro:")
                    print(df_filtrado.head())
                    salvar_resultado(df_filtrado)
            elif opcao == "2":
                df_filtrado = selecionar_colunas(df, filtrar_por=["DATE"])
                tipo_filtro = "DATE"
            elif opcao == "3":
                df_filtrado = selecionar_colunas(df, filtrar_por=["BHP"])
                tipo_filtro = "BHP"
            elif opcao == "4":
                df_filtrado = selecionar_colunas(df, filtrar_por=["DATE", "BHP"])
                tipo_filtro = "DATE_BHP"
            elif opcao == "5":
                print("\n👀 Visualização dos dados:")
                print(df.head())
                continue
            elif opcao == "6":
                print("\n👋 Saindo do programa...")
                break
            else:
                print("\n❌ Opção inválida!")
                continue
            
            if df_filtrado is not None:
                print("\n📋 Resultado do filtro:")
                print(df_filtrado.head())
                salvar_resultado(df_filtrado, tipo_filtro)

listaSelecionada=[
	r'L:\\res\\campos\\jubarte\\er\\er03\\PN\\2630\\C2\\P50\\2507181416\\RO300\\PID5_proj_RO300_PE259_P50_PID5.data',
	r'L:\\res\\campos\\jubarte\\er\\er03\\PN\\2630\\C2\\P50\\2507181416\\CO140\\EVPRO_P-58_proj_P50_CO140_EVU_2025_c2v3.data',
	r'L:\\res\\campos\\jubarte\\er\\er03\\PN\\2630\\C2\\P50\\2507181416\\MRL700\\EVPRO_P-58_proj_P50_MRL700_00489_2056_2025_c2.data',
	r'L:\\res\\campos\\jubarte\\er\\er03\\PN\\2630\\C2\\P50\\2507181416\\BR100\\PID1_proj_ajuste_6_00487_nome_JUB65_BASE.data',
	r'L:\\res\\campos\\jubarte\\er\\er03\\PN\\2630\\C2\\P50\\2507181416\\CXR\\ICSPB_proj_CXR_4P_1I_C1_6000_PN2630_V2_nova_krel.data',
	r'L:\\res\\campos\\jubarte\\er\\er03\\PN\\2630\\C2\\P50\\2507181416\\PreSal\\ICSPB_proj_RMSv14_er03_geoeng_output03_087_06_ICSPB_PRB.opt.data'
]

if __name__ == "__main__":
    print("\n" + "="*50)
    print("🔧 FILTRADOR DE DATASET AVANÇADO".center(50))
    print("="*50)
    menu_principal()