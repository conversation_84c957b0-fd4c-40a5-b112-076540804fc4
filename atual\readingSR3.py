#!/dfs_geral_ep/res/campos/jubarte/er/er02/00_Programas/python/linux/bin/python
# python readingSR3.py simulacao.sr3 --list_groups --list_wells --group_vars --well_vars
# python readingSR3.py simulacao.sr3 --csv_path output.csv --entities entity1,entity2 --variables var1,var2
# python readingSR3.py simulacao.sr3 --csv_path output.csv --entities entity1,entity2 --variables var1,var2 --monthly_avg
# python readingSR3.py simulacao.sr3 --csv_path output.csv --entities entity1,entity2 --variables var1,var2 --monthly_avg --ref_date YYYY-MM-DD --annual_rate 0.1
# python readingSR3.py simulacao.sr3 --entities entity1,entity2 --variables var1,var2 --monthly_avg
# python readingSR3.py simulacao.sr3 --dist_pocos distances.csv
# python readingSR3.py simulacao.sr3 --entities entity1,entity2 --variables var1,var2 --monthly_avg --dist_pocos distances.csv
# python readingSR3.py simulacao.sr3 --export_html well_trajectories.html
# python readingSR3.py simulacao.sr3 --csv_path output.csv --entities all_wells,all_groups,all_sectors --variables var1,var2
# python readingSR3.py simulacao.sr3 --export_well_coords well_coordinates.csv
# python readingSR3.py simulacao.sr3 --hphiso 1:5,10,:,-1
# No Linux
# /dfs_geral_ep/res/campos/jubarte/er/er02/00_Programas/python/script/atual/readingSR3.py /dfs_geral_ep/res/campos/jubarte/er/er03/JUB_PRB_MCBCOQ/2025/02_PD_Cenario4v250210/Sim/run/BASE_bkp/RMSv14_er03_geoeng_output02_001.sr3 --csv_path /dfs_geral_ep/res/campos/jubarte/er/er03/JUB_PRB_MCBCOQ/2025/02_PD_Cenario4v250210/Sim/run/BASE_bkp/RMSv14_er03_geoeng_output02_001.teste.data --entities all_sectors,all_groups,all_wells --variables "Liquid Rate SC,Qo,Qw,Qg,BSW,RGO,BHP,Well Head Pressure,Well Mobility-Weighted Datum Pressure - 9P,Np,Gp,Npe,Npe*,Oil Volume SC SCTR,Ave DAPRE HC POVO SCTR,On-time Fraction,Well Cumulative Open On-time,Cumulative lift gas injected per well,Gas Mole Frac(CO2) SC,Qgl,IP" --ref_date 2026-01-01 --annual_rate 0.1 --monthly_avg

from ogsim import GEM
from scipy import interpolate
import os
import h5py
import argparse
import importlib.util
import sys
import warnings
import textwrap
import numpy as np
import pandas as pd
import xarray as xr
import pickle
import re
from Auxiliares.modules.logging_module import log_execution

# Suppress specific warnings from the ogsim package
warnings.filterwarnings("ignore", category=FutureWarning, module="ogsim")
warnings.filterwarnings("ignore", category=RuntimeWarning, module="ogsim")

@log_execution
def decode_bytes(value):
    if isinstance(value, bytes):
        return value.decode('utf-8')
    return value

class SimulationData:
    @log_execution
    def __init__(self, sr3_path=None):
        self.sr3Path = sr3_path
        self.sim = GEM(self.sr3Path)
        self.well = self.sim.read_timeseries('WELLS')
        self.group = self.sim.read_timeseries('GROUPS')
        self.sector = self.sim.read_timeseries('SECTORS')

        self.conversion_table = self.readUnitConversionTable()
        self.layer_table = self.readLayerTable()

        # Replace deprecated applymap with map
        self.group = self.group.map(lambda x: x if x is not None else np.nan)

        #### NOVOS TRECHOS: Ler "Simulator Name" e "ComponentTable" para viabilizar cálculo do CO2
        self.simulator = "UNKNOWN"
        self.component_table = None
        try:
            with h5py.File(self.sr3Path, 'r') as f:
                # Tenta ler o nome do simulador (se existir)
                if "Simulator Name" in f.attrs:
                    self.simulator = f.attrs["Simulator Name"].decode()

                # Tenta ler a component_table (caso seja GEM e exista "General/ComponentTable")
                if "General" in f and "ComponentTable" in f["General"]:
                    df = pd.DataFrame(f["General"]["ComponentTable"][()])
                    # Decodifica se precisar
                    df = df.applymap(lambda x: x.decode('utf-8') if isinstance(x, bytes) else x)
                    self.component_table = df

        except Exception as e:
            # Se der erro, simplesmente não carrega a tabela
            print(f"Warning: Falhou ao ler component_table: {e}")
        #### FIM DOS NOVOS TRECHOS

    @log_execution
    def readLayerTable(self):
        f = h5py.File(self.sr3Path)
        df = pd.DataFrame(f['TimeSeries']['LAYERS']['LayerTable'][()])
        df = df.apply(lambda col: col.map(decode_bytes))
        return df

    @log_execution
    def readUnitConversionTable(self):
        f = h5py.File(self.sr3Path)
        df = pd.DataFrame(f['General']['UnitConversionTable'][()])
        df = df.apply(lambda col: col.map(decode_bytes))
        # Converter o DataFrame para o formato de dicionário desejado
        conversion_table = {}
        for _, row in df.iterrows():
            unit_name = row['Unit Name'].lower()  # Converter para minúsculas para consistência
            conversion_table[unit_name] = {
                'Dimensionality': int(row['Dimensionality']),
                'Gain': float(row['Gain']),
                'Offset': float(row['Offset'])
            }
        return conversion_table

    @log_execution
    def calculate_all_well_distances(self):
        """
        Calculates the distance between all wells and returns a DataFrame with wells as both indices and columns.
        """
        wells = self.layer_table['Parent'].unique()
        distance_matrix = pd.DataFrame(index=wells, columns=wells, dtype=float)

        for well1 in wells:
            for well2 in wells:
                if well1 == well2:
                    distance_matrix.loc[well1, well2] = 0.0
                elif pd.notna(distance_matrix.loc[well1, well2]):
                    continue
                else:
                    try:
                        min_distance = self.calculate_min_distance_between_wells(well1, well2)
                        distance_matrix.loc[well1, well2] = min_distance
                        distance_matrix.loc[well2, well1] = min_distance
                    except Exception as e:
                        print(f"Error calculating distance between {well1} and {well2}: {e}")
                        distance_matrix.loc[well1, well2] = np.nan
                        distance_matrix.loc[well2, well1] = np.nan
        return distance_matrix

    @log_execution
    def calculate_distances_to_all_wells(self, well_name):
        all_wells = self.layer_table['Parent'].unique()
        other_wells = [w for w in all_wells if w != well_name]

        distances = []
        for other_well in other_wells:
            try:
                min_distance = self.calculate_min_distance_between_wells(well_name, other_well)
                distances.append({'Well Name': other_well, f'Distance to {well_name}': min_distance})
            except Exception as e:
                print(f"Erro ao calcular a distância entre {well_name} e {other_well}: {e}")
                continue

        df = pd.DataFrame(distances)
        df_sorted = df.sort_values(by=f'Distance to {well_name}').reset_index(drop=True)

        return df_sorted

    @log_execution
    def calculate_min_distance_between_wells(self, well1_name, well2_name):
        # Filtrar para incluir apenas camadas de matriz (MT)
        mt_layers = self.layer_table[
            self.layer_table['Name'].str.contains('MT') |
            (~self.layer_table['Name'].str.contains('MT|FR'))
        ]

        well1_layers = mt_layers[mt_layers['Parent'] == well1_name]
        well2_layers = mt_layers[mt_layers['Parent'] == well2_name]
        well1_segments = self.extract_segments(well1_layers)
        well2_segments = self.extract_segments(well2_layers)

        min_distance = self.compute_min_distance_between_segments(well1_segments, well2_segments)

        return min_distance

    @log_execution
    def extract_segments(self, layers):
        segments = []
        for _, row in layers.iterrows():
            entry_point = np.array([row['X Entry'], row['Y Entry'], row['Z Entry']])
            exit_point = np.array([row['X Exit'], row['Y Exit'], row['Z Exit']])
            segments.append((entry_point, exit_point))
        return segments

    @log_execution
    def compute_min_distance_between_segments(self, segments1, segments2):
        min_dist = np.inf
        for seg1 in segments1:
            for seg2 in segments2:
                dist = self.segment_to_segment_distance(seg1, seg2)
                if dist < min_dist:
                    min_dist = dist
        return min_dist

    @log_execution
    def segment_to_segment_distance(self, seg1, seg2):
        p1, p2 = seg1
        p3, p4 = seg2

        u = p2 - p1
        v = p4 - p3
        w0 = p1 - p3

        a = np.dot(u, u)
        b = np.dot(u, v)
        c = np.dot(v, v)
        d = np.dot(u, w0)
        e = np.dot(v, w0)

        denominator = a * c - b * b

        if denominator == 0:
            # Segmentos paralelos
            dist = self.point_to_segment_distance(p1, seg2)
            dist = min(dist, self.point_to_segment_distance(p2, seg2))
            dist = min(dist, self.point_to_segment_distance(p3, seg1))
            dist = min(dist, self.point_to_segment_distance(p4, seg1))
            return dist
        else:
            s = (b * e - c * d) / denominator
            t = (a * e - b * d) / denominator

            s = np.clip(s, 0, 1)
            t = np.clip(t, 0, 1)

            closest_point_seg1 = p1 + s * u
            closest_point_seg2 = p3 + t * v

            distance = np.linalg.norm(closest_point_seg1 - closest_point_seg2)
            return distance

    @log_execution
    def point_to_segment_distance(self, point, segment):
        seg_start, seg_end = segment
        seg_vec = seg_end - seg_start
        point_vec = point - seg_start
        seg_len = np.dot(seg_vec, seg_vec)
        if seg_len == 0:
            return np.linalg.norm(point - seg_start)
        t = np.dot(point_vec, seg_vec) / seg_len
        t = np.clip(t, 0, 1)
        projection = seg_start + t * seg_vec
        return np.linalg.norm(point - projection)

    @log_execution
    def read_variable(self, var_name):
        if var_name in self.group.data_vars:
            return self.group[var_name]
        elif var_name in self.well.data_vars:
            return self.well[var_name]
        elif var_name in self.sector.data_vars:
            return self.sector[var_name]
        else:
            raise ValueError(f"Variable '{var_name}' not found in the dataset.")

    @log_execution
    def list_origins(self, dataset='group'):
        if dataset == 'group':
            return list(self.group.coords['origin'].values)
        elif dataset == 'well':
            return list(self.well.coords['origin'].values)
        elif dataset == 'sector':
            return list(self.sector.coords['origin'].values)
        else:
            raise ValueError("Dataset must be 'group' or 'well'.")

    @log_execution
    def list_variables(self, dataset='group'):
        if dataset == 'group':
            return list(self.group.data_vars)
        elif dataset == 'well':
            return list(self.well.data_vars)
        elif dataset == 'sector':
            return list(self.sector.data_vars)
        else:
            raise ValueError("Dataset must be 'group', 'well', or 'sector'.")

    @log_execution
    def list_all_groups(self):
        return sorted(self.group.coords['origin'].values)

    @log_execution
    def list_all_wells(self):
        return sorted(self.well.coords['origin'].values)

    @log_execution
    def list_all_sectors(self):
        return sorted(self.sector.coords['origin'].values)

    @log_execution
    def read_variable_for_origin(self, var_name, origin_name):
        if var_name in self.group.data_vars and origin_name in self.group.coords['origin'].values:
            return self.group[var_name].sel(origin=origin_name)
        if var_name in self.well.data_vars and origin_name in self.well.coords['origin'].values:
            return self.well[var_name].sel(origin=origin_name)
        if var_name in self.sector.data_vars and origin_name in self.sector.coords['origin'].values:
            return self.sector[var_name].sel(origin=origin_name)
        raise ValueError(f"Variable '{var_name}' or origin '{origin_name}' not found in the dataset.")

    @log_execution
    def compute_gas_mole_frac_co2_for_origin(self, origin_name):
        """
        Computa Gas Mole Frac(CO2) SC para *qualquer* origem (poço, grupo ou setor),
        retornando um DataFrame com colunas [Date, Gas Mole Frac(CO2) SC].
        Se o simulador não for GEM, ou se não houver CO2 na component_table,
        retorna 0.0 ou vazio (mesma lógica que antes).
        """
        # Verifica se existe "component_table" e se é GEM
        if self.simulator != "GEM" or self.component_table is None:
            # Tenta alinhar no tempo com "GASCMOLSC(1)" se existir
            gascmol1 = None
            try:
                gascmol1 = self.read_variable_for_origin("GASCMOLSC(1)", origin_name)
            except:
                gascmol1 = None

            if gascmol1 is not None:
                if gascmol1.name is None:
                    gascmol1.name = "GASCMOLSC(1)"
                df0 = gascmol1.to_dataframe().reset_index()
                df0["Gas Mole Frac(CO2) SC"] = 0.0
                return df0[["Date", "Gas Mole Frac(CO2) SC"]]
            else:
                return pd.DataFrame(columns=["Date", "Gas Mole Frac(CO2) SC"])

        # A partir daqui, supomos que é GEM + component_table OK
        if "Name" not in self.component_table.columns:
            return pd.DataFrame(columns=["Date", "Gas Mole Frac(CO2) SC"])

        # Tenta achar a linha do CO2
        co2_rows = self.component_table.index[self.component_table["Name"] == "CO2"].tolist()
        if not co2_rows:
            # Não achou CO2
            # fallback => zero
            try:
                gascmol1 = self.read_variable_for_origin("GASCMOLSC(1)", origin_name)
            except:
                gascmol1 = None

            if gascmol1 is not None:
                if gascmol1.name is None:
                    gascmol1.name = "GASCMOLSC(1)"
                df0 = gascmol1.to_dataframe().reset_index()
                df0["Gas Mole Frac(CO2) SC"] = 0.0
                return df0[["Date", "Gas Mole Frac(CO2) SC"]]
            else:
                return pd.DataFrame(columns=["Date", "Gas Mole Frac(CO2) SC"])

        co2_index = co2_rows[0]
        co2_varname = f"GASCMOLSC({co2_index+1})"

        # Descobrimos todas as variáveis do tipo "GASCMOLSC(i)" disponíveis no dataset (origem group/well/sector)
        all_vars = []
        # Se a origem estiver na self.well, a checagem é self.well.data_vars
        # Mas se estiver em group ou sector, precisamos verificar self.group.data_vars ou self.sector.data_vars.
        # Entretanto, podemos checar todos e ver qual deles contiver a var de fato:

        # Para simplificar, vamos ler do mesmíssimo "read_variable_for_origin" e montar a listagem
        # ou, se já tiver lido, usar algo assim:
        if hasattr(self.well, 'data_vars'):
            all_vars += [v for v in self.well.data_vars if v.startswith("GASCMOLSC(")]
        if hasattr(self.group, 'data_vars'):
            all_vars += [v for v in self.group.data_vars if v.startswith("GASCMOLSC(")]
        if hasattr(self.sector, 'data_vars'):
            all_vars += [v for v in self.sector.data_vars if v.startswith("GASCMOLSC(")]
        # Remove duplicados
        all_vars = list(set(all_vars))

        if not all_vars:
            return pd.DataFrame(columns=["Date", "Gas Mole Frac(CO2) SC"])

        # Tenta carregar a série do CO2 var principal
        try:
            co2_data = self.read_variable_for_origin(co2_varname, origin_name)
        except ValueError:
            # fallback => usar "GASCMOLSC(1)" se existir, mas zero
            try:
                gascmol1 = self.read_variable_for_origin("GASCMOLSC(1)", origin_name)
                if gascmol1.name is None:
                    gascmol1.name = "GASCMOLSC(1)"
                df1 = gascmol1.to_dataframe().reset_index()
                df1["Gas Mole Frac(CO2) SC"] = 0.0
                return df1[["Date", "Gas Mole Frac(CO2) SC"]]
            except:
                return pd.DataFrame(columns=["Date", "Gas Mole Frac(CO2) SC"])

        if co2_data.name is None:
            co2_data.name = co2_varname
        df_co2 = co2_data.to_dataframe().reset_index()
        df_co2 = df_co2.rename(columns={co2_varname: "CO2data"})

        # Agora somamos todos os GASCMOLSC(i) para achar soma total
        sum_data = None
        for gv in all_vars:
            try:
                tmp = self.read_variable_for_origin(gv, origin_name)
                if sum_data is None:
                    sum_data = tmp
                else:
                    sum_data = sum_data + tmp
            except:
                pass

        if sum_data is None:
            # fallback zero
            df_co2["Gas Mole Frac(CO2) SC"] = 0.0
            return df_co2[["Date", "Gas Mole Frac(CO2) SC"]]

        if sum_data.name is None:
            sum_data.name = "SUMdata"
        df_sum = sum_data.to_dataframe().reset_index()

        # Merge e faz a fração
        merged = pd.merge(df_co2, df_sum, on="Date", how="outer").sort_values("Date")
        merged.ffill(inplace=True)
        merged.fillna(0, inplace=True)

        merged["Gas Mole Frac(CO2) SC"] = 0.0
        mask = (merged["SUMdata"] != 0)
        merged.loc[mask, "Gas Mole Frac(CO2) SC"] = merged.loc[mask, "CO2data"] / merged.loc[mask, "SUMdata"]

        return merged[["Date", "Gas Mole Frac(CO2) SC"]]

    @log_execution
    def _compute_monthly_yearly_avg_instantaneous(self, df, var_name, monthly_avg, yearly_avg):
        """
        Para uma variável instantânea, gera datas mensais ou anuais
        (dependendo de monthly_avg/yearly_avg) e interpola linearmente
        o valor de 'var_name' nessas datas.

        :param df: DataFrame com colunas ['Date', var_name].
        :param var_name: Nome da coluna com os valores.
        :param monthly_avg: bool, se True, faz tratamento mensal.
        :param yearly_avg: bool, se True, faz tratamento anual.
        :return: Um DataFrame com as colunas ['Date', var_name], onde
                as linhas são as datas (início de cada mês ou ano)
                e os valores são o resultado da interpolação.
        """
        # 1) Ordena o DataFrame por data
        df = df.sort_values('Date')

        # 2) Verifica se vamos realmente fazer monthly ou yearly
        if not (monthly_avg or yearly_avg):
            return df  # Se nenhuma das flags estiver ativa, simplesmente retorna o original

        # 3) Define datas iniciais e finais
        start_date = df['Date'].min()
        end_date = df['Date'].max()

        # 4) Gera a lista de datas-alvo mensais ou anuais
        if monthly_avg:
            avg_dates = pd.date_range(
                start=start_date.to_period('M').to_timestamp(),
                end=(end_date.to_period('M') + 1).to_timestamp(),
                freq='MS'
            )
        else:  # yearly_avg
            avg_dates = pd.date_range(
                start=start_date.to_period('Y').to_timestamp(),
                end=(end_date.to_period('Y') + 1).to_timestamp(),
                freq='YS'
            )

        # 5) Cria a função de interpolação
        f_interp = interpolate.interp1d(
            df['Date'].astype(int),
            df[var_name].values,
            kind='linear',
            fill_value='extrapolate'
        )

        # 6) Aplica a interpolação nas datas definidas
        avg_values = f_interp(avg_dates.astype(int))

        # 7) Sobrescreve qualquer data acima do último ponto real, repetindo o último valor real
        #    (caso você queira também tratar as datas anteriores ao início, pode fazer algo similar)
        last_value = df[var_name].iloc[-1]
        mask_late = (avg_dates > end_date)
        avg_values[mask_late] = last_value

        # Opcionalmente, se quiser que datas ANTES do start_date tenham o primeiro valor real,
        # pode fazer algo parecido:
        # first_value = df[var_name].iloc[0]
        # mask_early = (avg_dates < start_date)
        # avg_values[mask_early] = first_value

        # 8) Monta o DataFrame final
        df_avg = pd.DataFrame({
            'Date': avg_dates,
            var_name: avg_values
        })

        return df_avg

    @log_execution
    def _compute_monthly_yearly_avg_rate_from_cumulative(self, df, var_name, monthly_avg, yearly_avg):
        """
        A partir de uma série cumulativa (ex: 'Cumulative Oil SC'), gera médias mensais ou anuais
        de taxa via diferenças sucessivas no tempo.

        Parâmetros:
        -----------
        df : pd.DataFrame
            Deve conter as colunas ['Date', var_name], com var_name sendo dados cumulativos.
        var_name : str
            Nome da coluna cumulativa (p. ex.: 'Cumulative Oil SC').
        monthly_avg : bool
            Se True, calcula médias mensais.
        yearly_avg : bool
            Se True, calcula médias anuais.
        Retorno:
        --------
        df_rate : pd.DataFrame
            Contém ['Date', var_name], onde var_name agora é a taxa média (ex.: m3/dia).
            A data de cada linha representa o fim do intervalo considerado.
        """
        from scipy import interpolate

        # Ordena por data
        df = df.sort_values('Date')
        df = df.dropna(subset=['Date', var_name])

        # Datas inicial e final
        start_date = df['Date'].min()
        end_date = df['Date'].max()

        # Gera as datas de fronteira (mensais ou anuais)
        if monthly_avg:
            avg_dates = pd.date_range(
                start=start_date.to_period('M').to_timestamp(),
                end=(end_date.to_period('M') + 1).to_timestamp(),
                freq='MS'
            )
        elif yearly_avg:
            avg_dates = pd.date_range(
                start=start_date.to_period('Y').to_timestamp(),
                end=(end_date.to_period('Y') + 1).to_timestamp(),
                freq='YS'
            )
        else:
            # Se nenhum dos dois estiver ativo, retorna o df original (ou faça outro tratamento)
            return df

        if len(avg_dates) < 2:
            # Se por acaso só gerou 1 data, não há intervalo para calcular taxa
            return pd.DataFrame(columns=['Date', var_name])

        # Interpolação linear do cumulativo nas datas de fronteira
        f_cum = interpolate.interp1d(
            df['Date'].astype(int),
            df[var_name],
            kind='linear',
            fill_value='extrapolate'
        )
        interp_cum = f_cum(avg_dates.astype(int))

        # Opcional: zera valores antes da 1ª data real
        interp_cum[avg_dates < start_date] = 0.0

        # Para calcular a taxa do intervalo [i, i+1], fazemos:
        # DeltaVolume = interp_cum[i+1] - interp_cum[i]
        # DeltaTempo (dias) = (avg_dates[i+1] - avg_dates[i]).days
        rates = []
        rate_dates = []

        for i in range(len(avg_dates) - 1):
            # Data do inicio do intervalo
            start_dt = avg_dates[i]

            delta_volume = interp_cum[i+1] - interp_cum[i]
            delta_days = (avg_dates[i+1] - avg_dates[i]).days

            if delta_days <= 0:
                # Evitar divisão por zero ou intervalo inválido
                avg_rate = 0.0
            else:
                # Taxa média no período
                avg_rate = delta_volume / delta_days

            rates.append(avg_rate)
            # Aqui, assumimos que queremos "rotular" a taxa pela data inicial do período:
            rate_dates.append(start_dt)

        # Monta DataFrame
        rate_dates = avg_dates[:-1]
        df_rate = pd.DataFrame({
            'Date': rate_dates,
            var_name: rates
        })

        return df_rate

    @log_execution
    def _compute_monthly_yearly_avg_for_bsw_rgo(self, merged_df, monthly_avg, yearly_avg):
        """
        Dado um DataFrame com as colunas 'Date', 'Cumulative Oil SC',
        'Cumulative Water SC' e/ou 'Cumulative Gas SC', retorna um novo
        DataFrame interpolado em datas mensais ou anuais, incluindo as
        colunas 'deltaNp', 'deltaWp', 'deltaGp' e 'resultDate'.

        Observação: Esse método é típico para suportar o cálculo de BSW e RGO,
        que dependem de (Np, Wp, Gp) em intervalos discretos.
        """
        from scipy import interpolate

        # Garante que está ordenado por data e preenche NaNs
        merged_df = merged_df.sort_values('Date').ffill().fillna(0)

        # Determina se vamos fazer monthly ou yearly
        if monthly_avg:
            freq = 'MS'  # "Month Start"
        elif yearly_avg:
            freq = 'YS'  # "Year Start"
        else:
            # Se nenhuma das opções estiver ativa, retornamos o DF original
            # pois não faz sentido continuar.
            return merged_df

        # Intervalos de data a serem usados
        start_date = merged_df['Date'].min()
        end_date = merged_df['Date'].max()
        # Gera as datas (fronteiras) mensais ou anuais
        avg_dates = pd.date_range(
            start=start_date.to_period(freq[0]).to_timestamp(),
            end=(end_date.to_period(freq[0]) + 1).to_timestamp(),
            freq=freq
        )

        # Vamos interpolar as séries cumulativas que existirem:
        cols_to_interp = []
        if 'Cumulative Oil SC' in merged_df.columns:
            cols_to_interp.append('Cumulative Oil SC')
        if 'Cumulative Water SC' in merged_df.columns:
            cols_to_interp.append('Cumulative Water SC')
        if 'Cumulative Gas SC' in merged_df.columns:
            cols_to_interp.append('Cumulative Gas SC')

        # DataFrame resultante com as datas de fronteira
        new_df = pd.DataFrame({'resultDate': avg_dates})

        # Para cada coluna cumulativa (Np, Wp, Gp), interpolamos
        for ccol in cols_to_interp:
            f = interpolate.interp1d(
                merged_df['Date'].astype(int),
                merged_df[ccol].values,
                kind='linear',
                fill_value='extrapolate'
            )
            new_df[ccol] = f(avg_dates.astype(int))

        # Agora calculamos as diferenças, que são os incrementos por intervalo
        # Ex.: deltaNp = diff de Cumulative Oil SC, etc.
        new_df['deltaNp'] = 0
        new_df['deltaWp'] = 0
        new_df['deltaGp'] = 0

        if 'Cumulative Oil SC' in new_df.columns:
            new_df['deltaNp'] = new_df['Cumulative Oil SC'].diff().fillna(new_df['Cumulative Oil SC'])

        if 'Cumulative Water SC' in new_df.columns:
            new_df['deltaWp'] = new_df['Cumulative Water SC'].diff().fillna(new_df['Cumulative Water SC'])

        if 'Cumulative Gas SC' in new_df.columns:
            new_df['deltaGp'] = new_df['Cumulative Gas SC'].diff().fillna(new_df['Cumulative Gas SC'])

        # Retorna esse new_df; lá em process_BSW ou process_RGO você lerá
        # new_df['deltaNp'], new_df['deltaWp'], new_df['deltaGp'] e
        # new_df['resultDate'] para fazer BSW, RGO, etc.

        # No final, renomeie 'resultDate' -> 'Date'
        new_df = new_df.rename(columns={'resultDate': 'Date'})
        return new_df

    @log_execution
    def _compute_monthly_yearly_avg_for_npe(self, merged_df, monthly_avg, yearly_avg):
        """
        Dado um DataFrame contendo as colunas:
        - 'Date'
        - 'Cumulative Oil SC' (opcionalmente)
        - 'Cumulative Gas SC' (opcionalmente)
        esta função:
        1) Ordena e preenche NaNs;
        2) Interpola linearmente as colunas cumulativas em datas mensais ou anuais,
            @log_execution
            definidas por freq='MS' (Month Start) ou freq='YS' (Year Start);
        3) Retorna um DataFrame que contém:
            - 'resultDate'
            - 'Cumulative Oil SC' (interp.)
            - 'Cumulative Gas SC' (interp.)
            para que, posteriormente, possamos calcular Npe = Np + Gp/1000.

        Caso monthly_avg e yearly_avg sejam ambos False, retornamos o DF original
        sem alterações.
        """
        from scipy import interpolate

        # Ordenamos e preenchemos dados ausentes com zero
        merged_df = merged_df.sort_values('Date').ffill().fillna(0)

        # Se não for mensal nem anual, não há o que interpolar
        if not monthly_avg and not yearly_avg:
            return merged_df

        # Define a frequência
        freq = 'MS' if monthly_avg else 'YS'

        # Determina data inicial e final
        start_date = merged_df['Date'].min()
        end_date = merged_df['Date'].max()

        # Gera as datas de fronteira (mensais ou anuais)
        avg_dates = pd.date_range(
            start=start_date.to_period(freq[0]).to_timestamp(),
            end=(end_date.to_period(freq[0]) + 1).to_timestamp(),
            freq=freq
        )

        # Montamos o DF interpolado
        new_df = pd.DataFrame({'resultDate': avg_dates})

        # Vamos interpolar para Cumulative Oil SC e Cumulative Gas SC se existirem
        cols_to_interp = ['Cumulative Oil SC', 'Cumulative Gas SC']
        for ccol in cols_to_interp:
            if ccol in merged_df.columns:
                # Função de interpolação
                f = interpolate.interp1d(
                    merged_df['Date'].astype(int),
                    merged_df[ccol].values,
                    kind='linear',
                    fill_value='extrapolate'
                )
                new_df[ccol] = f(avg_dates.astype(int))
            else:
                # Se a coluna não existir no DF original, setamos 0
                new_df[ccol] = 0.0

        # Retorna o DF com 'resultDate' + colunas cumulativas (interp.)
        # Deixe o nome 'resultDate' se seu código subsequente já espera isso;
        # caso queira mantê-lo como 'Date', basta renomear abaixo:
        #
        new_df = new_df.rename(columns={'resultDate': 'Date'})
        #
        return new_df

    @log_execution
    def process_regular_variable(self, var_name, entity, monthly_avg, yearly_avg, ref_date_str, annual_rate):
        """
        Processa uma variável "regular" (que não seja BSW, RGO, Npe, Npe*, Gas Mole Frac(CO2) SC)
        podendo aplicá-la como taxa (convertendo para cumulativo) ou tratar como cumulativo/instantâneo.

        Retorna uma lista com 1 DataFrame e um dicionário de unidades correspondentes.
        """
        data_frames = []
        units = {}

        # Detectar se a variável é do tipo "rate", "cumulative" ou "instantaneous"
        variable_type = self.get_variable_type(var_name)

        if variable_type == 'rate':
            # Tenta achar o nome da cumulativa correspondente
            cum_var_name = self.get_cumulative_var_name(var_name)
            if cum_var_name is not None:
                # Lê o dado cumulativo
                try:
                    cum_data = self.read_variable_for_origin(cum_var_name, entity)
                except ValueError as e:
                    # Se não existir, gera aviso e pula
                    raise ValueError(str(e))

                if cum_data.name is None:
                    cum_data.name = cum_var_name

                df_cum = cum_data.to_dataframe().reset_index()

                if monthly_avg or yearly_avg:
                    # Calcula a taxa média mensal ou anual a partir do cumulativo
                    df_rate = self._compute_monthly_yearly_avg_rate_from_cumulative(
                        df_cum,
                        cum_var_name,  # atenção: passamos o nome cumulativo
                        monthly_avg,
                        yearly_avg
                    )
                    # Agora renomeamos a coluna cumulativa para o nome da taxa
                    df_rate = df_rate.rename(columns={cum_var_name: var_name})
                    df = df_rate
                else:
                    # Se não for monthly/yearly, significa que queremos a taxa "instantânea"
                    # Já que a simulação pode ter armazenado cumulativo, mas o usuário pediu 'rate' sem média
                    # Precisaríamos derivar ou simplesmente não temos dados de "rate" instantâneo aqui.
                    # Escolha 1: retornar vazio
                    # Escolha 2: ou diferenciar a cada passo de tempo do cumulativo original
                    # Para manter a coerência, vamos supor que o usuário queira a "taxa do passo de simulação".
                    df = df_cum.sort_values('Date')
                    df[var_name] = df[cum_var_name].diff().fillna(df[cum_var_name])
                    # Divide pelo delta de dias entre steps
                    df['delta_days'] = df['Date'].diff().dt.days.fillna(1)
                    df[var_name] = df[var_name] / df['delta_days']
                    df.drop(columns=['delta_days'], inplace=True)
                    # Remove a coluna cumulativa
                    df.drop(columns=[cum_var_name], inplace=True)

            else:
                # Se não houver cumulativa correspondente, tratamos como "instantâneo"
                # (por exemplo, BHP erroneamente marcado como "rate"?)
                data = self.read_variable_for_origin(var_name, entity)
                if data.name is None:
                    data.name = var_name
                df = data.to_dataframe().reset_index()

                if monthly_avg or yearly_avg:
                    df = self._compute_monthly_yearly_avg_instantaneous(df, var_name, monthly_avg, yearly_avg)

        elif variable_type == 'cumulative':
            # Se for propriamente cumulativa
            data = self.read_variable_for_origin(var_name, entity)
            if data.name is None:
                data.name = var_name
            df = data.to_dataframe().reset_index()

            if monthly_avg or yearly_avg:
                # Supondo que você queira "cumulative" linearmente interpolado
                # Se quiser outro método, crie algo como _compute_monthly_yearly_avg_cumulative
                df = df.sort_values('Date')
                # Interpolamos cumulativa em datas mensais/anuais e retornamos cumulativa
                # (Sem tirar diffs)
                # Parecido com _compute_monthly_yearly_avg_instantaneous, mas sem diffs
                start_date = df['Date'].min()
                end_date = df['Date'].max()
                if monthly_avg:
                    avg_dates = pd.date_range(
                        start=start_date.to_period('M').to_timestamp(),
                        end=(end_date.to_period('M') + 1).to_timestamp(),
                        freq='MS'
                    )
                else:  # yearly_avg
                    avg_dates = pd.date_range(
                        start=start_date.to_period('Y').to_timestamp(),
                        end=(end_date.to_period('Y') + 1).to_timestamp(),
                        freq='YS'
                    )
                from scipy import interpolate
                f_cum = interpolate.interp1d(
                    df['Date'].astype(int),
                    df[var_name],
                    kind='linear',
                    fill_value='extrapolate'
                )
                interp_cum = f_cum(avg_dates.astype(int))
                interp_cum[avg_dates < start_date] = 0.0
                df_cum = pd.DataFrame({
                    'Date': avg_dates,
                    var_name: interp_cum
                })
                df = df_cum

        else:
            # "instantaneous"
            data = self.read_variable_for_origin(var_name, entity)
            if data.name is None:
                data.name = var_name
            df = data.to_dataframe().reset_index()

            if monthly_avg or yearly_avg:
                df = self._compute_monthly_yearly_avg_instantaneous(df, var_name, monthly_avg, yearly_avg)

        # Renomeia para <var>_<entity>
        df = df.rename(columns={var_name: f"{var_name}_{entity}"})

        # Determina a unidade
        if variable_type == "rate":
            # Se a cumulativa original tinha units, assumimos que a cumulativa é algo como 'm3'
            # Então a taxa vira 'm3/day'
            if cum_var_name is not None:
                # Tenta do cumulativo
                base_data = self.read_variable_for_origin(cum_var_name, entity)
                base_unit = base_data.attrs.get('units', 'unknown')
            else:
                base_unit = "unknown"

            rate_unit = f"{base_unit}/day"
            units[f"{var_name}_{entity}"] = rate_unit
        else:
            # BHP, Instantâneo, ou cumulativo
            # Pega a unidade do var_name real
            try:
                read_data = self.read_variable_for_origin(var_name, entity)
                detected_unit  = read_data.attrs.get('units', 'unknown')
            except:
                # se não existir
                detected_unit  = "unknown"

            # Se for especificamente a variável "On-time Fraction", forçamos a unidade para "fraction"
            if var_name == "On-time Fraction":
                units[f"{var_name}_{entity}"] = "fraction"
            else:
                units[f"{var_name}_{entity}"] = detected_unit

        data_frames.append(df)
        return data_frames, units

    @log_execution
    def _compute_time_weighted_avg_on_time_fraction(self, df_otf, monthly_avg, yearly_avg):
        """
        Recebe um DataFrame df_otf com colunas ['Date', 'On-time Fraction'],
        onde 'On-time Fraction' é considerada constante até o próximo "Date".

        Se monthly_avg ou yearly_avg for True, computa a média ponderada de
        'On-time Fraction' em cada intervalo (mês/ano). Retorna DataFrame final
        com colunas ['Date', 'On-time Fraction'] onde cada 'Date' é o início do
        intervalo mensal/anual e 'On-time Fraction' é a média ponderada no período.

        Caso monthly_avg e yearly_avg sejam ambos False, apenas retorna df_otf inalterado.
        """
        # 1) Ordenar por data
        df_otf = df_otf.sort_values('Date').dropna(subset=['Date'])

        # Se não for mensal/anual, retorna inalterado
        if not monthly_avg and not yearly_avg:
            return df_otf

        # 2) Definir a frequência
        if monthly_avg:
            freq = 'MS'  # inícios de cada mês
        else:
            freq = 'YS'  # inícios de cada ano

        start_date = df_otf['Date'].min()
        end_date = df_otf['Date'].max()

        # 3) Construir as datas de fronteira
        boundaries = pd.date_range(
            start=start_date.to_period(freq[0]).to_timestamp(),
            end=(end_date.to_period(freq[0]) + 1).to_timestamp(),
            freq=freq
        )
        # Se por acaso houver só 1 boundary, não temos intervalo
        if len(boundaries) < 2:
            return pd.DataFrame(columns=['Date', 'On-time Fraction'])

        # 4) Construir segmentos:
        #    Cada linha i define [date_i, date_{i+1}) com valor OTF = df_otf.loc[i, 'On-time Fraction'].
        #    Para isso, criamos uma coluna shiftada com a data seguinte.
        df_otf['Date_end'] = df_otf['Date'].shift(-1)
        # A última data_end podemos definir como end_date + 1 dia (ou end_date exato).
        df_otf.loc[df_otf.index[-1], 'Date_end'] = end_date

        # Exemplo de colunas agora:
        # Date, On-time Fraction, Date_end
        # 2008-01-01, 0.4, 2008-01-15
        # 2008-01-15, 0.2, 2008-02-01
        # 2008-02-01, 0.4, 2008-02-10  (se for o último ponto)

        # 5) Para cada intervalo [Bn, Bn+1], calculamos a média ponderada
        result_rows = []
        for i in range(len(boundaries) - 1):
            interval_start = boundaries[i]
            interval_end   = boundaries[i+1]
            if interval_start >= interval_end:
                continue

            # Vamos iterar sobre os segmentos e ver a interseção
            total_duration = (interval_end - interval_start).total_seconds()
            if total_duration <= 0:
                avg_value = 0.0
                result_rows.append({'Date': interval_start, 'On-time Fraction': avg_value})
                continue

            weighted_sum = 0.0
            for idx, row in df_otf.iterrows():
                seg_start = row['Date']
                seg_end   = row['Date_end']
                seg_val   = row['On-time Fraction']

                # Se seg_start >= seg_end, pular
                if pd.isnull(seg_end) or seg_start >= seg_end:
                    continue

                # Interseção do [seg_start, seg_end) com [interval_start, interval_end)
                effective_start = max(seg_start, interval_start)
                effective_end   = min(seg_end, interval_end)

                duration = (effective_end - effective_start).total_seconds()
                if duration > 0:
                    weighted_sum += duration * seg_val

            avg_value = weighted_sum / total_duration
            result_rows.append({'Date': interval_start, 'On-time Fraction': avg_value})

        # 6) Construir DataFrame final
        df_avg = pd.DataFrame(result_rows)
        df_avg.sort_values('Date', inplace=True)
        return df_avg

    @log_execution
    def fast_time_weighted_avg_otf(self, df_otf, monthly_avg=False, yearly_avg=False, entity=None):
        """
        df_otf: DataFrame com colunas ['Date', 'On-time Fraction']
        monthly_avg, yearly_avg: booleans.
        Retorna DataFrame com ['Date', 'On-time Fraction'] calculado via média ponderada no tempo.
        """
        # Se não for monthly nem yearly, não faz nada
        if not monthly_avg and not yearly_avg:
            return df_otf

        # Decide freq
        if monthly_avg:
            freq = 'MS'
        else:
            freq = 'YS'

        # df_otf deve ter ['Date', 'On-time Fraction']
        df_otf = df_otf.sort_values('Date').copy()
        df_otf['Date_end'] = df_otf['Date'].shift(-1)
        df_otf.loc[df_otf.index[-1], 'Date_end'] = df_otf.iloc[-1]['Date'] + pd.Timedelta(days=1)

        # Gera fronteiras (mensais ou anuais)
        start_date = df_otf['Date'].min()
        end_date   = df_otf['Date_end'].max()
        boundaries = pd.date_range(start=start_date, end=end_date, freq=freq)
        if boundaries[-1] < end_date:
            # garante que a última fronteira abranja o fim
            boundaries = boundaries.append(pd.DatetimeIndex([end_date]))

        # 1) Monta uma tabela de "eventos": pontos em que OTF muda.
        #    - Data de início: df_otf['Date']
        #    - Data de fim:    df_otf['Date_end']
        # Precisamos de uma “linha” por data de transição, e a respectiva OTF.

        # "Eventos" = datas do df_otf + datas do boundaries
        all_events = pd.concat([
            pd.DataFrame({'Date': df_otf['Date'], 'OTF': df_otf['On-time Fraction']}),
            pd.DataFrame({'Date': boundaries,      'OTF': np.nan})
        ], ignore_index=True).drop_duplicates(subset=['Date']).sort_values('Date')

        # 2) Preenche OTF por ffill
        all_events['OTF'] = all_events['OTF'].ffill().fillna(0.0)

        # 3) Agora cada “subintervalo” é de Date[i] até Date[i+1].
        #    Calculamos a duração e qual o valor de OTF nesse subintervalo
        all_events['Date_next'] = all_events['Date'].shift(-1)
        all_events.loc[all_events.index[-1], 'Date_next'] = all_events.iloc[-1]['Date']
        all_events['duration_s'] = (all_events['Date_next'] - all_events['Date']).dt.total_seconds()

        # 4) Descarta a última linha (que terá duration_s=0)
        all_events = all_events.iloc[:-1]

        # 5) Para agrupar por mês/ano, basta rotular cada Date[i] (ou subintervalo)
        #    segundo o "mês" ou "ano" de destino. Podemos usar 'Date_next' ou a média do intervalo.
        #    Mas o usual é classificar pelo “início do subintervalo”.
        # all_events['period'] = all_events['Date'].dt.to_period('M')  if freq=='MS' else \
        all_events['period'] = all_events['Date'].dt.to_period('M')  if freq=='MS' else \
                            all_events['Date'].dt.to_period('Y')  # se freq=='YS'

        # 6) Agora podemos somar “OTF * duração_s” e “duração_s” por período.
        grp = all_events.groupby('period', group_keys=False)
        df_res = grp.apply(
            lambda g: pd.Series({
                'weighted_sum': (g['OTF'] * g['duration_s']).sum(),
                'total_s': g['duration_s'].sum()
            })
        )
        df_res = df_res.reset_index(drop=False)

        # 7) A média ponderada
        df_res['time_weighted_otf'] = df_res['weighted_sum'] / df_res['total_s']

        # 8) Converter period -> início do período (Timestamp)
        #    Se quisermos a data “início de cada mês/ano”:
        df_res["Date"] = df_res["period"].dt.to_timestamp(how="start")

        df_res.rename(columns={"time_weighted_otf": "On-time Fraction"}, inplace=True)
        df_res.rename(columns={"On-time Fraction": f"On-time Fraction_{entity}"}, inplace=True)

        return df_res[['Date',f"On-time Fraction_{entity}"]]

    @log_execution
    def process_on_time_fraction_weighted(self, entities, monthly_avg, yearly_avg, ref_date_str, annual_rate):
        """
        Processa a variável "On-time Fraction" para uma lista de entities.
        Faz média ponderada mensal ou anual, caso monthly_avg ou yearly_avg seja True.
        Retorna:
        - data_frames: lista de DataFrames (um por entity)
        - units: dicionário {coluna: unidade}
        """
        data_frames = []
        units = {}

        for entity in entities:
            try:
                # Lê a série de On-time Fraction
                otf_data = self.read_variable_for_origin("On-time Fraction", entity)
            except ValueError as e:
                print(f"Warning: On-time Fraction not found for {entity}: {e}")
                continue

            if otf_data.name is None:
                otf_data.name = "On-time Fraction"

            df_otf = otf_data.to_dataframe().reset_index()  # colunas: ['Date', 'On-time Fraction']

            # Se monthly_avg ou yearly_avg for True, calculamos a média ponderada;
            # caso contrário, mantemos como está.
            if monthly_avg or yearly_avg:
                # df_otf = self._compute_time_weighted_avg_on_time_fraction(df_otf, monthly_avg, yearly_avg)
                df_otf = self.fast_time_weighted_avg_otf(df_otf, monthly_avg, yearly_avg, entity)

            # Renomeia a coluna final para "On-time Fraction_<entity>"
            df_otf = df_otf.rename(columns={"On-time Fraction": f"On-time Fraction_{entity}"})

            data_frames.append(df_otf)
            # Unidade sempre "fraction"
            units[f"On-time Fraction_{entity}"] = "fraction"

        return data_frames, units

    @log_execution
    def process_on_time_fraction(self, entities, monthly_avg, yearly_avg, ref_date_str, annual_rate):
        """
        Ajuste da função process_on_time_fraction:
        - Se monthly_avg ou yearly_avg estiver ativo, e no período houver algum
        valor não-nulo/on-time fraction > 0, escolhemos o primeiro valor
        diferente de zero encontrado naquele período.
        - Se o período for todo zero ou ausente, retornamos zero.
        """

        data_frames = []
        units = {}

        otf_var = "On-time Fraction"

        for entity in entities:
            # 1) Ler dados de OTF (On-time Fraction)
            try:
                otf_data = self.read_variable_for_origin(otf_var, entity)
            except ValueError:
                print(f"Warning: No '{otf_var}' found for {entity}. Skipping.")
                continue

            # Converte On-time Fraction para DataFrame
            df_otf = otf_data.to_dataframe().reset_index()  # colunas: [Date, On-time Fraction]
            if df_otf.empty:
                continue

            df_merged = df_otf.copy()

            df_merged.sort_values('Date', inplace=True)
            # Preencher valores ausentes para frente (se existirem)
            df_merged.ffill(inplace=True)
            df_merged.fillna(0, inplace=True)

            otf_col = "On-time Fraction"

            # 5) Se monthly_avg ou yearly_avg, ao invés de interpolar, fazemos a agregação
            #    definindo: "se houver pelo menos um valor não-zero, pegar o 1o valor não-zero;
            #    senão zero".
            if monthly_avg or yearly_avg:
                freq = 'MS' if monthly_avg else 'YS'
                df_for_resample = df_merged[['Date', otf_col]].copy()
                df_for_resample.set_index('Date', inplace=True)

                # Função que retorna o primeiro valor != 0 no intervalo
                # Caso só tenha zeros, retorna 0
                @log_execution
                def first_nonzero_or_zero(series):
                    s_nonzero = series[(series != 0) & (~series.isna())]
                    if not s_nonzero.empty:
                        return s_nonzero.iloc[0]
                    else:
                        return 0

                # Agrupar pelo início de cada mês/ano e pegar a "primeira não-zero"
                df_agg = df_for_resample.resample(freq).apply(first_nonzero_or_zero)

                # 'df_agg' terá como índice a data do início do mês/ano.
                df_agg = df_agg.reset_index()

                # Usamos esse resultado como "df_otf_final"
                df_otf_final = df_agg

            else:
                # Caso não seja mensal/anual, somente mantemos a série "diária" (ou de cada step) original
                df_otf_final = df_merged[['Date', otf_col]].copy()

            # Renomear para "On-time Fraction_<entity>"
            final_col_name = f"{otf_var}_{entity}"
            df_otf_final.rename(columns={otf_col: final_col_name}, inplace=True)

            data_frames.append(df_otf_final)
            units[final_col_name] = "fraction"

        return data_frames, units

    @log_execution
    def process_BSW(self, entities, monthly_avg, yearly_avg, ref_date_str, annual_rate):
        """
        Processa a variável BSW para uma lista de entities.
        Retorna lista de DataFrames e um dicionário de unidades correspondentes.
        """
        data_frames = []
        units = {}

        for entity in entities:
            try:
                np_data = self.read_variable_for_origin('Cumulative Oil SC', entity)
                wp_data = self.read_variable_for_origin('Cumulative Water SC', entity)
                # Gás não é necessário para BSW, mas veremos se iremos ler do mesmo jeito
                # gp_data = self.read_variable_for_origin('Cumulative Gas SC', entity)

                if np_data.name is None:
                    np_data.name = "Cumulative Oil SC"
                if wp_data.name is None:
                    wp_data.name = "Cumulative Water SC"

                np_df = np_data.to_dataframe().reset_index()
                wp_df = wp_data.to_dataframe().reset_index()

                merged_df = pd.merge(np_df, wp_df, on='Date', how='outer', suffixes=('_Np', '_Wp'))
                merged_df.sort_values('Date', inplace=True)
                merged_df = merged_df.ffill().fillna(0)

                if monthly_avg or yearly_avg:
                    merged_df = self._compute_monthly_yearly_avg_for_bsw_rgo(merged_df, monthly_avg, yearly_avg)

                # deltaNp e deltaWp
                delta_np = merged_df['Cumulative Oil SC'].diff().fillna(merged_df['Cumulative Oil SC'])
                delta_wp = merged_df['Cumulative Water SC'].diff().fillna(merged_df['Cumulative Water SC'])
                result_dates = merged_df['Date']

                if monthly_avg or yearly_avg:
                    # No método _compute_monthly_yearly_avg_for_bsw_rgo, a lógica já retorna
                    # "differences" e ajusta 'Date' para os blocos mensais/anuais
                    # Então podemos reatribuir:
                    delta_np = merged_df['deltaNp']
                    delta_wp = merged_df['deltaWp']
                    # result_dates = merged_df['resultDate']
                    result_dates = merged_df['Date']

                delta_np = delta_np.fillna(0)
                delta_wp = delta_wp.fillna(0)

                bsw_series = delta_wp / (delta_wp + delta_np)
                bsw_series = bsw_series.fillna(0)

                df_bsw = pd.DataFrame({'Date': result_dates, f"BSW_{entity}": bsw_series})
                data_frames.append(df_bsw)
                units[f"BSW_{entity}"] = 'fraction'

            except ValueError as e:
                print(f"Warning: {e}")
                continue

        return data_frames, units

    @log_execution
    def process_RGO(self, entities, monthly_avg, yearly_avg, ref_date_str, annual_rate):
        """
        Processa a variável RGO para uma lista de entities (baseando-se em Np e Gp).
        Retorna lista de DataFrames e um dicionário de unidades correspondentes.
        """
        data_frames = []
        units = {}

        for entity in entities:
            try:
                np_data = self.read_variable_for_origin('Cumulative Oil SC', entity)
                gp_data = self.read_variable_for_origin('Cumulative Gas SC', entity)

                if np_data.name is None:
                    np_data.name = "Cumulative Oil SC"
                if gp_data.name is None:
                    gp_data.name = "Cumulative Gas SC"

                # Precisamos alinhar as unidades (ex: bbl vs scf ou m3 vs Mm3)
                np_unit = np_data.attrs.get('units', 'unknown')
                gp_unit = gp_data.attrs.get('units', 'unknown')
                gp_data_converted = gp_data

                if np_unit != gp_unit:
                    conversion_factor = self.get_conversion_factor(gp_unit, np_unit)
                    if conversion_factor is None:
                        raise ValueError(f"Cannot convert from {gp_unit} to {np_unit} for entity {entity}.")
                    else:
                        gp_data_converted = gp_data * conversion_factor
                        gp_unit = np_unit

                np_df = np_data.to_dataframe().reset_index()
                gp_df = gp_data_converted.to_dataframe().reset_index()
                merged_df = pd.merge(np_df, gp_df, on='Date', how='outer', suffixes=('_Np', '_Gp'))
                merged_df.sort_values('Date', inplace=True)
                merged_df = merged_df.ffill().fillna(0)

                if monthly_avg or yearly_avg:
                    merged_df = self._compute_monthly_yearly_avg_for_bsw_rgo(merged_df, monthly_avg, yearly_avg)

                # deltaNp e deltaGp
                delta_np = merged_df['Cumulative Oil SC'].diff().fillna(merged_df['Cumulative Oil SC'])
                delta_gp = merged_df['Cumulative Gas SC'].diff().fillna(merged_df['Cumulative Gas SC'])
                result_dates = merged_df['Date']

                if monthly_avg or yearly_avg:
                    delta_np = merged_df['deltaNp']
                    delta_gp = merged_df['deltaGp']
                    # result_dates = merged_df['resultDate']
                    result_dates = merged_df['Date']

                delta_np = delta_np.fillna(0)
                delta_gp = delta_gp.fillna(0)

                rgo_series = delta_gp / delta_np
                rgo_series = rgo_series.fillna(0)

                df_rgo = pd.DataFrame({'Date': result_dates, f"RGO_{entity}": rgo_series})
                data_frames.append(df_rgo)
                # Definimos a unidade (gp_unit / np_unit)
                rgo_unit = f"{gp_unit}/{np_unit}"
                units[f"RGO_{entity}"] = rgo_unit

            except ValueError as e:
                print(f"Warning: {e}")
                continue

        return data_frames, units

    @log_execution
    def process_IP(self, entities, monthly_avg, yearly_avg, ref_date_str, annual_rate):
        """
        Processa a variável IP = Ql / (WellMobWeightedDatum - BHP).

        Se BHP e WellMobWeightedDatum estiverem em kPa, convertemos para kgf/cm2
        antes de fazer a subtração. Desta forma, IP resultará em (unidade de Ql)/(kgf/cm2).

        Retorna lista de DataFrames e um dicionário de unidades correspondentes.
        """
        data_frames = []
        units = {}

        datum_var = "Well Mobility-Weighted Datum Pressure - 9P"
        ql_var    = "Liquid Rate SC"
        bhp_var   = "BHP"

        # Fator de conversão de kPa para kgf/cm2:
        # 1 kPa = 0.0101972 kgf/cm2  (aprox.)
        kpa_to_kgf = 0.0101972

        for entity in entities:
            try:
                # 1) Ler cada variável (BHP, Ql e Datum)
                bhp_data   = self.read_variable_for_origin(bhp_var, entity)
                ql_data    = self.read_variable_for_origin(ql_var, entity)
                datum_data = self.read_variable_for_origin(datum_var, entity)
            except ValueError as e:
                print(f"Warning: Falha ao ler BHP/Ql/Datum para {entity}: {e}")
                continue

            # Nomes internos para cada array
            if bhp_data.name is None:
                bhp_data.name = bhp_var
            if ql_data.name is None:
                ql_data.name = ql_var
            if datum_data.name is None:
                datum_data.name = datum_var

            # 2) Converter para DataFrame e fazer merge por 'Date'
            df_bhp = bhp_data.to_dataframe().reset_index()    # colunas: ['Date', 'BHP']
            df_ql  = ql_data.to_dataframe().reset_index()     # colunas: ['Date', 'Liquid Rate SC']
            df_dtm = datum_data.to_dataframe().reset_index()  # colunas: ['Date', 'Well Mobility-Weighted ...']

            df_merge = pd.merge(df_bhp, df_ql, on='Date', how='outer')
            df_merge = pd.merge(df_merge, df_dtm, on='Date', how='outer')
            df_merge.sort_values('Date', inplace=True)
            df_merge.ffill(inplace=True)   # Preenche valores faltantes
            df_merge.fillna(0, inplace=True)

            # 3) Verificar unidades para saber se é kPa
            bhp_unit   = bhp_data.attrs.get('units', 'unknown').lower()
            ql_unit    = ql_data.attrs.get('units', 'unknown')
            datum_unit = datum_data.attrs.get('units', 'unknown').lower()

            # Caso BOTH BHP e Datum estejam em kPa, convertemos ambos para kgf/cm2
            if bhp_unit == 'kpa' and datum_unit == 'kpa':
                df_merge[bhp_var]   = df_merge[bhp_var]   * kpa_to_kgf
                df_merge[datum_var] = df_merge[datum_var] * kpa_to_kgf
                # Agora, internamente, estamos em kgf/cm2
                bhp_unit   = 'kgf/cm2'
                datum_unit = 'kgf/cm2'

            # 4) Calcular IP = Ql / (Datum - BHP)
            denominator = df_merge[datum_var] - df_merge[bhp_var]
            # Evitar divisões por zero ou negativas
            mask_zero = (denominator.abs() < 1e-12)  # ou outro critério
            denominator[mask_zero] = np.nan

            df_merge['IP'] = df_merge[ql_var] / denominator
            df_merge["IP"] = df_merge["IP"].replace([np.inf, -np.inf], np.nan)
            df_merge["IP"] = df_merge["IP"].fillna(0)

            # 5) Caso --monthly_avg ou --yearly_avg, tratar IP como "instantâneo"
            if monthly_avg or yearly_avg:
                df_for_avg = df_merge[['Date','IP']].copy()
                df_for_avg = self._compute_monthly_yearly_avg_instantaneous(
                    df_for_avg, 'IP', monthly_avg, yearly_avg
                )
                df_ip = df_for_avg
            else:
                # Manter a série original
                df_ip = df_merge[['Date','IP']].copy()

            # 6) Renomear a coluna para IP_<entity>
            ip_col_name = f"IP_{entity}"
            df_ip.rename(columns={'IP': ip_col_name}, inplace=True)
            data_frames.append(df_ip)

            # 7) Montar a unidade final de IP
            #    Se BHP/Datum viraram "kgf/cm2", então IP está em (Ql_unit)/(kgf/cm2)
            #    ex: "m3/d / kgf/cm2"
            if bhp_unit == 'kgf/cm2' and datum_unit == 'kgf/cm2':
                ip_unit = f"{ql_unit}/kgf/cm2"
            else:
                # Caso não tenham sido kPa, ou haja unidade diferente,
                # deixamos genérico (ex: "m3/d / bar") ou "unknown"
                ip_unit = f"{ql_unit}/{bhp_unit}"  # simples

            units[ip_col_name] = ip_unit

        return data_frames, units

    @log_execution
    def process_Npe(self, entities, monthly_avg, yearly_avg, ref_date_str, annual_rate):
        """
        Processa a variável Npe para uma lista de entities (Npe = Np + Gp/1000).
        Retorna lista de DataFrames e um dicionário de unidades correspondentes.
        """
        data_frames = []
        units = {}

        for entity in entities:
            try:
                np_data = self.read_variable_for_origin('Cumulative Oil SC', entity)
                gp_data = self.read_variable_for_origin('Cumulative Gas SC', entity)

                if np_data.name is None:
                    np_data.name = "Cumulative Oil SC"
                if gp_data.name is None:
                    gp_data.name = "Cumulative Gas SC"

                # Ajustar unidades
                np_unit = np_data.attrs.get('units', 'unknown')
                gp_unit = gp_data.attrs.get('units', 'unknown')
                gp_data_converted = gp_data

                if np_unit != gp_unit:
                    conversion_factor = self.get_conversion_factor(gp_unit, np_unit)
                    if conversion_factor is None:
                        raise ValueError(f"Cannot convert from {gp_unit} to {np_unit} for entity {entity}.")
                    else:
                        gp_data_converted = gp_data * conversion_factor
                        gp_unit = np_unit

                np_df = np_data.to_dataframe().reset_index()
                gp_df = gp_data_converted.to_dataframe().reset_index()

                merged_df = pd.merge(np_df, gp_df, on='Date', how='outer', suffixes=('_Np', '_Gp'))
                merged_df.sort_values('Date', inplace=True)
                merged_df = merged_df.ffill().fillna(0)

                if monthly_avg or yearly_avg:
                    # Interpolação das cumulativas para datas mensais ou anuais
                    merged_df = self._compute_monthly_yearly_avg_for_npe(merged_df, monthly_avg, yearly_avg)

                merged_df['Npe'] = merged_df['Cumulative Oil SC'] + merged_df['Cumulative Gas SC'] / 1e3

                df_npe = merged_df[['Date', 'Npe']].copy()
                df_npe = df_npe.rename(columns={'Npe': f"Npe_{entity}"})
                data_frames.append(df_npe)
                units[f"Npe_{entity}"] = np_unit  # mesma unidade do Oil SC, assumindo que Gp foi convertido.

            except ValueError as e:
                print(f"Warning: {e}")
                continue

        return data_frames, units

    @log_execution
    def process_NpeStar(self, entities, monthly_avg, yearly_avg, ref_date_str, annual_rate):
        """
        Processa a variável Npe* (igual a Npe, mas descontada temporalmente usando ref_date e annual_rate).
        Retorna lista de DataFrames e um dicionário de unidades correspondentes.
        """
        data_frames = []
        units = {}

        if not ref_date_str or annual_rate is None:
            print("Error: 'Npe*' requires '--ref_date' and '--annual_rate'.")
            return data_frames, units

        ref_date = pd.to_datetime(ref_date_str)
        monthly_rate = (1 + annual_rate)**(1/12) - 1

        for entity in entities:
            try:
                np_data = self.read_variable_for_origin('Cumulative Oil SC', entity)
                gp_data = self.read_variable_for_origin('Cumulative Gas SC', entity)

                if np_data.name is None:
                    np_data.name = "Cumulative Oil SC"
                if gp_data.name is None:
                    gp_data.name = "Cumulative Gas SC"

                # Ajustar unidades
                np_unit = np_data.attrs.get('units', 'unknown')
                gp_unit = gp_data.attrs.get('units', 'unknown')
                gp_data_converted = gp_data

                if np_unit != gp_unit:
                    conversion_factor = self.get_conversion_factor(gp_unit, np_unit)
                    if conversion_factor is None:
                        raise ValueError(f"Cannot convert from {gp_unit} to {np_unit} for entity {entity}.")
                    else:
                        gp_data_converted = gp_data * conversion_factor
                        gp_unit = np_unit

                np_df = np_data.to_dataframe().reset_index()
                gp_df = gp_data_converted.to_dataframe().reset_index()

                merged_df = pd.merge(np_df, gp_df, on='Date', how='outer', suffixes=('_Np', '_Gp'))
                merged_df.sort_values('Date', inplace=True)
                merged_df = merged_df.ffill().fillna(0)

                if monthly_avg or yearly_avg:
                    merged_df = self._compute_monthly_yearly_avg_for_npe(merged_df, monthly_avg, yearly_avg)

                # Npe = Np + Gp/1000
                merged_df['Npe'] = merged_df['Cumulative Oil SC'] + merged_df['Cumulative Gas SC'] / 1e3
                merged_df['dNpe'] = merged_df['Npe'].diff().fillna(merged_df['Npe'])

                # Calculamos discount factor com base em months entre ref_date e Date
                merged_df['delta_months'] = merged_df['Date'].apply(lambda x: (x.year - ref_date.year)*12 + (x.month - ref_date.month))
                merged_df['discount_factor'] = (1 + monthly_rate) ** merged_df['delta_months']
                # Só começamos a contar a partir de ref_date
                merged_df.loc[merged_df['delta_months'] < 0, 'discount_factor'] = np.inf

                merged_df['Npe*_calc'] = merged_df.apply(
                    lambda row: row['dNpe'] / row['discount_factor'] if row['discount_factor'] != np.inf else 0,
                    axis=1
                )
                merged_df['Npe*'] = merged_df['Npe*_calc'].cumsum()

                df_npestar = merged_df[['Date', 'Npe*']].copy()
                df_npestar = df_npestar.rename(columns={'Npe*': f"Npe*_{entity}"})
                data_frames.append(df_npestar)
                units[f"Npe*_{entity}"] = np_unit

            except ValueError as e:
                print(f"Warning: {e}")
                continue

        return data_frames, units

    @log_execution
    def process_gas_mole_frac_co2_sc(self, entities, monthly_avg, yearly_avg, ref_date_str, annual_rate):
        """
        Processa a variável "Gas Mole Frac(CO2) SC" para uma lista de entities (poços).
        Retorna lista de DataFrames e um dicionário de unidades correspondentes.
        """
        data_frames = []
        units = {}

        for entity in entities:
            # # GMSF(CO2) só faz sentido para poços
            # if entity not in self.well.coords['origin'].values:
            #     continue

            df_co2 = self.compute_gas_mole_frac_co2_for_origin(entity)
            if df_co2.empty:
                continue

            if monthly_avg or yearly_avg:
                # Precisamos interpolar como variável instantânea
                # 1) Monta a lista de datas de referência
                all_dates = df_co2['Date']
                if len(all_dates) == 0:
                    continue

                start_date = all_dates.min()
                end_date = all_dates.max()

                if monthly_avg:
                    avg_dates = pd.date_range(start=start_date.to_period('M').to_timestamp(),
                                            end=(end_date.to_period('M') + 1).to_timestamp(),
                                            freq='MS')
                else:  # yearly_avg
                    avg_dates = pd.date_range(start=start_date.to_period('Y').to_timestamp(),
                                            end=(end_date.to_period('Y') + 1).to_timestamp(),
                                            freq='YS')

                from scipy import interpolate
                f = interpolate.interp1d(df_co2['Date'].astype(int),
                                        df_co2["Gas Mole Frac(CO2) SC"],
                                        kind='linear',
                                        fill_value='extrapolate')
                frac_avg = f(avg_dates.astype(int))
                frac_avg[avg_dates < start_date] = 0.0

                df_co2_avg = pd.DataFrame({'Date': avg_dates, f"Gas Mole Frac(CO2) SC_{entity}": frac_avg})
                data_frames.append(df_co2_avg)
                units[f"Gas Mole Frac(CO2) SC_{entity}"] = 'fraction'
            else:
                # Mantém como está
                df_co2 = df_co2.rename(columns={"Gas Mole Frac(CO2) SC": f"Gas Mole Frac(CO2) SC_{entity}"})
                data_frames.append(df_co2)
                units[f"Gas Mole Frac(CO2) SC_{entity}"] = 'fraction'

        return data_frames, units

    # def process_Qgco2(self, entities, monthly_avg, yearly_avg, ref_date_str, annual_rate):
    #     """
    #     Processa a variável Qgco2 = Qg * Gas Mole Frac(CO2) SC para uma lista de entities (poços).
    #     Retorna (data_frames, units), onde cada DataFrame contém ['Date', 'Qgco2_<entity>'].
    #     """
    #     data_frames = []
    #     units = {}

    #     # Para cada entidade (normalmente poço), calculamos:
    #     for entity in entities:
    #         # # "Gas Mole Frac(CO2) SC" só faz sentido se for poço.
    #         # if entity not in self.well.coords['origin'].values:
    #         #     # Se desejar, você pode simplesmente ignorar ou emitir um aviso.
    #         #     print(f"Warning: 'Qgco2' não faz sentido para entidade '{entity}' que não é poço. Ignorando.")
    #         #     continue

    #         # 1) Obter Qg (taxa de gás)
    #         #    Reaproveitando process_regular_variable para 'Qg'.
    #         #    Isso já aplica monthly_avg/yearly_avg, se solicitado.
    #         try:
    #             df_list_qg, units_qg = self.process_regular_variable(
    #                 var_name="Qg",
    #                 entity=entity,
    #                 monthly_avg=monthly_avg,
    #                 yearly_avg=yearly_avg,
    #                 ref_date_str=ref_date_str,
    #                 annual_rate=annual_rate
    #             )
    #             # A chamada retorna uma lista de dataframes (em geral, 1 só) + um dicionário de unidades
    #             if not df_list_qg:
    #                 continue
    #             df_qg = df_list_qg[0]  # Deve ter colunas: ['Date', 'Qg_<entity>']

    #             # Descobrimos a unidade de Qg para usar em Qgco2
    #             # Exemplo: units_qg["Qg_<entity>"] pode ser algo como "m3/day"
    #             qg_col = f"Qg_{entity}"
    #             qg_unit = units_qg.get(qg_col, "unknown")
    #         except ValueError as e:
    #             print(f"Warning: falha ao ler 'Qg' para {entity}: {e}")
    #             continue

    #         # 2) Obter "Gas Mole Frac(CO2) SC"
    #         #    Reaproveitando process_gas_mole_frac_co2_sc, que também suporta monthly_avg/yearly_avg.
    #         try:
    #             df_list_co2, units_co2 = self.process_gas_mole_frac_co2_sc(
    #                 entities=[entity],
    #                 monthly_avg=monthly_avg,
    #                 yearly_avg=yearly_avg,
    #                 ref_date_str=ref_date_str,
    #                 annual_rate=annual_rate
    #             )
    #             if not df_list_co2:
    #                 # Se não existe, pula
    #                 continue
    #             df_co2 = df_list_co2[0]  # Deve ter colunas: ['Date', 'Gas Mole Frac(CO2) SC_<entity>']
    #         except ValueError as e:
    #             print(f"Warning: falha ao ler 'Gas Mole Frac(CO2) SC' para {entity}: {e}")
    #             continue

    #         # 3) Fazer merge das duas séries e calcular Qgco2
    #         df_merged = pd.merge(df_qg, df_co2, on='Date', how='outer').fillna(0.0)

    #         # Nomes das colunas que vieram
    #         qg_column = f"Qg_{entity}"
    #         co2_column = f"Gas Mole Frac(CO2) SC_{entity}"
    #         qgco2_column = f"Qgco2_{entity}"

    #         df_merged[qgco2_column] = df_merged[qg_column] * df_merged[co2_column]

    #         # 4) Montar o DataFrame final e guardar
    #         df_final = df_merged[['Date', qgco2_column]].copy()
    #         data_frames.append(df_final)

    #         # 5) Definir a unidade de Qgco2
    #         #    Como é Qg (e.g. "m3/d") multiplicado por uma fração (adimensional),
    #         #    a unidade final é a mesma de Qg.
    #         units[qgco2_column] = qg_unit

    #     return data_frames, units

    @log_execution
    def process_Qgco2(self, entities, monthly_avg, yearly_avg, ref_date_str, annual_rate):
        """
        Processa a variável Qgco2 = Qg * Gas Mole Frac(CO2) SC para uma lista de entities.
        Caso Qg > 0 e Gas Mole Frac(CO2) SC_{entity} == 0, será usado o valor da linha seguinte.
        Retorna (data_frames, units), onde cada DataFrame contém ['Date', 'Qgco2_<entity>'].
        """
        data_frames = []
        units = {}

        for entity in entities:
            try:
                # 1) Obter Qg
                df_list_qg, units_qg = self.process_regular_variable(
                    var_name="Qg",
                    entity=entity,
                    monthly_avg=monthly_avg,
                    yearly_avg=yearly_avg,
                    ref_date_str=ref_date_str,
                    annual_rate=annual_rate
                )
                if not df_list_qg:
                    continue
                df_qg = df_list_qg[0]  # Deve ter ['Date', f"Qg_{entity}"]

                qg_col = f"Qg_{entity}"
                qg_unit = units_qg.get(qg_col, "unknown")
            except ValueError as e:
                print(f"Warning: falha ao ler 'Qg' para {entity}: {e}")
                continue

            try:
                # 2) Obter "Gas Mole Frac(CO2) SC"
                df_list_co2, units_co2 = self.process_gas_mole_frac_co2_sc(
                    entities=[entity],
                    monthly_avg=monthly_avg,
                    yearly_avg=yearly_avg,
                    ref_date_str=ref_date_str,
                    annual_rate=annual_rate
                )
                if not df_list_co2:
                    continue
                df_co2 = df_list_co2[0]  # Deve ter ['Date', f"Gas Mole Frac(CO2) SC_{entity}"]
            except ValueError as e:
                print(f"Warning: falha ao ler 'Gas Mole Frac(CO2) SC' para {entity}: {e}")
                continue

            # 3) Merge e ajuste para substituir zero por valor da próxima linha
            df_merged = pd.merge(df_qg, df_co2, on='Date', how='outer').fillna(0.0)

            qg_column = f"Qg_{entity}"
            co2_column = f"Gas Mole Frac(CO2) SC_{entity}"
            qgco2_column = f"Qgco2_{entity}"

            # 3.1) Criar coluna auxiliar com o valor "da próxima linha" de CO2
            df_merged["co2_next"] = df_merged[co2_column].shift(-1)

            # 3.2) Substituir co2_column pelo co2_next onde Qg > 0 e co2_column == 0
            mask = (df_merged[qg_column] > 0) & (df_merged[co2_column] == 0)
            df_merged.loc[mask, co2_column] = df_merged.loc[mask, "co2_next"]

            # 3.3) Remover a coluna auxiliar
            df_merged.drop(columns=["co2_next"], inplace=True)

            # 4) Calcular Qgco2
            df_merged[qgco2_column] = df_merged[qg_column] * df_merged[co2_column]

            # 5) Criar DataFrame final e salvar
            df_final = df_merged[["Date", qgco2_column]].copy()
            data_frames.append(df_final)

            # 6) Unidade de Qgco2 -> mesma de Qg (fração é adimensional)
            units[qgco2_column] = qg_unit

        return data_frames, units

    @log_execution
    def process_Qghc(self, entities, monthly_avg, yearly_avg, ref_date_str, annual_rate):
        """
        Processa a variável Qghc = Qg * (1 - Gas Mole Frac(CO2) SC) para uma lista de entities (poços).
        Caso Qg > 0 e Gas Mole Frac(CO2) SC_{entity} == 0, será usado o valor da linha seguinte
        no lugar do 0. Retorna (data_frames, units), onde cada DataFrame contém ['Date', 'Qghc_<entity>'].
        """
        data_frames = []
        units = {}

        # Para cada entidade (poço), calculamos Qghc.
        for entity in entities:
            # 1) Obter Qg (taxa de gás) usando process_regular_variable
            try:
                df_list_qg, units_qg = self.process_regular_variable(
                    var_name="Qg",
                    entity=entity,
                    monthly_avg=monthly_avg,
                    yearly_avg=yearly_avg,
                    ref_date_str=ref_date_str,
                    annual_rate=annual_rate
                )
                if not df_list_qg:
                    continue
                df_qg = df_list_qg[0]  # Deve ter colunas: ['Date', 'Qg_<entity>']

                qg_col = f"Qg_{entity}"
                qg_unit = units_qg.get(qg_col, "unknown")
            except ValueError as e:
                print(f"Warning: falha ao ler 'Qg' para {entity}: {e}")
                continue

            # 2) Obter "Gas Mole Frac(CO2) SC" usando process_gas_mole_frac_co2_sc
            try:
                df_list_co2, units_co2 = self.process_gas_mole_frac_co2_sc(
                    entities=[entity],
                    monthly_avg=monthly_avg,
                    yearly_avg=yearly_avg,
                    ref_date_str=ref_date_str,
                    annual_rate=annual_rate
                )
                if not df_list_co2:
                    continue
                df_co2 = df_list_co2[0]  # Deve ter colunas: ['Date', 'Gas Mole Frac(CO2) SC_<entity>']
            except ValueError as e:
                print(f"Warning: falha ao ler 'Gas Mole Frac(CO2) SC' para {entity}: {e}")
                continue

            # 3) Merge das duas séries e, caso co2 seja 0 e Qg>0, usar valor da próxima linha
            df_merged = pd.merge(df_qg, df_co2, on='Date', how='outer').fillna(0.0)

            qg_column = f"Qg_{entity}"
            co2_column = f"Gas Mole Frac(CO2) SC_{entity}"
            qghc_column = f"Qghc_{entity}"

            # 3.1) Criar coluna auxiliar com o valor "da próxima linha" de CO2
            df_merged["co2_next"] = df_merged[co2_column].shift(-1)

            # 3.2) Substituir co2_column pelo co2_next onde Qg > 0 e co2_column == 0
            mask = (df_merged[qg_column] > 0) & (df_merged[co2_column] == 0)
            df_merged.loc[mask, co2_column] = df_merged.loc[mask, "co2_next"]

            # 3.3) Remover coluna auxiliar
            df_merged.drop(columns=["co2_next"], inplace=True)

            # 4) Calcular Qghc
            df_merged[qghc_column] = df_merged[qg_column] * (1 - df_merged[co2_column])

            # 5) Criar DataFrame final e adicionar à lista
            df_final = df_merged[['Date', qghc_column]].copy()
            data_frames.append(df_final)

            # 6) Definir a unidade (é a mesma de Qg, pois a fração é adimensional)
            units[qghc_column] = qg_unit

        return data_frames, units

    @log_execution
    def export_variables(self, export_list):
        """
        Refatoração do método 'export_variables' para ficar mais organizado,
        chamando cada método auxiliar para calcular cada tipo de variável.
        """
        for item in export_list:
            variables = item["variables"]
            entities = item["entities"]
            csv_path = item["csv_path"]
            monthly_avg = item.get("monthly_avg", False)
            yearly_avg = item.get("yearly_avg", False)
            ref_date_str = item.get("ref_date")
            annual_rate = item.get("annual_rate")

            # Separamos as variaveis especiais
            special_variables = {
                'BSW',
                'RGO',
                'Npe',
                'Npe*',
                'Gas Mole Frac(CO2) SC',
                'Qgco2',
                'Qghc',
                'IP',
                'On-time Fraction',
            }
            variables_to_compute = set(var for var in variables if var in special_variables)
            # As regulares sao aquelas que nao estao em special_variables
            regular_variables = [var for var in variables if var not in special_variables]

            # Colecoes para consolidar resultados
            data_frames = []
            units_dict = {}

            # 1) Tratar as variaveis "regulares"
            for var_name in regular_variables:
                for entity in entities:
                    try:
                        dfs_var, units_var = self.process_regular_variable(
                            var_name, entity, monthly_avg, yearly_avg, ref_date_str, annual_rate
                        )
                        data_frames.extend(dfs_var)
                        units_dict.update(units_var)
                    except ValueError as e:
                        print(f"Warning: {e}")
                        continue

            # 2) Tratar cada variável especial separadamente
            if 'BSW' in variables_to_compute:
                dfs_bsw, units_bsw = self.process_BSW(entities, monthly_avg, yearly_avg, ref_date_str, annual_rate)
                data_frames.extend(dfs_bsw)
                units_dict.update(units_bsw)

            if 'RGO' in variables_to_compute:
                dfs_rgo, units_rgo = self.process_RGO(entities, monthly_avg, yearly_avg, ref_date_str, annual_rate)
                data_frames.extend(dfs_rgo)
                units_dict.update(units_rgo)

            if 'Npe' in variables_to_compute:
                dfs_npe, units_npe = self.process_Npe(entities, monthly_avg, yearly_avg, ref_date_str, annual_rate)
                data_frames.extend(dfs_npe)
                units_dict.update(units_npe)

            if 'Npe*' in variables_to_compute:
                dfs_npe_star, units_npe_star = self.process_NpeStar(entities, monthly_avg, yearly_avg, ref_date_str, annual_rate)
                data_frames.extend(dfs_npe_star)
                units_dict.update(units_npe_star)

            if 'Gas Mole Frac(CO2) SC' in variables_to_compute:
                dfs_co2, units_co2 = self.process_gas_mole_frac_co2_sc(entities, monthly_avg, yearly_avg, ref_date_str, annual_rate)
                data_frames.extend(dfs_co2)
                units_dict.update(units_co2)

            if 'Qgco2' in variables_to_compute:
                dfs_qgco2, units_qgco2 = self.process_Qgco2(
                    entities, monthly_avg, yearly_avg, ref_date_str, annual_rate
                )
                data_frames.extend(dfs_qgco2)
                units_dict.update(units_qgco2)

            if 'Qghc' in variables_to_compute:
                dfs_qghc, units_qghc = self.process_Qghc(
                    entities, monthly_avg, yearly_avg, ref_date_str, annual_rate
                )
                data_frames.extend(dfs_qghc)
                units_dict.update(units_qghc)

            if 'IP' in variables_to_compute:
                dfs_ip, units_ip = self.process_IP(
                    entities, monthly_avg, yearly_avg, ref_date_str, annual_rate
                )
                data_frames.extend(dfs_ip)
                units_dict.update(units_ip)

            if 'On-time Fraction' in variables_to_compute:
                dfs_otf, units_otf = self.process_on_time_fraction(entities, monthly_avg, yearly_avg, ref_date_str, annual_rate)
                data_frames.extend(dfs_otf)
                units_dict.update(units_otf)

            # 3) Se houver DataFrames, fazemos o merge final e escrevemos no CSV
            if data_frames:
                result_df = data_frames[0]
                for df in data_frames[1:]:
                    result_df = pd.merge(result_df, df, on='Date', how='outer')

                # Preparar cabeçalho multi-linha
                entity_row = {}
                variable_row = {}
                units_row = {}
                for col in result_df.columns:
                    if col == 'Date':
                        entity_row[col] = ''
                        variable_row[col] = 'Date'
                        units_row[col] = ''
                    else:
                        var_entity = col.split('_')
                        var_name = var_entity[0]
                        entity_name = '_'.join(var_entity[1:])
                        entity_row[col] = entity_name
                        variable_row[col] = var_name
                        units_row[col] = units_dict.get(col, '')

                header_df = pd.DataFrame([entity_row, variable_row, units_row])
                result_df = pd.concat([header_df, result_df], ignore_index=True)

                # Salva no CSV (aqui usando tab como separador, mas pode ser vírgula se quiser)
                result_df.to_csv(csv_path, sep='\t', index=False, header=False)
                print(f"Data successfully saved to {csv_path}")
            else:
                print(f"No data found for variables {variables} and entities {entities}")

    @log_execution
    def get_conversion_factor(self, from_unit, to_unit):
        from_unit = from_unit.strip().lower()
        to_unit = to_unit.strip().lower()

        from_unit_info = self.conversion_table.get(from_unit)
        to_unit_info = self.conversion_table.get(to_unit)

        if not from_unit_info or not to_unit_info:
            return None

        if from_unit_info['Dimensionality'] != to_unit_info['Dimensionality']:
            return None

        Gain_A = from_unit_info['Gain']
        Offset_A = from_unit_info['Offset']
        Gain_B = to_unit_info['Gain']
        Offset_B = to_unit_info['Offset']

        conversion_factor = Gain_A / Gain_B
        return conversion_factor

    @log_execution
    def get_variable_type(self, var_name):
        cumulative_keywords = ['cumulative', 'np', 'wp', 'gp']
        rate_keywords = ['rate', 'qo', 'qw', 'qg', 'qgl']

        var_name_lower = var_name.lower()
        if any(keyword in var_name_lower for keyword in cumulative_keywords):
            return 'cumulative'
        elif any(keyword in var_name_lower for keyword in rate_keywords):
            return 'rate'
        else:
            return 'instantaneous'

    @log_execution
    def get_cumulative_var_name(self, var_name):
        rate_to_cumulative = {
            'Oil Rate SC': 'Cumulative Oil SC',
            'Water Rate SC': 'Cumulative Water SC',
            'Gas Rate SC': 'Cumulative Gas SC',
            'Liquid Rate SC': 'Cumulative Liquid SC',
            'Qo': 'Cumulative Oil SC',
            'Qw': 'Cumulative Water SC',
            'Qg': 'Cumulative Gas SC',
            'Qgl': 'Cumulative lift gas injected per well',
            'Lift Gas Rate injected per well': 'Cumulative lift gas injected per well',
            'Oil Prod Rate SCTR': 'Oil Prod Cum SCTR',
            'Water Prod Rate SCTR': 'Water Prod Cum SCTR',
            'Gas Prod Rate SCTR': 'Gas Prod Cum SCTR',
            'Oil Inje Rate SCTR': 'Oil Inje Cum SCTR',
            'Water Inje Rate SCTR': 'Water Inje Cum SCTR',
            'Gas Inje Rate SCTR': 'Gas Inje Cum SCTR',
        }
        return rate_to_cumulative.get(var_name)

    @log_execution
    def export_well_coordinates(self, csv_path):
        well_coords = []
        mt_layers = self.layer_table[
            self.layer_table['Name'].str.contains('MT') |
            (~self.layer_table['Name'].str.contains('MT|FR'))
        ]

        for well_name in mt_layers['Parent'].unique():
            well_layers = mt_layers[mt_layers['Parent'] == well_name]
            x_coords = []
            y_coords = []

            for _, row in well_layers.iterrows():
                x_coords.extend([row['X Entry'], row['X Exit']])
                y_coords.extend([row['Y Entry'], row['Y Exit']])

            avg_x = sum(x_coords) / len(x_coords)
            avg_y = sum(y_coords) / len(y_coords)

            well_coords.append({
                'Poço': well_name,
                'x': avg_x,
                'y': avg_y
            })

        df = pd.DataFrame(well_coords)
        df.to_csv(csv_path, index=False)
        print(f"Well coordinates exported to {csv_path}")

    @log_execution
    def plot_all_wells(self, html_path):
        import plotly.graph_objects as go
        from plotly.subplots import make_subplots

        mt_layers = self.layer_table[
            self.layer_table['Name'].str.contains('MT') |
            (~self.layer_table['Name'].str.contains('MT|FR'))
        ]

        wells = mt_layers['Parent'].unique()
        fig = make_subplots(rows=1, cols=2,
                            specs=[[{'type': 'scene'}, {'type': 'xy'}]],
                            subplot_titles=('3D View', '2D Projection (Top View)'))

        all_x = []
        all_y = []
        all_z = []

        for well_name in wells:
            well_layers = mt_layers[mt_layers['Parent'] == well_name]
            segments = self.extract_segments(well_layers)

            x = []
            y = []
            z = []

            for entry_point, exit_point in segments:
                x.extend([entry_point[0], exit_point[0], None])
                y.extend([entry_point[1], exit_point[1], None])
                z.extend([entry_point[2], exit_point[2], None])

            all_x.extend([coord for coord in x if coord is not None])
            all_y.extend([coord for coord in y if coord is not None])
            all_z.extend([coord for coord in z if coord is not None])

            fig.add_trace(
                go.Scatter3d(
                    x=x,
                    y=y,
                    z=z,
                    mode='lines',
                    name=well_name,
                    line=dict(width=2)
                ),
                row=1, col=1
            )

            fig.add_trace(
                go.Scatter(
                    x=x,
                    y=y,
                    mode='lines+markers+text',
                    name=well_name,
                    line=dict(width=3),
                    marker=dict(size=6),
                ),
                row=1, col=2
            )

        x_min, x_max = min(all_x), max(all_x)
        y_min, y_max = min(all_y), max(all_y)
        z_min, z_max = min(all_z), max(all_z)

        x_padding = (x_max - x_min) * 0.05
        y_padding = (y_max - y_min) * 0.05
        z_padding = (z_max - z_min) * 0.05

        x_range = [x_min - x_padding, x_max + x_padding]
        y_range = [y_min - y_padding, y_max + y_padding]
        z_range = [z_min - z_padding, z_max + z_padding]

        x_range_size = x_range[1] - x_range[0]
        y_range_size = y_range[1] - y_range[0]
        z_range_size = z_range[1] - z_range[0]

        x_ratio = 1
        y_ratio = 1
        z_ratio = z_range_size / x_range_size * 2

        fig.update_layout(
            height=600,
            width=1200,
            title_text='Well Trajectories'
        )

        fig.update_scenes(
            xaxis_title='X',
            yaxis_title='Y',
            zaxis_title='Z',
            aspectmode='manual',
            aspectratio=dict(x=x_ratio, y=y_ratio, z=z_ratio),
            xaxis=dict(range=x_range),
            yaxis=dict(range=y_range),
            zaxis=dict(range=z_range)
        )

        fig.update_xaxes(title_text='X', row=1, col=2, range=x_range)
        fig.update_yaxes(title_text='Y', row=1, col=2, range=y_range)

        fig.write_html(html_path)
        print(f"Well trajectories figure saved to {html_path}")

    @log_execution
    def process_hphiso(self, hphiso_arg):
        if not hphiso_arg:
            return

        self.sim.load_properties(['BLOCKPVOL', 'SO'], load_geometry=True)
        self.grid = self.sim.grid
        self.grid['HPhiSo'] = (self.grid['BLOCKPVOL'] * self.grid['SO'] / self.grid['DX'] / self.grid['DY'])

        lists = hphiso_arg.split(',')
        for lst in lists:
            k_range = self.parse_k_range(lst)
            if k_range:
                start, end = k_range
                if start == -1:
                    start = 0

                xc_start = self.grid['xc'].sel(K=start, method='nearest')
                yc_start = self.grid['yc'].sel(K=start, method='nearest')

                try:
                    hphiso_sum = self.grid['HPhiSo'].sel(K=slice(start, end), model="matrix").sum(dim='K')
                except:
                    hphiso_sum = self.grid['HPhiSo'].sel(K=slice(start, end)).sum(dim='K')

                result = xr.DataArray(
                    data=hphiso_sum.values,
                    dims=['I', 'J', 'Date'],
                    coords={
                        'I': hphiso_sum.I,
                        'J': hphiso_sum.J,
                        'Date': hphiso_sum.Date,
                        'xc': (('I', 'J'), xc_start.values),
                        'yc': (('I', 'J'), yc_start.values)
                    },
                    name='HPhiSo'
                )

                base_name = os.path.splitext(os.path.basename(self.sr3Path))[0]
                if lst == ':':
                    filename = f"{base_name}.all.hphiso"
                else:
                    filename = f"{base_name}.{lst.replace(':', 'to')}.hphiso"

                path2pkl = os.path.join(os.path.dirname(self.sr3Path), filename)

                with open(path2pkl, 'wb') as f:
                    pickle.dump(result, f)

                print(f"HPhiSo data for K range {lst} saved to {path2pkl}")

    @log_execution
    def parse_k_range(self, k_str):
        if ':' not in k_str:
            try:
                k = int(k_str)
                return k - 1, k
            except ValueError:
                return None

        parts = k_str.split(':')
        if len(parts) != 2:
            return None

        start, end = parts
        max_k = len(self.grid.K)

        if start == '':
            start = 0
        else:
            try:
                start = int(start) - 1
            except ValueError:
                return None

        if end == '':
            end = None
        else:
            try:
                end = int(end)
                if end < 0:
                    end = max_k + end
            except ValueError:
                return None

        return start, end

@log_execution
def print_variables(title, variables):
    print(f"\n{title}:")
    sorted_vars = sorted(variables)
    columns = textwrap.wrap(','.join(sorted_vars), width=100)
    for line in columns:
        print(line)

@log_execution
def main():
    parser = argparse.ArgumentParser(description='Extract simulation data and save to CSV or list available variables, groups, and wells.')
    parser.add_argument('sr3_path', type=str, help='Path to the SR3 file.')
    parser.add_argument('--csv_path', type=str, help='Path to the output CSV file.')
    parser.add_argument('--entities', type=str, help='Comma-separated list of entities.')
    parser.add_argument('--variables', type=str, help='Comma-separated list of variables.')
    parser.add_argument('--group_vars', action='store_true', help='List available group variables')
    parser.add_argument('--well_vars', action='store_true', help='List available well variables')
    parser.add_argument('--sector_vars', action='store_true', help='List available sector variables')
    parser.add_argument('--list_groups', action='store_true', help='List all available groups')
    parser.add_argument('--list_wells', action='store_true', help='List all available wells')
    parser.add_argument('--list_sectors', action='store_true', help='List all available sectors')
    parser.add_argument('--monthly_avg', action='store_true', help='Calculate monthly averages for non-cumulative variables')
    parser.add_argument('--yearly_avg', action='store_true', help='Calculate yearly averages for non-cumulative variables')
    parser.add_argument('--ref_date', type=str, help='Reference date in YYYY-MM-DD format (required for Npe*)')
    parser.add_argument('--annual_rate', type=float, help='Annual rate as a decimal (e.g., 0.1 for 10%%) (required for Npe*)')
    parser.add_argument('--dist_pocos', type=str, help='Path to output CSV file for well distances.')
    parser.add_argument('--export_html', type=str, help='Path to output HTML file for well trajectories.')
    parser.add_argument('--export_well_coords', type=str, help='Path to output CSV file for well coordinates.')
    parser.add_argument('--hphiso', type=str, help='Comma-separated list of K layers to calculate HPhiSo')
    args = parser.parse_args()

    sr3_path = args.sr3_path
    sim = SimulationData(sr3_path)

    if args.group_vars or args.well_vars or args.sector_vars or args.list_groups or args.list_wells or args.list_sectors:
        if args.group_vars:
            print_variables("Available group variables", sim.list_variables('group'))
        if args.well_vars:
            print_variables("Available well variables", sim.list_variables('well'))
        if args.sector_vars:
            print_variables("Available sector variables", sim.list_variables('sector'))
        if args.list_groups:
            print_variables("Available groups", sim.list_all_groups())
        if args.list_wells:
            print_variables("Available wells", sim.list_all_wells())
        if args.list_sectors:
            print_variables("Available sectors", sim.list_all_sectors())
    else:
        fail_args = True

        if args.hphiso:
            # # Esta com um problema para leitura de arquivo do CO140. Por isso esta desabilitado por enquanto.
            # sim.process_hphiso(args.hphiso)
            fail_args = False

        if args.export_well_coords:
            sim.export_well_coordinates(args.export_well_coords)
            fail_args = False

        if args.export_html:
            sim.plot_all_wells(args.export_html)
            fail_args = False

        if args.dist_pocos:
            distance_df = sim.calculate_all_well_distances()
            distance_df.to_csv(args.dist_pocos)
            print(f"Distance matrix saved to {args.dist_pocos}")
            fail_args = False

        if args.entities and args.variables:
            if args.csv_path:
                csv_path = args.csv_path
            else:
                base, ext = os.path.splitext(sr3_path)
                csv_path = base + '.csv'

            entities = args.entities.split(',')
            variables = args.variables.split(',')

            expanded_entities = []
            for entity in entities:
                if entity == 'all_wells':
                    expanded_entities.extend(map(str, sim.list_all_wells()))
                elif entity == 'all_groups':
                    expanded_entities.extend(map(str, sim.list_all_groups()))
                elif entity == 'all_sectors':
                    expanded_entities.extend(map(str, sim.list_all_sectors()))
                else:
                    expanded_entities.append(entity)

            expanded_entities = list(dict.fromkeys(expanded_entities))  # remove duplicates preservando a ordem
            entities = expanded_entities
            while "" in entities:
                entities.remove("")
                print("Warning: removing '' element from entities.")

            if 'Npe*' in variables:
                if not args.ref_date or args.annual_rate is None:
                    print("Error: '--ref_date' and '--annual_rate' are required when requesting 'Npe*'.")
                    return

            export_list = [
                {
                    'variables': variables,
                    'entities': entities,
                    'csv_path': csv_path,
                    'monthly_avg': args.monthly_avg,
                    'yearly_avg': args.yearly_avg,
                    'ref_date': args.ref_date,
                    'annual_rate': args.annual_rate
                }
            ]

            try:
                sim.export_variables(export_list)
            except ValueError as e:
                print(e)
            fail_args = False

        if fail_args:
            parser.print_help()

if __name__ == "__main__":
    main()
