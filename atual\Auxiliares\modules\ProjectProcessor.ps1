#####################################################################################################################################
# ProjectProcessor.ps1 - Funcoes para processamento de projetos
#####################################################################################################################################

function Invoke-ProjectProcessing {
    param (
        [hashtable]$Projects,
        [string]$Variables,
        [hashtable]$PythonConfig,
        [string]$ExportPath
    )

    $python = $PythonConfig.executable
    $script = Join-Path $PythonConfig.script_dir $PythonConfig.script_name
    $command = $PythonConfig.command
    $excel_files = @()

    # Total de projetos
    $totalProjects = $Projects.Count
    $currentProject = 0

    foreach ($project in $Projects.GetEnumerator()) {
        $currentProject++
        $proj_name = $project.Key
        $proj_path = $project.Value.proj
        $base_path = $project.Value.base

        # Mensagens visiveis imediatamente
        Write-Host "`n[PROCESSANDO] Iniciando projeto ($currentProject de $totalProjects): $proj_name" -ForegroundColor Cyan
        Write-Host "Arquivo PROJ: $proj_path"
        Write-Host "Arquivo BASE: $base_path"

        $temp_excel = Join-Path $ExportPath "delta_$proj_name.xlsx"
        Write-Host "Saida Excel: $temp_excel"

        $arguments = @(
            $script,
            $command,
            $proj_path,
            $base_path,
            "--variables",
            $Variables,
            "--export_path",
            $temp_excel
        )

        $fullCommand = "$python $($arguments -join ' ')"
        Write-Host "Executando:`n$fullCommand" -ForegroundColor DarkGray

        try {
            # Executa o Python e captura saida/erros
            $result = & $python $arguments 2>&1

            # Exibe saida do Python em tempo real
            if ($result) {
                Write-Host "Saida do Python:" -ForegroundColor DarkYellow
                $result | ForEach-Object { Write-Host $_ -ForegroundColor DarkYellow }
            }

            if (Test-Path $temp_excel) {
                $excel_files += $temp_excel
                Write-Host "[SUCESSO] Projeto $proj_name concluido" -ForegroundColor Green
            } else {
                Write-Host "[FALHA] Arquivo nao gerado: $temp_excel" -ForegroundColor Red
            }
        } catch {
            Write-Host "[ERRO CRITICO] $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    return $excel_files
}

function Merge-ExcelFiles {
    param (
        [array]$ExcelFiles,
        [hashtable]$PythonConfig,
        [string]$ExportPath
    )

    if ($ExcelFiles.Count -eq 0) {
        throw "Nenhum arquivo Excel para mesclar."
    }

    Write-Host "`n[INICIANDO MESCLAGEM]" -ForegroundColor Cyan
    Write-Host "Arquivos a mesclar:" -ForegroundColor Cyan
    $ExcelFiles | ForEach-Object { Write-Host "  $_" }

    $merged_xlsx = Join-Path $ExportPath "delta_merged.xlsx"
    $python = $PythonConfig.executable
    $script = Join-Path $PythonConfig.script_dir $PythonConfig.script_name

    $arguments = @(
        $script,
        "merge_delta_exports"
    )
    $ExcelFiles | ForEach-Object { $arguments += $_ }
    $arguments += @(
        "--output",
        $merged_xlsx,
        "--file_name2proj"
    )

    $fullCommand = "$python $($arguments -join ' ')"
    Write-Host "Executando mesclagem:`n$fullCommand" -ForegroundColor DarkGray

    try {
        $mergeResult = & $python $arguments 2>&1

        if ($mergeResult) {
            Write-Host "Saida da mesclagem:" -ForegroundColor DarkYellow
            $mergeResult | ForEach-Object { Write-Host $_ -ForegroundColor DarkYellow }
        }

        if (Test-Path $merged_xlsx) {
            Write-Host "[MESCLAGEM CONCLUIDA] Arquivo final: $merged_xlsx" -ForegroundColor Green
            return $merged_xlsx
        } else {
            throw "Arquivo mesclado nao criado."
        }
    } catch {
        throw "ERRO NA MESCLAGEM: $($_.Exception.Message)"
    }
}