#####################################################################################################################################
# FileManager.ps1 - Funcoes para gerenciamento de arquivos
#####################################################################################################################################

function Save-ScriptCopy {
    <#
    .SYNOPSIS
    Salva uma copia do script no diretorio de saida

    .PARAMETER ScriptPath
    Caminho do script atual

    .PARAMETER ExportPath
    Diretorio de exportacao

    .PARAMETER OriginalScriptDir
    Diretorio original do script (para substituicao)
    #>
    param (
        [string]$ScriptPath,
        [string]$ExportPath,
        [string]$OriginalScriptDir
    )

    if (-not $ExportPath) {
        Write-Warning "Diretorio de exportacao nao definido. Nao sera salva copia do script."
        return
    }

    try {
        $path_without_extension = Join-Path -Path $ExportPath "delta_gen"
        $script_output_path = "$path_without_extension.ps1"

        # Obter o conteudo do script atual
        $script_content = Get-Content -Path $ScriptPath

        # Substituir a variavel $script_dir pelo diretorio original
        $script_content = $script_content -replace ' = \$PSScriptRoot # Diretorio do repositorio', " = `"$OriginalScriptDir`" # Diretorio do repositorio"

        # Salvar o conteudo no diretorio de saida
        $script_content | Out-File -FilePath $script_output_path -Encoding UTF8

        # Exibir mensagem de confirmacao
        Write-Output "O script foi salvo em: $script_output_path"
        Write-Output "Segue o comando para executar novamente:"
        Write-Output "powershell.exe -ExecutionPolicy Bypass -File `"$script_output_path`""

    } catch {
        Write-Warning "Erro ao salvar copia do script: $($_.Exception.Message)"
    }
}

function Remove-TemporaryFiles {
    <#
    .SYNOPSIS
    Remove arquivos temporarios se necessario

    .PARAMETER Files
    Array com os caminhos dos arquivos a serem removidos

    .PARAMETER KeepFiles
    Se verdadeiro, mantem os arquivos
    #>
    param (
        [array]$Files,
        [bool]$KeepFiles = $true
    )

    if ($KeepFiles) {
        Write-Output "Mantendo arquivos temporarios..."
        return
    }

    Write-Output "Removendo arquivos temporarios..."

    foreach ($file in $Files) {
        if (Test-Path $file) {
            try {
                Remove-Item $file -Force
                Write-Output "Arquivo removido: $file"
            } catch {
                Write-Warning "Nao foi possivel remover o arquivo: $file - $($_.Exception.Message)"
            }
        }
    }
}

function Test-FileExists {
    <#
    .SYNOPSIS
    Verifica se um arquivo existe e exibe mensagem apropriada

    .PARAMETER FilePath
    Caminho do arquivo

    .PARAMETER Description
    Descricao do arquivo para mensagem
    #>
    param (
        [string]$FilePath,
        [string]$Description = "Arquivo"
    )

    if (Test-Path $FilePath) {
        Write-Host "[OK] $Description existe: $FilePath" -ForegroundColor Green
        return $true
    } else {
        Write-Host "[ERRO] $Description nao encontrado: $FilePath" -ForegroundColor Red
        return $false
    }
}

function Get-SafeFileName {
    <#
    .SYNOPSIS
    Gera um nome de arquivo seguro removendo caracteres invalidos

    .PARAMETER FileName
    Nome do arquivo original

    .PARAMETER Replacement
    Caractere de substituicao para caracteres invalidos
    #>
    param (
        [string]$FileName,
        [string]$Replacement = "_"
    )

    # Caracteres invalidos para nomes de arquivo no Windows
    $invalidChars = [IO.Path]::GetInvalidFileNameChars()

    foreach ($char in $invalidChars) {
        $FileName = $FileName.Replace($char, $Replacement)
    }

    return $FileName
}