import re
import sys
import os
from datetime import datetime, timedelta
from Auxiliares.modules.logging_module import log_execution

# --------------------------------------------------------------
# 1) Ler o arquivo principal
# --------------------------------------------------------------
@log_execution
def le_arquivo(file_path):
    """ Lê o arquivo principal e retorna uma lista de strings (linhas do arquivo). """
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as infile:
        return infile.readlines()

# --------------------------------------------------------------
# 2) Ajustar linhas DATE
# --------------------------------------------------------------
@log_execution
def ajusta_DATE(conteudo):
    """
    Ajusta as linhas DATE no formato correto:
    DATE YYYY MM DD
    O dia (DD) pode ter parte decimal, sempre mantém 2 dígitos na parte inteira.
    """
    conteudo_ajustado = []

    for line in conteudo:
        # Captura a indentação inicial (tabs ou espaços)
        indentacao = re.match(r"^\s*", line).group(0)

        # Expressão regular para encontrar "DATE" seguido de AAAA MM DD (DD pode ter decimal)
        match = re.search(r"(DATE)\s+(\d{4})\s+(\d{1,2})\s+([\d\.]+)", line)
        if match:
            date_str, year, month, day = match.groups()

            # Separando a parte inteira e decimal do dia
            if "." in day:
                day_int, day_dec = day.split(".")
                formatted_day = f"{int(day_int):02d}.{day_dec}"  # Mantém os decimais
            else:
                formatted_day = f"{int(day):02d}"

            formatted_line = f"{indentacao}{date_str} {year} {int(month):02d} {formatted_day}"
            conteudo_ajustado.append(formatted_line)
        else:
            # Mantém a linha original (menos o \n) para não mexer em espaços/indentação extra
            conteudo_ajustado.append(line.rstrip())

    return conteudo_ajustado

# --------------------------------------------------------------
# 3) Função para ler linux2windows.txt e construir mapeamento
#    Se você não precisar desse arquivo, pode ajustar/omitir
# --------------------------------------------------------------
@log_execution
def carrega_mapa_linux2windows():
    """
    Lê o arquivo linux2windows.txt (espera 2 colunas: caminho_linux, caminho_windows).
    Retorna dict: {caminho_linux: caminho_windows, ...}
    Ajuste conforme a sua convenção real.
    """
    mapa = {}
    arquivo_mapa = "linux2windows.txt"

    # Obtém o diretório do script atual
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # Constrói o caminho completo para o arquivo
    file_path = os.path.join(script_dir, arquivo_mapa)

    # Verifica se o arquivo existe
    if not os.path.exists(file_path):
        print(f"Warning: O arquivo {file_path} não foi encontrado!")

    if not os.path.isfile(arquivo_mapa):
        return mapa  # Se não existir, retorna vazio

    with open(arquivo_mapa, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith("#"):
                continue
            # Espera algo como: /mnt/projetos  L:\projetos
            cols = line.split()
            if len(cols) >= 2:
                linux_path, windows_path = cols[0], cols[1]
                mapa[linux_path] = windows_path
    return mapa

@log_execution
def traduz_caminho_includes(caminho_incluido, mapa_linux2win):
    """
    Verifica se 'caminho_incluido' começa (ou contém) algum caminho em formato Linux
    que precise ser substituído pelo correspondente Windows.
    Caso seja relativo, assume que não precisa de substituição extra,
    a não ser que contenha um prefixo mapeado.
    """
    # Remove aspas se existirem
    caminho_limpo = caminho_incluido.strip().strip("'\"")

    # Verifica cada chave do dicionário e substitui
    for linux_prefix, windows_prefix in mapa_linux2win.items():
        if caminho_limpo.startswith(linux_prefix):
            # Substitui só o prefixo
            sufixo = caminho_limpo[len(linux_prefix):]
            caminho_limpo = windows_prefix + sufixo
            break

    return caminho_limpo

# --------------------------------------------------------------
# 4) Ler recursivamente arquivos INCLUDE (sem modificar nada neles)
#    para detectar "linhas relevantes" (conteúdo).
# --------------------------------------------------------------
@log_execution
def carregar_conteudo_incluido(base_file_path, mapa_linux2win, linhas, ja_carregado=None):
    """
    - Percorre as linhas do arquivo principal (já ajustadas ou não).
    - Quando encontra INCLUDE 'caminho', traduz para Windows (se precisar),
      depois lê esse arquivo e obtém suas linhas.
    - Para cada arquivo incluído, faz o mesmo processo recursivamente (se desejar).
    - Retorna uma lista de tuplas (linha, origem_main_bool) com:
         linha: a linha do arquivo principal OU do arquivo incluído
         origem_main_bool: True se a linha é do arquivo principal, False se é do incluído
    - Assim, conseguimos "enxergar" o conteúdo do incluído para saber se tem linhas
      (ignorar blank, WSRF, **), sem mexer fisicamente no incluído.
    """
    if ja_carregado is None:
        ja_carregado = set()  # para evitar recursão infinita

    base_dir = os.path.dirname(base_file_path)
    resultado = []

    for line in linhas:
        # Essa linha é do arquivo principal
        origem_main = True

        # Verifica se começa com INCLUDE
        match_inc = re.match(r"^\s*INCLUDE\s+'([^']+)'", line, re.IGNORECASE)
        if match_inc:
            caminho_incl = match_inc.group(1)
            # Traduz caminho se necessário
            caminho_incl = traduz_caminho_includes(caminho_incl, mapa_linux2win)

            # Se for relativo, ajusta para base_dir
            if not os.path.isabs(caminho_incl):
                caminho_incl = os.path.join(base_dir, caminho_incl)

            # Normaliza o path
            caminho_incl = os.path.normpath(caminho_incl)

            # Adiciona a linha original ao resultado (para manter no arquivo principal)
            resultado.append((line, origem_main))

            # Se ainda não carregamos esse arquivo, carrega agora
            if caminho_incl not in ja_carregado and os.path.isfile(caminho_incl):
                ja_carregado.add(caminho_incl)

                # Lê o arquivo incluído
                with open(caminho_incl, 'r', encoding='utf-8', errors='ignore') as incf:
                    conteudo_incl = incf.readlines()

                # Recorre (se quiser aninhar INCLUDEs)
                sub_resultado = carregar_conteudo_incluido(
                    caminho_incl,
                    mapa_linux2win,
                    conteudo_incl,
                    ja_carregado=ja_carregado
                )

                # As linhas do arquivo incluído serão marcadas como origem_main=False
                resultado.extend(sub_resultado)
        else:
            # Linha normal (não é INCLUDE)
            resultado.append((line, origem_main))

    return resultado

# --------------------------------------------------------------
# Funções auxiliares de Data/Hora
# --------------------------------------------------------------
@log_execution
def parse_date_decimal(year_str, month_str, day_str):
    """
    Converte strings 'year', 'month', 'day(.ddd)' em um objeto datetime + fração de dia.
    Exemplo: parse_date_decimal("2025", "02", "01.001") => datetime(2025, 2, 1, 0, 0, 86,400 microseconds)
    """
    year = int(year_str)
    month = int(month_str)

    # Verifica se existe parte decimal no dia
    if "." in day_str:
        # Separa inteiro e decimal
        parts = day_str.split(".")
        day_int = int(parts[0])          # parte inteira do dia
        day_frac = float("0." + parts[1])  # fracionária (ex: 0.001, 0.25, ...)
    else:
        day_int = int(day_str)
        day_frac = 0.0

    # Cria um datetime sem a parte fracionária
    # (hora, minuto, segundo = 0)
    dt_base = datetime(year, month, day_int, 0, 0, 0)

    # Converte a fração de dia em segundos
    # Ex: 0.001 dia = 0.001 * 86400 = 86.4 segundos
    seconds = day_frac * 86400.0
    return dt_base + timedelta(seconds=seconds)

@log_execution
def difference_in_days(dt1, dt2):
    """
    Retorna a diferença em dias (float) entre dt2 e dt1 => (dt2 - dt1).
    Exemplo: difference_in_days(dt1, dt2) = 1.234 dias
    """
    delta = dt2 - dt1
    return delta.total_seconds() / 86400.0

@log_execution
def add_fraction_of_day(dt, fraction_days):
    """
    Soma 'fraction_days' (float) a um datetime.
    Ex: add_fraction_of_day(dt, 0.001) => dt + 0.001 dia
    """
    return dt + timedelta(days=fraction_days)

@log_execution
def format_date_decimal(dt):
    """
    Converte datetime + fração de dia para o formato:
        DATE YYYY MM DD(.DDD)
    - Mantém 2 dígitos na parte inteira do dia;
    - Se houver fração, exibe .DDD (sem truncar zeros à direita além do que for relevante).
    - Exemplo: se dt = 2025-02-01 00:00:43.2 => 01.0005 ~ mas nós arredondamos a 3 decimais.
      Ajuste conforme a necessidade.
    """
    year = dt.year
    month = dt.month
    day = dt.day

    # Calcula a fração do dia baseado na hora, minuto, segundo, microsegundo
    # total segundos passados desde o início do dia
    segs_desde_meia_noite = dt.hour * 3600 + dt.minute * 60 + dt.second + dt.microsecond / 1_000_000

    frac_day = segs_desde_meia_noite / 86400.0  # fração do dia (0 <= frac_day < 1)
    if frac_day > 0:
        # Arredonda para 3 casas decimais (ou o que desejar)
        frac_str = f"{frac_day:.3f}".lstrip('0')  # ex: 0.001 => .001
        # Garantir que não fique .000
        if frac_str == ".000":
            # Significa 0
            return f"DATE {year} {month:02d} {day:02d}"
        else:
            return f"DATE {year} {month:02d} {day:02d}{frac_str}"
    else:
        return f"DATE {year} {month:02d} {day:02d}"

# --------------------------------------------------------------
# 5) Função para adicionar DATE + 0.001 se houver conteúdo entre duas datas
#    e a diferença entre essas datas for >= 0.001 dia
# --------------------------------------------------------------
@log_execution
def adiciona_DATE_curto(linhas_ajustadas):
    """
    - Recebe as linhas já formatadas pela ajusta_DATE (linhas do arquivo principal).
    - Lê (virtualmente) também o conteúdo dos arquivos incluídos para verificar se existe
      conteúdo relevante entre 2 DATE do arquivo principal.
    - Se houver conteúdo relevante E a diferença entre as duas datas for >= 0.001 dia,
      insere DATE (data_anterior + 0.001) antes do próximo DATE do arquivo principal.
    - Ignora linhas em branco, que começam com 'WSRF', ou que começam com '**' na hora de
      verificar "conteúdo".
    - As linhas de INCLUDE são consideradas conteúdo, mas não são alteradas.
    """

    # Carregamos aqui para manter a lógica do seu código
    from datetime import datetime

    # Carrega o mapeamento Linux->Windows se precisar
    mapa_linux2win = carrega_mapa_linux2windows()

    # Caminho do arquivo principal (definido em main)
    main_file_path = getattr(adiciona_DATE_curto, "MAIN_FILE_PATH", None)
    if not main_file_path:
        main_file_path = "main_file.dat"

    # "Expande" (virtualmente) os includes
    linhas_expandidas = carregar_conteudo_incluido(
        main_file_path,
        mapa_linux2win,
        linhas_ajustadas
    )

    # Percurso final para gerar as linhas do arquivo principal
    resultado_principal = []

    # Armazena última data encontrada e se houve conteúdo relevante no intervalo
    ultima_data_dt = None        # datetime do último DATE do arquivo principal
    ultima_data_str = None       # String exata do último DATE (para buscar year, month, etc.)
    buffer_principal = []        # Guarda linhas do principal que ainda não foram adicionadas
    conteudo_relevante = False   # Indica se houve conteúdo entre duas datas do principal

    @log_execution
    def eh_conteudo_relevante(linha):
        """Verifica se a linha conta como conteúdo relevante (ignorando branca, WSRF, **)."""
        txt = linha.strip()
        if not txt:
            return False
        if txt.upper().startswith("WSRF"):
            return False
        if txt.startswith("**"):
            return False
        return True

    # Regex para detectar DATE no arquivo principal
    re_date_main = re.compile(r"^\s*DATE\s+(\d{4})\s+(\d{1,2})\s+(\d{1,2}(\.\d+)?)\s*$", re.IGNORECASE)

    for (line, is_main) in linhas_expandidas:
        match_date = re_date_main.match(line)
        if match_date and is_main:
            # Temos um DATE no arquivo principal
            year_str, month_str, day_decimal_str, _ = match_date.groups()

            # Converte para datetime (robusto)
            current_date_dt = parse_date_decimal(year_str, month_str, day_decimal_str)

            if ultima_data_dt is not None:
                # Calcula a diferença em dias
                diff_days = difference_in_days(ultima_data_dt, current_date_dt)  # dt2 - dt1

                # Se houve conteúdo relevante E a diferença >= 0.001,
                # precisamos inserir a data "ultima_data_dt + 0.001" antes da data atual
                if conteudo_relevante and diff_days >= 0.001:
                    # Monta a nova data
                    new_dt = add_fraction_of_day(ultima_data_dt, 0.001)

                    # Converte new_dt em string final
                    nova_linha_date = format_date_decimal(new_dt)
                    resultado_principal.append(nova_linha_date)

            # Agora adiciona a data atual ao resultado
            resultado_principal.append(line)

            # Reinicia buffer e flags
            buffer_principal = []
            ultima_data_dt = current_date_dt
            ultima_data_str = line
            conteudo_relevante = False
        else:
            # Linha comum (ou DATE de arquivo incluído, ou não é DATE)
            if is_main:
                buffer_principal.append(line)

            # Verifica se essa linha é relevante (principal ou incluída)
            if eh_conteudo_relevante(line):
                conteudo_relevante = True

    # Ao final, adiciona o que sobrou do buffer
    resultado_principal.extend(buffer_principal)

    return resultado_principal

# --------------------------------------------------------------
# 6) Salvar o arquivo principal modificado
# --------------------------------------------------------------
@log_execution
def salva_arquivo(file_path, conteudo):
    """ Salva o conteúdo ajustado em um novo arquivo """
    base, ext = os.path.splitext(file_path)
    if ext.lower() not in {".dat", ".tpl"}:
        print("Erro: O arquivo deve ter extensão .dat ou .tpl")
        return

    output_file = f"{base}_formatted{ext}"  # Mantém a mesma extensão original
    with open(output_file, 'w', encoding='utf-8') as outfile:
        # Já que `conteudo` é uma lista de strings, vamos simplesmente dar join
        outfile.write("\n".join(conteudo) + "\n")
    print(f"Arquivo processado e salvo como: {output_file}")

# --------------------------------------------------------------
# 7) main
# --------------------------------------------------------------
@log_execution
def main(file_path):
    # Guardamos o caminho do arquivo principal para uso dentro de adiciona_DATE_curto,
    # pois lá precisamos saber o path para resolver INCLUDEs relativos.
    adiciona_DATE_curto.MAIN_FILE_PATH = file_path

    if not file_path.lower().endswith((".dat", ".tpl")):
        print("Erro: O arquivo deve ter extensão .dat ou .tpl")
        sys.exit(1)

    # Lê o arquivo
    conteudo = le_arquivo(file_path)

    # Ajusta os DATE (parte 1)
    conteudo_ajustado = ajusta_DATE(conteudo)

    # Adiciona DATE + 0.001 se necessário (parte 2)
    conteudo_final = adiciona_DATE_curto(conteudo_ajustado)

    # Salva o resultado
    salva_arquivo(file_path, conteudo_final)

# --------------------------------------------------------------
# Execução via linha de comando
# --------------------------------------------------------------
if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Uso: python script.py <caminho_para_arquivo.dat ou .tpl>")
        sys.exit(1)

    file_path = sys.argv[1]
    main(file_path)
