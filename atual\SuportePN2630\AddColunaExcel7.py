import pandas as pd
from openpyxl import load_workbook

def main():
    # Caminho do arquivo Excel
    caminho_arquivo = r'L:\res\campos\jubarte\er\er03\PN\2630\C2\P50\2507181416\UNIR_PN_PO\PN2630C2.202507181650.ajustado.gir.incPN_PO1.xlsx'
    caminho_saida = r'L:\res\campos\jubarte\er\er03\PN\2630\C2\P50\2507181416\UNIR_PN_PO\PN2630C2.202507181650.ajustado.gir.incPN_PO1_teste.xlsx'
    # Nome da sheet (altere conforme necessário)
    nome_sheet = 'Uniao'

    # 1) Carregar os dados com pandas para obter dimensões
    df = pd.read_excel(caminho_arquivo, sheet_name=nome_sheet)

    # Verificar quantidade de linhas e colunas
    linhas, colunas = df.shape
    print(f"Quantidade de linhas: {linhas}")
    print(f"Quantidade de colunas: {colunas}")

    # 2) Adicionar nova coluna vazia (apenas para manter a estrutura)
    df['Coluna_Nova'] = 'NovaColuna'

    # 3) Usar openpyxl para inserir a fórmula complexa na nova coluna
    wb = load_workbook(caminho_arquivo)
    ws = wb[nome_sheet]

    # Nova coluna (última coluna após adição)
    nova_coluna_index = colunas + 1  # Colunas no pandas começam em 0, Excel em 1

    valueCol = ["Nome_Projeto_exp", "Seq_Proj_PN2630_C2", "Nome_Zona", "REGIAO", "INJ/PRO", "INJ/PRO_Negativo"]
    # Montar a fórmula como string
    formula = [(
        'IF(F2="DP-11-0310","IPB",'
        'IF(F2="DPCOMSO-20-0002","PID1",'
        'IF(F2="DPCOM-20-0001","PID2",'
        'IF(F2="DPCOMSO-21-0002","DC1",'
        'IF(F2="DPCOMSO-21-0003","PID3",'
        'IF(F2="DPCOM-22-0177","ICSPB",'
        'IF(F2="DPCOMSO-22-0118","PID4",'
        'IF(F2="DPCOM-23-0144","PID5",'
        'IF(F2="NO.P-57","MP P-57",'
        'IF(F2="NO.P-58","MP P-58",'
        'IF(F2="NO.CDAN","MP CDAN",'
        'IF(F2="EVPRO-23-0179","EVPRO P-57",'
        'IF(F2="EVPRO-23-0180","EVPRO P-58",'
        'IF(F2="CCUS-24-0230","CCUS",'
        'IF(F2="POCSUB-24-0645","PID1",-1'
        ')))))))))))))))'
    ),(
        'IF(F2="NO.CDAN",1,'
        'IF(F2="NO.P-57",2,'
        'IF(F2="NO.P-58",3,'
        'IF(F2="POCSUB-24-0645",4,'
        'IF(F2="DP-11-0310",5,'
        'IF(F2="DPCOM-20-0001",6,'
        'IF(F2="DPCOMSO-20-0002",7,'
        'IF(F2="DPCOMSO-21-0002",8,'
        'IF(F2="DPCOMSO-22-0118",9,'
        'IF(F2="EVPRO-23-0179",10,'
        'IF(F2="EVPRO-23-0180",11,'
        'IF(F2="DPCOMSO-21-0003",12,'
        'IF(F2="DPCOM-22-0177",13,'
        'IF(F2="DPCOM-23-0144",14,'
        'IF(F2="CCUS-24-0230",15,'
        '-1)))))))))))))))'
    ),(
        '='
        'IF(and(C2="ANC8",D2=1),"MCB/COQ-ESS103A",'
        'IF(and(C2="ANC9",D2=1),"MCB/COQ-ESS103A",'
        'IF(and(C2="ARGO",D2=4),"MCB/COQ-ESS103A",'
        'IF(and(C2="BLA",D2=1), "CO140-ESS122",'
        'IF(and(C2="CXR",D2=1), "MCB/COQ-ESS172",'
        'IF(and(C2="JUB",D2=1), "RO300",'
        'IF(and(C2="JUB",D2=5), "MCB/COQ-ESS103A",'
        'IF(and(C2="JUB",D2=9), "CO140-ESS116",'
        'IF(and(C2="JUB",D2=11),"MRL700-136",'
        'IF(and(C2="JUB",D2=15),"BR100",'
        'IF(and(C2="JUB",D2=29),"MRL700-153",'
        'IF(and(C2="JUB",D2=31),"MCB/COQ-PRB2",'
        'IF(and(C2="MGG",D2=8), "MCB/COQ-PRB2",'
        'IF(and(C2="PRB",D2=6), "MCB/COQ-PRB2",-1'
        '))))))))))))))'

    ),(
        '='
        'IF(S2 ="RO300-ESS100","POS_SAL",'
        'IF(S2 ="MCB/COQ-ESS172","PRE_SAL",'
        'IF(S2 ="MCB/COQ-ESS103A","PRE_SAL",'
        'IF(S2 ="MCB/COQ-PRB2","PRE_SAL",'
        'IF(S2 ="MRL700-ESS153","POS_SAL",'
        'IF(S2 ="MRL700-ESS136","POS_SAL",'
        'IF(S2 ="CO140-ESS122","POS_SAL",'
        'IF(S2 ="CO140-ESS116","POS_SAL",'
        'IF(S2 ="BR100-CHT4","POS_SAL",-1'
        ')))))))))'
    ),(
        '='
        'IF(and(I2=0,J2=0,K2=0,L2<>0),"Injetor",'
        'IF(and(I2<>0,L2=0),"Produtor",-1'
        '))'
    ),(
        '='
        'IF(and(I2=0,J2=0,K2=0,L2<0),"Injetor Negativo",'
        'IF(and(I2<0,L2=0),"Produtor Negativo",-1'
        '))'        
    )]

    # Inserir a fórmula em cada linha da nova coluna
    for count1, n_coll in enumerate(valueCol):
        ws.cell(row=1, column=nova_coluna_index + count1 - 1, value=n_coll)
        for linha in range(2, linhas + 2):  # Começa da linha 2 (ignorando cabeçalho)
            form1 = formula[count1].replace("F2", f"F{linha}")
            form2 = form1.replace("D2", f"D{linha}")
            form3 = form2.replace("C2", f"C{linha}")
            form4 = form3.replace("S2", f"S{linha}")
            form5 = form4.replace("I2", f"I{linha}")
            form6 = form5.replace("J2", f"J{linha}")
            form7 = form6.replace("K2", f"K{linha}")
            form8 = form7.replace("L2", f"L{linha}")
            formula_atualizada = form8
            ws.cell(row=linha, column=nova_coluna_index + count1 - 1, value=f'={formula_atualizada}')
   


    # Salvar o arquivo Excel com as fórmulas
    wb.save(caminho_saida)

    print("Nova coluna adicionada com sucesso com a fórmula complexa.")

if __name__ =="__main__":
    main()