# Usage
# L:\res\campos\jubarte\er\er02\00_Programas\python\windows\python-3.12.6.amd64\python.exe .\250206\cmg.dat_helperv02.py L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\02_PD_Cenario4v250210\Sim\RMSv14_er03_geoeng_output02.tpl

import re
import sys
import os
from datetime import datetime, timedelta
from Auxiliares.modules.logging_module import log_execution

@log_execution
def le_arquivo(file_path):
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as infile:
        return infile.readlines()

@log_execution
def ajusta_DATE(conteudo):
    """
    Ajusta as linhas DATE para o formato DATE YYYY MM DD (DD com 2 dígitos).
    Se houver parte decimal, mantém como .XXX
    """
    conteudo_ajustado = []
    for line in conteudo:
        indent = re.match(r"^\s*", line).group(0)
        m = re.search(r"(DATE)\s+(\d{4})\s+(\d{1,2})\s+([\d\.]+)", line)
        if m:
            date_str, year, month, day = m.groups()
            if "." in day:
                day_int, day_dec = day.split(".")
                formatted_day = f"{int(day_int):02d}.{day_dec}"
            else:
                formatted_day = f"{int(day):02d}"
            conteudo_ajustado.append(f"{indent}{date_str} {year} {int(month):02d} {formatted_day}")
        else:
            conteudo_ajustado.append(line.rstrip())
    return conteudo_ajustado

@log_execution
def parse_date_decimal(year_str, month_str, day_str):
    """
    Converte strings 'YYYY','MM','DD(.DDD)' em datetime + fração de dia.
    """
    year = int(year_str)
    month = int(month_str)
    if "." in day_str:
        parts = day_str.split(".")
        day_int = int(parts[0])
        day_frac = float("0." + parts[1])
    else:
        day_int = int(day_str)
        day_frac = 0.0
    dt_base = datetime(year, month, day_int)
    seconds = day_frac * 86400.0
    return dt_base + timedelta(seconds=seconds)

@log_execution
def difference_in_days(dt1, dt2):
    delta = dt2 - dt1
    return delta.total_seconds() / 86400.0

@log_execution
def add_fraction_of_day(dt, fraction_days):
    return dt + timedelta(days=fraction_days)

@log_execution
def format_date_decimal(dt):
    year = dt.year
    month = dt.month
    day = dt.day
    secs_since_midnight = dt.hour * 3600 + dt.minute * 60 + dt.second + dt.microsecond / 1_000_000
    frac_day = secs_since_midnight / 86400.0
    if frac_day > 0:
        frac_str = f"{frac_day:.3f}".lstrip('0')  # .001
        if frac_str == ".000":
            return f"DATE {year} {month:02d} {day:02d}"
        else:
            return f"DATE {year} {month:02d} {day:02d}{frac_str}"
    else:
        return f"DATE {year} {month:02d} {day:02d}"

@log_execution
def extrair_limites_include(caminho_incl):
    """
    Lê o arquivo INCLUDE e retorna (first_dt, last_dt),
    isto é, a primeira e última DATE encontradas, ou (None, None) se não houver.
    """
    re_date = re.compile(r"^\s*DATE\s+(\d{4})\s+(\d{1,2})\s+([\d\.]+)", re.IGNORECASE)
    first_dt, last_dt = None, None
    try:
        with open(caminho_incl, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        for line in lines:
            m = re_date.match(line)
            if m:
                y, m_, d = m.groups()
                dt = parse_date_decimal(y, m_, d)
                if first_dt is None:
                    first_dt = dt
                last_dt = dt
    except Exception:
        pass
    return first_dt, last_dt

@log_execution
def adiciona_DATE_curto(linhas_ajustadas):
    """
    Processa as linhas do arquivo principal e insere 'DATE + 0.001'
    se houver conteúdo relevante (ou includes, etc.) entre duas datas do arquivo principal.

    - Qualquer linha que não comece com '**', 'WSRF', e não seja vazia, é considerada relevante.
    - As linhas INCLUDE são mantidas na saída e também contam como relevantes.
    - Se difference_in_days(prev_date, current_date) >= 0.0 e houver gap_relevante,
      insere 'prev_date + 0.001' desde que não seja praticamente igual à data atual.
    """
    resultado = []
    prev_date = None
    gap_relevante = False

    @log_execution
    def eh_conteudo_relevante(linha):
        txt = linha.strip()
        if not txt:
            return False
        if txt.upper().startswith("WSRF"):
            return False
        if txt.startswith("**"):
            return False
        # Se não for nenhuma das condições acima, consideramos relevante
        return True

    re_date_main = re.compile(r"^\s*DATE\s+(\d{4})\s+(\d{1,2})\s+([\d\.]+)\s*$", re.IGNORECASE)

    # Quando encontrarmos um INCLUDE, podemos extrair se há datas no arquivo,
    # mas nesse exemplo, vamos considerar a linha INCLUDE também como 'conteúdo relevante'
    # para forçar a inserção de DATE + 0.001.
    @log_execution
    def processa_include(line):
        nonlocal gap_relevante
        # A simples presença de INCLUDE já deve sinalizar conteúdo
        m = re.match(r"^\s*INCLUDE\s+'([^']+)'", line, re.IGNORECASE)
        if m:
            # Se quiser também forçar gap_relevante caso existam datas no include:
            # first_dt, last_dt = extrair_limites_include(caminho_incl)
            # if first_dt is not None and last_dt is not None:
            gap_relevante = True

    for line in linhas_ajustadas:
        # Verificamos se é uma linha DATE no arquivo principal
        # ou se é outro tipo de linha (INCLUDE, etc.)
        if line.strip().upper().startswith("INCLUDE"):
            # Mantemos a linha no arquivo
            resultado.append(line)
            # Marca gap
            processa_include(line)
            continue

        m_date = re_date_main.match(line)
        if m_date:
            year_str, month_str, day_str = m_date.groups()
            current_dt = parse_date_decimal(year_str, month_str, day_str)
            if prev_date is not None:
                diff_days = difference_in_days(prev_date, current_dt)
                # Aqui, se diff_days >= 0.0 (ou > -1e-6 para lidar c/ flutuações) e gap_relevante,
                # insere 'prev_date + 0.001'.
                # Assim, mesmo se diff = 0, insere .001 se gap_relevante == True
                if gap_relevante and diff_days > 0.001:
                    expected_dt = add_fraction_of_day(prev_date, 0.001)
                    # Se expected_dt for praticamente igual a current_dt, não insere
                    if abs(difference_in_days(expected_dt, current_dt)) > 1e-6:
                        resultado.append(format_date_decimal(expected_dt))
            # Adiciona a data atual
            resultado.append(line)
            prev_date = current_dt
            gap_relevante = False  # zera a flag depois de achar uma data do principal
        else:
            # Linha comum
            resultado.append(line)
            # Se for relevante, marca gap
            if eh_conteudo_relevante(line):
                gap_relevante = True

    return resultado

@log_execution
def salva_arquivo(file_path, conteudo):
    base, ext = os.path.splitext(file_path)
    if ext.lower() not in {".dat", ".tpl"}:
        print("Erro: extensão inválida.")
        return
    output_file = f"{base}_formatted{ext}"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("\n".join(conteudo) + "\n")
    print(f"Arquivo processado e salvo como: {output_file}")

@log_execution
def main(file_path):
    conteudo = le_arquivo(file_path)
    conteudo_ajustado = ajusta_DATE(conteudo)
    conteudo_final = adiciona_DATE_curto(conteudo_ajustado)
    salva_arquivo(file_path, conteudo_final)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Uso: python script.py <arquivo.dat/.tpl>")
        sys.exit(1)
    file_path = sys.argv[1]
    main(file_path)
