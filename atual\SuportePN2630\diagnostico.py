#!/usr/bin/env python3
"""
Script de Diagnóstico para o Sistema de Cálculo de Volumes
Identifica e resolve problemas comuns automaticamente
"""

import sys
import os
import importlib
import warnings
from pathlib import Path
import subprocess
import platform

def check_dependencies():
    """Verifica dependências e instala as faltantes"""
    print("=== VERIFICAÇÃO DE DEPENDÊNCIAS ===")
    
    required_packages = {
        'pandas': 'pandas>=2.0.0',
        'numpy': 'numpy>=1.24.0', 
        'polars': 'polars>=0.20.0',
        'openpyxl': 'openpyxl>=3.1.0',
        'pyxlsb': 'pyxlsb>=1.0.10',
        'psutil': 'psutil>=5.9.0',
        'xlrd': 'xlrd>=2.0.1'
    }
    
    optional_packages = {
        'numba': 'numba>=0.58.0',
        'calamine': 'calamine>=0.4.0'
    }
    
    missing_packages = []
    
    # Verifica pacotes obrigatórios
    for package, version in required_packages.items():
        try:
            importlib.import_module(package)
            print(f"✓ {package} - OK")
        except ImportError:
            print(f"✗ {package} - FALTANDO")
            missing_packages.append(version)
    
    # Verifica pacotes opcionais
    for package, version in optional_packages.items():
        try:
            importlib.import_module(package)
            print(f"✓ {package} - OK (opcional)")
        except ImportError:
            print(f"? {package} - Recomendado para melhor performance")
            # Não adiciona aos obrigatórios
    
    # Instala pacotes faltantes
    if missing_packages:
        print(f"\n{len(missing_packages)} pacotes precisam ser instalados:")
        for pkg in missing_packages:
            print(f"  - {pkg}")
        
        response = input("\nDeseja instalar automaticamente? (s/n): ").lower()
        if response == 's':
            install_packages(missing_packages)
        else:
            print("Comando para instalação manual:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
    
    print("\n✓ Todas as dependências obrigatórias estão instaladas!")
    return True

def install_packages(packages):
    """Instala pacotes automaticamente"""
    print("\nInstalando pacotes...")
    
    for package in packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✓ {package} instalado com sucesso")
        except subprocess.CalledProcessError as e:
            print(f"✗ Erro ao instalar {package}: {e}")

def check_cuda_setup():
    """Verifica configuração CUDA"""
    print("\n=== VERIFICAÇÃO CUDA/GPU ===")
    
    # Verifica variáveis de ambiente
    cuda_path = os.environ.get('CUDA_PATH')
    if cuda_path:
        print(f"✓ CUDA_PATH encontrado: {cuda_path}")
    else:
        print("? CUDA_PATH não definido (normal se não usar GPU)")
    
    # Testa CuPy
    try:
        import cupy as cp
        print("✓ CuPy instalado")
        try:
            # Testa operação simples
            arr = cp.array([1, 2, 3])
            print("✓ GPU funcional")
            return True
        except Exception as e:
            print(f"✗ GPU não funcional: {e}")
            return False
    except ImportError:
        pass

def main():
      check_dependencies()
      check_cuda_setup()

if __name__ == "__main__":
        main()