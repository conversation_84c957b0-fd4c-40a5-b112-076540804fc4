import sys
import re
import os
from collections import defaultdict
from datetime import datetime, timedelta
from Auxiliares.modules.logging_module import log_execution

# ======================
# METADADOS DO SISTEMA
# ======================
__version__ = "1.0"
__release_date__ = "2025-06-20"
__author__ = "Iury <PERSON>ottele Medeiros"
__contact__ = "<EMAIL>"

# ======================
# STATUS DE DESENVOLVIMENTO
# ======================
__status__ = {
    'implementado': [
r"""Procura por todos TRIGGER com TDATE na secao RUN que estao dentro de arquivos INCLUDE OPT e os coloca na devida data no cronograma.
    O arquivo INCLUDE OPT e identificado pelo seguinte padrao (em vim):
        ^\s*include_opt\s\+'\(.*\.inc\)'
        onde \1 e o caminho para o arquivo INCLUDE.
    O padrao que define o inicio do bloco TRIGGER TDATE e o seguinte:
        ^\s*trigger.*tdate\s\+>\s\+\(\d\+\)\s\+\(\d\+\)\s\+(\d\+\(\.\d\+\)?\)
        onde \1 = ano, \2 = mes e \3 = dia, sendo que dia pode conter casas decimais ou nao.
    Todo bloco de TRIGGER é finalizado com END_TRIGGER, nao é case sensitive.
    Devemos ficar atento pois cada TRIGGER pode ter outros TRIGGER aninhados.
    Para cada bloco de TRIGGER TDATE identificado deve copiar se conteudo e coloca-lo na data correspondente no arquivo principal .dat/.tpl.
    O arquivo principal possui linhas com data no seguinte padrao:
        ^\s*date\s\+\(\d\+\)\s\+\(\d\+\)\s\+(\d\+\(\.\d\+\)?\)
        onde \1 = ano, \2 = mes e \3 = dia, sendo que dia pode conter casas decimais ou nao.
    Apos cada DATE vem o bloco correspondente a esse DATE o qual termina no proximo DATE.
    Pode haver DATE sem conteudo ou conteudo em branco.
    A datas devem aparecer em ordem crescente.
    Assim devemos colocar o conteudo do TRIGGER TDATE na data correspondente.
    O arquivo final nao deve conter DATE com datas repetidas.
    Ao colocar o bloco do TRIGGER TDATE na data correspondente devemos criar uma nova linha de DATE colocando com a data do TRIGGER TDATE + 0.001 dias finalizando o conteudo do TRIGGER TDATE (caso nao ja nao exista esse DATE).
    Caso a data do TRIGGER TDATE nao exista deve-se cria-la para que o bloco do TRIGGER TDATE seja inserido apos ela.
""",
r"""Ao copiar o conteudo do TRIGGER TDATE para o arquivo principal nao devemos deixar a linha que inicia o bloco (trigger.*tdate) e nem a aque finaliza o bloco (end_trigger).
""",
r"""Ao criar novos DATE deixar as datas como AAAA MM DD ou AAAA MM DD.ddd caso o dia tenha casas decimais. Ou seja, ano com 4 algarismos, mes com 2 algarismos e dia com 2 algarismos e casas decimais caso necessario.
""",
r"""Deve-se adicionar ** no inicio da linha na qual o include_opt ja foi lido.
""",
r"""Deve-se o arquivo de saida ter o mesmo nome de entrada porem com a extensao .opt.dat ou .opt.tpl a depender do arquivo de entrada se for .dat ou .tpl.
""",
r"""Devemos ficar atentos ao seguinte padrao:
        ^\s*trigger\s\+'\(.*\)'\s\+off
        onde \1 = identificador do trigger
    Caso encontrarmos algum trigger recebendo off, devemos adicionar ao inicio de todas as linhas do conteudo desse trigger (com mesmo identificador) no arquivo principal **.
    Sendo assim tambem e necessario melhorar a definicao do padrao de procura pelos TRIGGER TDATE. Segue o novo padrao:
        ^\s*trigger\s\+'\(.*\)'\s\+.*tdate\s\+>\s\+\(\d\+\)\s\+\(\d\+\)\s\+(\d\+\(\.\d\+\)?\)
        onde \1 = identificador do trigger, \2 = ano, \3 = mes e \4 = dia, sendo que dia pode conter casas decimais ou nao.
""",
r"""Uma correcao: o trigger off nao possui o end_trigger apos ele. O trigger off possui apenas 1 linha com o padrao definido, nao ha conteudo.
""",
r"""O conteudo dos TRIGGER TDATE que recebem off ainda nao estao recebendo '** ' no inicio de cada linha. Deve-se levar em conta que a linha com trigger off pode estar em um include_opt diferente da linha que possuiro o trigger tdate com mesmo identificador.
""",
r"""Devemos fazer a procura por trigger apenas nos arquivos include_opt.
""",
r"""Implementar a opcao de entrar com o nome do arquivo de saida, case a entrada nao seja usada continuar como esta implementado agora colocando o .opt antes do .dat/.tpl.
""",
r"""Criar uma opcao --silent ou um nome mais adequado para que nao seja imprimido nenhuma informacao que nao seja de erro.
""",
r"""Usar apenas os triggers que aparecem apos a data do date_opt. O date_opt deve ser procurado no arquivo principal usando o padrao:
    ^\s*date_opt\s\+\(\d\+\)\s\+\(\d\+\)\s\+(\d\+\(\.\d\+\)?\)
    onde a data do date_opt vem de \1 = ano, \2 = mes e \3 = dia, sendo que dia pode conter casas decimais ou nao.
""",
r"""A linha que contem o padrao date_opt deve ser reescrita adicinando '** ' em seu inicio.
""",
r"""Deve-se evitar o uso de caracteres especiais. A saida pode ser impressa em terminais que nao comportam caracteres especiais, ocasionando erros quando esses aparecem.
""",
r"""date_opt deve ser procurado apenas para coletar a data que a partir dela iremos coletar os conteudos do trigger tdate e apos essa coleta essa linha deve ser comentada (adicionado '** ') no conteudo principal.
""",
r"""Alteracao no nome padrao do arquivo, caso nao seja entrada uma saida devemos adicionar apenas .dat ao final do caminho do arquivo de entrada.
""",
r"""Foi achado um trigger tdate em 2025 12 31.999. O date criado para essa data somando-se 0.001 ficou em 2025 12 32. Essa data nao existe, o resultado esperado era 2026 01 01.
""",
r"""Voltamos a ter problema o problema abaixo:
    O conteudo dos TRIGGER TDATE que recebem off ainda nao estao recebendo '** ' no inicio de cada linha. Deve-se levar em conta que a linha com trigger off pode estar em um include_opt diferente da linha que possuiro o trigger tdate com mesmo identificador.
""",
r"""Todos os trigger tdate que possem data menores que a data do date_opt devem entrar no bloco do 1o date que possui data maior ou igual a data do date_opt.
""",
    ],
    'em_desenvolvimento': [
    ],
    'planejado': [
r"""Ao coletar a data do trigger tdate ao inves de criar o date com essa data e outro date dessa data + 0.001, vamos criar o date com a data - 0.001 e outro com o date na data.
""",
    ]
}

# ======================
# INSTRUCOES DE USO
# ======================
__usage__ = r"""
COMO USAR:
    python trigger_tdate_translator.py [OPCOES] <arquivo.dat|arquivo.tpl>

OPCOES:
    --version   Mostra versao e sai
    --docs      Exibe documentacao completa
    --status    Mostra status de implementacao
    -o, --output  Especifica o arquivo de saida

EXEMPLOS:
  # Processamento normal com saída automática:
  python trigger_tdate_translator.py arquivo.dat

  # Especificando arquivo de saída:
  python trigger_tdate_translator.py -o saida.dat arquivo.dat
  python trigger_tdate_translator.py --output custom.tpl arquivo.tpl

  # Verificar documentação:
  python trigger_tdate_translator.py --docs

  # Verificar status:
  python trigger_tdate_translator.py --status
"""

# ======================
# FUNCOES DE EXIBICAO
# ======================
@log_execution
def show_version():
    print(f"\nTRIGGER TDATE TRANSLATOR v{__version__}")
    print(f"Data de lançamento: {__release_date__}")
    print(f"Autor: {__author__} | Contato: {__contact__}")

@log_execution
def show_usage():
    print(__usage__)

@log_execution
def show_status():
    print("\nSTATUS DE DESENVOLVIMENTO:")
    print("\n✅ IMPLEMENTADO:")
    for item in __status__["implementado"]:
        print(f"  • {item}")

    print("\n🚧 EM DESENVOLVIMENTO:")
    for item in __status__["em_desenvolvimento"]:
        print(f"  • {item}")

    print("\n📅 PLANEJADO:")
    for item in __status__["planejado"]:
        print(f"  • {item}")

@log_execution
def show_full_docs():
    show_version()
    print("\n" + "="*50)
    show_usage()
    print("\n" + "="*50)
    show_status()

@log_execution
def documentacao():
    # Controle de argumentos
    if "--version" in sys.argv:
        show_version()
        sys.exit(0)

    if "--docs" in sys.argv:
        show_full_docs()
        sys.exit(0)

    if "--status" in sys.argv:
        show_status()
        sys.exit(0)

    if "--help" in sys.argv or "-h" in sys.argv:
        show_usage()
        sys.exit(0)

# ======================
# FUNCOES DE PROCESSAMENTO
# ======================
@log_execution
def ajustar_data(ano, mes, dia, incremento=0.001):
    """
    Ajusta uma data adicionando um incremento em dias, tratando corretamente
    casos de virada de mês/ano.
    Retorna uma nova tupla (ano, mes, dia) com o valor ajustado.
    """
    try:
        # Converter para objeto datetime
        data_base = datetime(ano, mes, 1)

        # Calcular o dia inteiro e a fração
        dia_inteiro = int(dia)
        fracao = dia - dia_inteiro

        # Ajustar para o dia correto no mês
        data = data_base + timedelta(days=dia_inteiro-1 + fracao + incremento)

        # Extrair componentes da nova data
        novo_ano = data.year
        novo_mes = data.month
        novo_dia = data.day + (data.hour/24 + data.minute/1440 + data.second/86400)

        return (novo_ano, novo_mes, round(novo_dia, 3))
    except Exception as e:
        print(f"[ERRO] Falha ao ajustar data {ano}-{mes}-{dia}: {str(e)}")
        return (ano, mes, dia + incremento)  # Fallback

@log_execution
def extrair_data(linha, tipo='DATE'):
    if tipo == 'DATE':
        padrao = r'^\s*date\s+(\d+)\s+(\d+)\s+(\d+(?:\.\d+)?)'
    else:
        padrao = r'^\s*trigger.*tdate\s+>\s+(\d+)\s+(\d+)\s+(\d+(?:\.\d+)?)'

    match = re.match(padrao, linha, re.IGNORECASE)
    if match:
        ano = int(match.group(1))
        mes = int(match.group(2))
        dia = float(match.group(3))
        return (ano, mes, dia)
    return None

@log_execution
def coletar_includes(linhas):
    includes = []
    padrao = r"^\s*include_opt\s+\'(.*\.inc)\'"
    for linha in linhas:
        match = re.match(padrao, linha, re.IGNORECASE)
        if match:
            includes.append(match.group(1))
    return includes

@log_execution
def coletar_triggers_desligados(conteudo):
    desligados = set()
    padrao = re.compile(r"^\s*trigger\s+'(.*?)'\s+off\s*$", re.IGNORECASE)
    for linha in conteudo:
        match = padrao.match(linha)
        if match:
            desligados.add(match.group(1))
    return desligados

@log_execution
def extrair_blocos_tdate(conteudo, triggers_desligados_global, date_opt_data=None):
    blocos_por_data = defaultdict(list)
    tdate_pattern = re.compile(r'^\s*trigger\s+\'(.*?)\'.*?\btdate\s+>\s+(\d+)\s+(\d+)\s+(\d+(?:\.\d+)?)', re.IGNORECASE)
    trigger_pattern = re.compile(r'^\s*trigger\b', re.IGNORECASE)
    end_trigger_pattern = re.compile(r'^\s*end_trigger\b', re.IGNORECASE)

    profundidade = 0
    bloco_atual = []
    data_atual = None
    nome_trigger = None

    for linha in conteudo:
        if profundidade > 0:
            bloco_atual.append(linha)
            if trigger_pattern.match(linha):
                profundidade += 1
            elif end_trigger_pattern.match(linha):
                profundidade -= 1
                if profundidade == 0:
                    # Aplicar filtro DATE_OPT aqui
                    if data_atual and (date_opt_data is None or data_atual >= date_opt_data):
                        desligado = nome_trigger in triggers_desligados_global
                        blocos_por_data[data_atual].append((nome_trigger, desligado, bloco_atual[:]))
                    bloco_atual = []
                    data_atual = None
                    nome_trigger = None
        else:
            match_tdate = tdate_pattern.match(linha)
            if match_tdate:
                nome_trigger = match_tdate.group(1)
                ano = int(match_tdate.group(2))
                mes = int(match_tdate.group(3))
                dia = float(match_tdate.group(4))  # Corrigido para match_tdate.group(4)
                data_atual = (ano, mes, dia)
                profundidade = 1
                bloco_atual = [linha]

    return dict(blocos_por_data)

@log_execution
def formatar_data(data):
    ano, mes, dia = data
    ano_str = str(ano).zfill(4)
    mes_str = str(mes).zfill(2)

    if isinstance(dia, float):
        parte_inteira = int(dia)
        parte_fracionaria = dia - parte_inteira
        if parte_fracionaria > 0:
            frac_str = f"{parte_fracionaria:.3f}".split('.')[1].rstrip('0')
            dia_str = f"{parte_inteira:02d}.{frac_str}"
        else:
            dia_str = f"{parte_inteira:02d}"
    else:
        dia_str = f"{dia:02d}"

    return f"DATE {ano_str} {mes_str} {dia_str}"

@log_execution
def processar_principal(conteudo_principal, all_trigger_blocks, date_opt_data=None):
    cabecalho = []
    blocos = []
    current_block = None
    date_pattern = re.compile(r'^\s*date\s+', re.IGNORECASE)
    date_opt_pattern = re.compile(r'^\s*date_opt\s+', re.IGNORECASE)

    # Coletar triggers antigos (antes do DATE_OPT)
    triggers_antigos = []
    all_trigger_blocks_novo = {}

    if date_opt_data is not None:
        for data_trigger, blocos_trigger in all_trigger_blocks.items():
            if data_trigger < date_opt_data:
                triggers_antigos.extend(blocos_trigger)
            else:
                all_trigger_blocks_novo[data_trigger] = blocos_trigger
    else:
        all_trigger_blocks_novo = all_trigger_blocks

    # Processar linhas do arquivo principal
    for linha in conteudo_principal:
        if date_pattern.match(linha):
            data = extrair_data(linha, 'DATE')
            if data is None:
                if current_block is None:
                    cabecalho.append(linha)
                else:
                    current_block['conteudo'].append(linha)
                continue

            if current_block is not None:
                blocos.append(current_block)
            current_block = {'data': data, 'linha_date': linha, 'conteudo': []}
        else:
            if date_opt_pattern.match(linha):
                linha = "** " + linha

            if current_block is None:
                cabecalho.append(linha)
            else:
                current_block['conteudo'].append(linha)

    if current_block is not None:
        blocos.append(current_block)

    if not blocos and not all_trigger_blocks_novo and not triggers_antigos:
        return conteudo_principal

    blocos_por_data = {bloco['data']: bloco for bloco in blocos if bloco['data'] is not None}
    novas_datas = []

    # Processar triggers normais (após DATE_OPT)
    for data_trigger, blocos_trigger in all_trigger_blocks_novo.items():
        data_trigger_001 = ajustar_data(*data_trigger, 0.001)

        if data_trigger not in blocos_por_data:
            novo_bloco = {
                'data': data_trigger,
                'linha_date': formatar_data(data_trigger),
                'conteudo': []
            }
            for (nome, desligado, bloco) in blocos_trigger:
                conteudo = bloco[1:-1] if len(bloco) > 2 else []
                if desligado:
                    conteudo = ["** " + linha for linha in conteudo]
                novo_bloco['conteudo'].extend(conteudo)
            blocos_por_data[data_trigger] = novo_bloco
            novas_datas.append(data_trigger)
        else:
            bloco_existente = blocos_por_data[data_trigger]
            conteudo_original = bloco_existente['conteudo'][:]
            bloco_existente['conteudo'] = []

            for (nome, desligado, bloco) in blocos_trigger:
                conteudo = bloco[1:-1] if len(bloco) > 2 else []
                if desligado:
                    conteudo = ["** " + linha for linha in conteudo]
                bloco_existente['conteudo'].extend(conteudo)

            if conteudo_original:
                if data_trigger_001 not in blocos_por_data:
                    blocos_por_data[data_trigger_001] = {
                        'data': data_trigger_001,
                        'linha_date': formatar_data(data_trigger_001),
                        'conteudo': conteudo_original
                    }
                    novas_datas.append(data_trigger_001)
                else:
                    bloco_001 = blocos_por_data[data_trigger_001]
                    bloco_001['conteudo'] = conteudo_original + bloco_001['conteudo']

        if data_trigger_001 not in blocos_por_data:
            blocos_por_data[data_trigger_001] = {
                'data': data_trigger_001,
                'linha_date': formatar_data(data_trigger_001),
                'conteudo': []
            }
            novas_datas.append(data_trigger_001)

    # Processar triggers antigos (antes do DATE_OPT)
    if triggers_antigos and date_opt_data is not None:
        # Encontrar o primeiro bloco >= DATE_OPT
        primeiro_bloco_pos_opt = None
        for data in sorted(blocos_por_data.keys()):
            if data >= date_opt_data:
                primeiro_bloco_pos_opt = blocos_por_data[data]
                break

        # Se não encontrou, criar um novo bloco na data do DATE_OPT
        if primeiro_bloco_pos_opt is None:
            primeiro_bloco_pos_opt = {
                'data': date_opt_data,
                'linha_date': formatar_data(date_opt_data),
                'conteudo': []
            }
            blocos_por_data[date_opt_data] = primeiro_bloco_pos_opt
            novas_datas.append(date_opt_data)

        # Processar conteúdo dos triggers antigos
        conteudo_antigo = []
        for (nome, desligado, bloco) in triggers_antigos:
            conteudo = bloco[1:-1] if len(bloco) > 2 else []
            if desligado:
                conteudo = ["** " + linha for linha in conteudo]
            conteudo_antigo.extend(conteudo)

        # Inserir no início do bloco alvo
        primeiro_bloco_pos_opt['conteudo'] = conteudo_antigo + primeiro_bloco_pos_opt['conteudo']

    todos_blocos = list(blocos_por_data.values())
    todos_blocos.sort(key=lambda x: (x['data'][0], x['data'][1], x['data'][2]))

    novas_linhas = cabecalho[:]
    for bloco in todos_blocos:
        novas_linhas.append(bloco['linha_date'])
        novas_linhas.extend(bloco['conteudo'])

    return novas_linhas

@log_execution
def extrair_date_opt(conteudo):
    """
    Extrai a data do DATE_OPT do conteúdo fornecido.
    Retorna uma tupla (ano, mes, dia) ou None se não encontrado.
    Considera a última ocorrência de DATE_OPT no arquivo.
    """
    padrao = re.compile(r'^\s*date_opt\s+(\d+)\s+(\d+)\s+(\d+(?:\.\d+)?)', re.IGNORECASE)
    datas = []
    for linha in conteudo:
        match = padrao.match(linha)
        if match:
            ano = int(match.group(1))
            mes = int(match.group(2))
            dia = float(match.group(3))
            datas.append((ano, mes, dia))

    return datas[-1] if datas else None

@log_execution
def parse_arguments():
    output_file = None
    input_file = None
    silent_mode = False  # Initialize silent mode flag
    args = sys.argv[1:]
    i = 0
    while i < len(args):
        arg = args[i]
        if arg in ["-o", "--output"]:
            if i + 1 < len(args):
                output_file = args[i + 1]
                i += 1
            else:
                print("Erro: opção -o/--output requer um nome de arquivo.")
                sys.exit(1)
        elif arg in ["--silent", "-s"]:
            silent_mode = True  # Set silent mode flag
        elif not arg.startswith('-'):
            if input_file is None:
                input_file = arg
            else:
                print("Erro: apenas um arquivo de entrada pode ser especificado.")
                sys.exit(1)
        i += 1

    if input_file is None:
        print("Erro: nenhum arquivo de entrada especificado.")
        if not silent_mode:  # Only show usage if not silent
            show_usage()
        sys.exit(1)

    return input_file, output_file, silent_mode

# ======================
# FUNCAO PRINCIPAL
# ======================
@log_execution
def main():
    documentacao()

    # Parse regular arguments
    arquivo_principal, arquivo_saida, silent_mode = parse_arguments()
    dir_principal = os.path.dirname(arquivo_principal) or '.'

    # Generate output filename if not specified
    if arquivo_saida is None:
        nome_base, extensao = os.path.splitext(arquivo_principal)
        arquivo_saida = f"{arquivo_principal}.dat"

    try:
        with open(arquivo_principal, 'r') as f:
            linhas_principal = [linha.rstrip('\n') for linha in f]
    except FileNotFoundError:
        print(f"Erro: Arquivo '{arquivo_principal}' não encontrado.")
        sys.exit(1)

    includes = coletar_includes(linhas_principal)
    all_triggers_desligados = set()
    all_trigger_blocks = {}
    includes_processados = set()

    # Extrair DATE_OPT do arquivo principal
    date_opt_data = extrair_date_opt(linhas_principal)

    if not silent_mode:
        if date_opt_data:
            print(f"[INFO] DATE_OPT encontrado: {formatar_data(date_opt_data)}")
        else:
            print("[INFO] Nenhum DATE_OPT encontrado - processando todos os triggers")

    # 1. COLETAR TODOS OS TRIGGERS DESLIGADOS PRIMEIRO
    # Coletar do arquivo principal
    all_triggers_desligados |= coletar_triggers_desligados(linhas_principal)

    # Coletar de todos os includes
    for inc in includes:
        caminho_inc = os.path.join(dir_principal, inc)
        if os.path.exists(caminho_inc):
            try:
                with open(caminho_inc, 'r') as f:
                    linhas_inc = [linha.rstrip('\n') for linha in f]
            except Exception as e:
                print(f"[ERRO] Não foi possível ler o arquivo include: {caminho_inc}")
                print(f"[ERRO] Detalhes: {str(e)}")
                continue

            # Adicionar triggers desligados deste include
            all_triggers_desligados |= coletar_triggers_desligados(linhas_inc)
            includes_processados.add(inc)

    # 2. PROCESSAR BLOCOS TDATE APÓS COLETA COMPLETA
    for inc in includes:
        caminho_inc = os.path.join(dir_principal, inc)
        if os.path.exists(caminho_inc) and inc in includes_processados:
            try:
                with open(caminho_inc, 'r') as f:
                    linhas_inc = [linha.rstrip('\n') for linha in f]
            except Exception as e:
                continue  # Já foi tratado anteriormente

            # Extrair blocos TDATE com lista completa de desligados
            blocos_inc = extrair_blocos_tdate(linhas_inc, all_triggers_desligados, None)  # Remover filtro DATE_OPT aqui

            # Adicionar ao dicionário geral
            for data, blocos in blocos_inc.items():
                if data in all_trigger_blocks:
                    all_trigger_blocks[data].extend(blocos)
                else:
                    all_trigger_blocks[data] = blocos

    # Process main file structure - agora passando date_opt_data
    novas_linhas = processar_principal(linhas_principal, all_trigger_blocks, date_opt_data)

    # Mark processed includes
    padrao_include = re.compile(r"^\s*include_opt\s+\'(.*\.inc)\'", re.IGNORECASE)
    novas_linhas_marcadas = []
    for linha in novas_linhas:
        match = padrao_include.match(linha)
        if match and match.group(1) in includes_processados:
            novas_linhas_marcadas.append("** " + linha)
        else:
            novas_linhas_marcadas.append(linha)

    # Write output file
    try:
        with open(arquivo_saida, 'w') as f:
            for linha in novas_linhas_marcadas:
                f.write(linha + '\n')
    except Exception as e:
        print(f"[ERRO CRÍTICO] Não foi possível escrever o arquivo de saída: {arquivo_saida}")
        print(f"[ERRO] Detalhes: {str(e)}")
        sys.exit(1)

    if not silent_mode:
        print(f"\n[OK] Arquivo processado salvo como: {arquivo_saida}")
        print(f"[INFO] Tamanho original: {len(linhas_principal)} linhas")
        print(f"[INFO] Tamanho processado: {len(novas_linhas_marcadas)} linhas")
        print(f"[INFO] Includes processados: {len(includes_processados)}")
        print(f"[INFO] Triggers TDATE inseridos: {sum(len(b) for b in all_trigger_blocks.values())}")
        print(f"[INFO] Triggers desligados: {len(all_triggers_desligados)}")

        # Contar triggers movidos para o primeiro bloco pós-DATE_OPT
        if date_opt_data:
            triggers_antigos = 0
            for data_trigger, blocos_trigger in all_trigger_blocks.items():
                if data_trigger < date_opt_data:
                    triggers_antigos += len(blocos_trigger)
            print(f"[INFO] Triggers movidos para primeiro bloco pos-DATE_OPT: {triggers_antigos}")

        if date_opt_data:
            print(f"[INFO] DATE_OPT aplicado: {formatar_data(date_opt_data)}")
            triggers_filtrados = sum(1 for bloco in all_trigger_blocks.values() for _ in bloco)
            print(f"[INFO] Triggers apos DATE_OPT: {triggers_filtrados}")

if __name__ == '__main__':
    main()

# Implementar o que esta em __status__['em_desenvolvimento'].
