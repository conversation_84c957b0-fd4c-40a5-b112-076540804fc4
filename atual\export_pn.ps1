#####################################################################################################################################
# Pre requisito
#####################################################################################################################################
# Install-Module -Name ImportExcel -Scope CurrentUser -Force

#####################################################################################################################################
# Entrada
#####################################################################################################################################

# # Importar o modulo ImportExcel
# Import-Module Import-Excel

$variables = "Qo,Qw,Qg,Qgco2,Qghc,Qgl,On-time Fraction"
$export_path = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\02_PD_Cenario4v250210\Sim_Cen4_SemEVPRO\Docs\Auxiliar"

# Definir projetos
$projects = @{
    "MP_IPB_PID2_PID1" = @{
        proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\02_PD_Cenario4v250210\Sim_Cen4_SemEVPRO\run\BASE\RMSv14_er03_geoeng_output02_044.dat"
        base = "None"
    }
    "PID4" = @{
        proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\02_PD_Cenario4v250210\Sim_Cen4_SemEVPRO\run\ATTEMPT_0065\RMSv14_er03_geoeng_output02_044.dat"
        base = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\02_PD_Cenario4v250210\Sim_Cen4_SemEVPRO\run\BASE\RMSv14_er03_geoeng_output02_044.dat"
    }
    "ICSPB" = @{
        proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\02_PD_Cenario4v250210\Sim_Cen4_SemEVPRO\run\ATTEMPT_0066\RMSv14_er03_geoeng_output02_044.dat"
        base = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\02_PD_Cenario4v250210\Sim_Cen4_SemEVPRO\run\ATTEMPT_0065\RMSv14_er03_geoeng_output02_044.dat"
    }
    "TP58" = @{
        proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\02_PD_Cenario4v250210\Sim_Cen4_SemEVPRO\run\ATTEMPT_0067\RMSv14_er03_geoeng_output02_044.dat"
        base = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\02_PD_Cenario4v250210\Sim_Cen4_SemEVPRO\run\ATTEMPT_0066\RMSv14_er03_geoeng_output02_044.dat"
    }
    "PIDn" = @{
        proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\02_PD_Cenario4v250210\Sim_Cen4_SemEVPRO\run\ATTEMPT_0068\RMSv14_er03_geoeng_output02_044.dat"
        base = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\02_PD_Cenario4v250210\Sim_Cen4_SemEVPRO\run\ATTEMPT_0067\RMSv14_er03_geoeng_output02_044.dat"
    }
}

#####################################################################################################################################
# Verificacao de caminhos
#####################################################################################################################################

Write-Host "Verificando existência dos caminhos em cada projeto..." -ForegroundColor Cyan

foreach ($projectName in $projects.Keys) {
    Write-Host "`n--> Projeto: $projectName" -ForegroundColor Green

    $projPath = $Projects[$projectName].proj
    $basePath = $Projects[$projectName].base

    # Write-Host "    Caminho proj: $projPath"
    # Write-Host "    Caminho base: $basePath"

    # Verificando se existe o caminho proj
    if (Test-Path $projPath) {
        Write-Host "    OK - proj: $projPath existe." -ForegroundColor DarkGreen
    }
    else {
        Write-Host "    ERRO - proj: $projPath NÃO existe!" -ForegroundColor Red
    }

    # Verificando se existe o caminho base
    if ($basePath -and $basePath -ne "None") {
        if (Test-Path $basePath) {
            Write-Host "    OK - base: $basePath existe." -ForegroundColor DarkGreen
        }
        else {
            Write-Host "    ERRO - base: $basePath NAO existe!" -ForegroundColor Red
        }
    }
    else {
        Write-Host "    Observação - base esta vazio ou 'None'. Nada a verificar." -ForegroundColor Yellow
    }
}

Write-Host "`nVerificacao concluida.`n" -ForegroundColor Cyan

#####################################################################################################################################
# Execucao
#####################################################################################################################################

# Definir diretorio do script dinamicamente
# $script_dir = "L:\res\campos\jubarte\er\er02\00_Programas\python\script\atual" # Diretorio do repositorio
$script_dir = $PSScriptRoot # Diretorio do repositorio

# Definir caminhos e variaveis
$python = "L:\res\campos\jubarte\er\er02\00_Programas\python\windows\python-3.12.6.amd64\python.exe"
$script = "$script_dir\ensemble.py"
$command = "delta_export"

# Lista para armazenar todos os .xlsx gerados
$excel_files = @()

foreach ($project in $projects.GetEnumerator()) {
    $proj_name = $project.Key
    $proj_path = $project.Value.proj
    $base_path = $project.Value.base

    Write-Output "Processando projeto: $proj_name"

    # Criar um nome de arquivo unico para cada projeto
    # $temp_excel = Join-Path $export_path "temp_$proj_name.xlsx"
    $temp_excel = Join-Path $export_path "delta_$proj_name.xlsx"

    # Comando a ser executado
    $cmd = "$python $script $command $proj_path $base_path --variables `"$variables`" --export_path `"$temp_excel`""

    Write-Output "Executando: $cmd"

    # Executando o comando
    Invoke-Expression $cmd

    # Adiciona o .xlsx gerado a nossa lista
    $excel_files += $temp_excel

    Write-Output "Projeto $proj_name processado."
    Write-Output ""
}

#####################################################################################################################################
# Mesclar todos os arquivos gerados
#####################################################################################################################################

Write-Output "Realizando mescla de todos os arquivos gerados..."

# Arquivo final de mescla
$merged_xlsx = Join-Path $export_path "delta_merged.xlsx"

# Monta o array de arquivos em uma unica string (cada caminho separado por espaco)
# Ex: "arquivo1.xlsx arquivo2.xlsx arquivo3.xlsx ..."
$files_str = $excel_files -join " "

# Monta o comando para merge_delta_exports
$merge_cmd = "$python `"$script`" merge_delta_exports $files_str --output `"$merged_xlsx`" --file_name2proj"

Write-Output "Executando: $merge_cmd"
Invoke-Expression $merge_cmd

Write-Output "Mescla final concluida. Arquivo gerado: $merged_xlsx"
Write-Output ""

#####################################################################################################################################
# Salvando o script atual no diretorio de saida
#####################################################################################################################################

if ($export_path -ne "") {
    $out_directory = $export_path
    $path_without_extension = Join-Path -Path $out_directory "delta_gen"

    # Caminho do novo script a ser salvo
    $script_output_path = "$path_without_extension.ps1"

    # Obter o conteudo do script atual
    $script_content = Get-Content -Path $MyInvocation.MyCommand.Path

    # Substituir a variavel $script_dir pelo diretorio original
    $script_content = $script_content -replace ' = \$PSScriptRoot # Diretorio do repositorio', " = `"$script_dir`" # Diretorio do repositorio"

    # Salvar o conteudo no diretorio de saida
    $script_content | Out-File -FilePath $script_output_path -Encoding UTF8

    # Exibir mensagem de confirmacao
    Write-Output "O script foi salvo em: $script_output_path"

    Write-Output "Segue o comando para executar novamente:"
    Write-Output "powershell.exe -ExecutionPolicy Bypass -File $script_output_path"
}

#####################################################################################################################################
# FinalizaÃ§Ã£o
#####################################################################################################################################

Write-Host "`nPressione qualquer tecla para finalizar..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
