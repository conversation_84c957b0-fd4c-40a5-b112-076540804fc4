#!/usr/bin/env python3
"""
Sistema Otimizado de Cálculo de Volumes para Dados de Petróleo
Versão Python 3.1 - Análise Avançada com Pandas e NumPy (Melhorada)

Autor: Sistema de Análise de Produção
Data: 2025

Melhorias implementadas:
- Preservação exata das datas originais
- Melhor tratamento de erros e validação
- Otimização de performance
- Logging mais detalhado
- Formatação aprimorada de dados
- Suporte a diferentes formatos de data
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, date
import warnings
from pathlib import Path
import argparse
from typing import Dict, List, Tuple, Optional, Union
import logging
from calendar import monthrange
import traceback

# Configuração de logging mais detalhada
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('volume_calculator.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# Suprimir warnings específicos do pandas
warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)
warnings.filterwarnings('ignore', category=UserWarning)

class VolumeCalculator:
    """
    Classe principal para cálculo de volumes de produção de petróleo
    Versão melhorada com preservação de datas e otimizações
    """
    
    def __init__(self):
        """Inicializa o calculador com configurações padrão"""
        self.vazoes_config = {
            'Qo': {'coluna': '1 - Qo Pot', 'nome': 'Óleo', 'unidade': 'm³/d'},
            'Qg': {'coluna': '2 - Qg Pot', 'nome': 'Gás', 'unidade': 'm³/d'},
            'Qw': {'coluna': '3 - Qw Pot', 'nome': 'Água', 'unidade': 'm³/d'},
            'Qwi': {'coluna': '4 - Qwi Pot', 'nome': 'Água Injetada', 'unidade': 'm³/d'},
            'Qgi': {'coluna': '5 - Qgi Pot', 'nome': 'Gás Injetado', 'unidade': 'm³/d'},
            'GL': {'coluna': '6 - GL Pot', 'nome': 'Gas Lift', 'unidade': 'm³/d'},
            'Auto': {'coluna': '7 - Auto inj Pot', 'nome': 'Auto Injeção', 'unidade': 'm³/d'}
        }
        
        self.colunas_principais = {
            'poco': 'Poço',
            'data': 'Data',
            'campo': 'Campo',
            'plataforma': 'Plataforma',
            'projeto': 'Projeto'
        }
        
        self.df = None
        self.df_resultado = None
        self.datas_originais = None  # Para preservar formato original das datas
        self.metadata = {}
    
    def carregar_excel(self, caminho_arquivo: str, nome_aba: Optional[str] = None) -> pd.DataFrame:
        """
        Carrega arquivo Excel e realiza limpeza inicial dos dados
        
        Args:
            caminho_arquivo: Caminho para o arquivo Excel
            nome_aba: Nome da aba (opcional, usa a primeira se não especificado)
            
        Returns:
            DataFrame com os dados carregados
        """
        try:
            logger.info(f"Carregando arquivo: {caminho_arquivo}")
            
            # Verifica se o arquivo existe
            if not os.path.exists(caminho_arquivo):
                raise FileNotFoundError(f"Arquivo não encontrado: {caminho_arquivo}")
            
            # Obtém informações do arquivo
            file_size = os.path.getsize(caminho_arquivo) / (1024 * 1024)  # MB
            logger.info(f"Tamanho do arquivo: {file_size:.2f} MB")
            
            # Carrega o arquivo Excel
            if nome_aba:
                df = pd.read_excel(caminho_arquivo, sheet_name=nome_aba, engine='openpyxl')
                logger.info(f"Aba carregada: {nome_aba}")
            else:
                # Lista todas as abas disponíveis
                try:
                    excel_file = pd.ExcelFile(caminho_arquivo)
                    abas_disponiveis = excel_file.sheet_names
                    logger.info(f"Abas disponíveis: {abas_disponiveis}")
                    df = pd.read_excel(caminho_arquivo, sheet_name=0, engine='openpyxl')  # Primeira aba
                    logger.info(f"Usando primeira aba: {abas_disponiveis[0]}")
                except Exception as e:
                    logger.warning(f"Erro ao listar abas: {e}. Carregando aba padrão.")
                    df = pd.read_excel(caminho_arquivo, engine='openpyxl')
            
            logger.info(f"Dados carregados: {len(df)} registros, {len(df.columns)} colunas")
            logger.info(f"Colunas encontradas: {list(df.columns)}")
            
            # Armazena metadata
            self.metadata = {
                'arquivo_origem': caminho_arquivo,
                'aba_origem': nome_aba,
                'data_carregamento': datetime.now(),
                'registros_originais': len(df),
                'colunas_originais': list(df.columns)
            }
            
            # Preserva as datas originais antes da conversão
            if 'Data' in df.columns:
                self.datas_originais = df['Data'].copy()
            
            # Limpeza inicial
            df = self._preparar_dados(df)
            
            self.df = df
            return df
            
        except Exception as e:
            logger.error(f"Erro ao carregar arquivo: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise
    
    def _preparar_dados(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepara e limpa os dados para processamento
        Preserva as datas originais
        
        Args:
            df: DataFrame original
            
        Returns:
            DataFrame limpo e preparado
        """
        logger.info("Preparando e limpando dados...")
        
        # Cria cópia para não modificar o original
        df_clean = df.copy()
        
        # Validação das colunas obrigatórias
        colunas_obrigatorias = ['Data', 'Poço']
        colunas_faltantes = [col for col in colunas_obrigatorias if col not in df_clean.columns]
        
        if colunas_faltantes:
            raise ValueError(f"Colunas obrigatórias não encontradas: {colunas_faltantes}")
        
        # Preserva dados de data original para referência
        df_clean['Data_Original'] = df_clean['Data'].copy()
        
        # Converte coluna de data de forma mais robusta
        if 'Data' in df_clean.columns:
            # Tenta diferentes formatos de data
            df_clean['Data'] = self._converter_datas_robusta(df_clean['Data'])
        
        # Limpa e converte colunas de vazão
        colunas_vazao_processadas = 0
        for vazao_key, vazao_info in self.vazoes_config.items():
            col_name = vazao_info['coluna']
            if col_name in df_clean.columns:
                df_clean[col_name] = self._processar_coluna_numerica(df_clean[col_name], col_name)
                colunas_vazao_processadas += 1
            else:
                logger.warning(f"Coluna de vazão não encontrada: {col_name}")
        
        logger.info(f"Colunas de vazão processadas: {colunas_vazao_processadas}")
        
        # Remove registros com data inválida
        registros_antes = len(df_clean)
        df_clean = df_clean.dropna(subset=['Data'])
        registros_depois = len(df_clean)
        
        if registros_antes != registros_depois:
            logger.warning(f"Removidos {registros_antes - registros_depois} registros com data inválida")
        
        # Validação adicional
        if len(df_clean) == 0:
            raise ValueError("Nenhum registro válido encontrado após limpeza")
        
        # Ordena por Poço e Data
        if 'Poço' in df_clean.columns and 'Data' in df_clean.columns:
            df_clean = df_clean.sort_values(['Poço', 'Data']).reset_index(drop=True)
        
        # Estatísticas de limpeza
        periodo_dados = f"{df_clean['Data'].min().date()} a {df_clean['Data'].max().date()}"
        pocos_unicos = df_clean['Poço'].nunique()
        
        logger.info(f"Dados limpos: {len(df_clean)} registros válidos")
        logger.info(f"Período dos dados: {periodo_dados}")
        logger.info(f"Poços únicos: {pocos_unicos}")
        
        return df_clean
    
    def _converter_datas_robusta(self, serie_datas: pd.Series) -> pd.Series:
        """
        Converte datas de forma robusta, tentando diferentes formatos
        
        Args:
            serie_datas: Série com datas em diferentes formatos
            
        Returns:
            Série com datas convertidas
        """
        formatos_data = [
            '%Y-%m-%d',
            '%d/%m/%Y',
            '%m/%d/%Y',
            '%d-%m-%Y',
            '%Y/%m/%d',
            '%d.%m.%Y',
            '%Y.%m.%d'
        ]
        
        # Primeiro tenta conversão automática do pandas
        try:
            datas_convertidas = pd.to_datetime(serie_datas, errors='coerce', dayfirst=True)
            if not datas_convertidas.isna().all():
                logger.info("Conversão automática de datas bem-sucedida")
                return datas_convertidas
        except:
            pass
        
        # Se falhar, tenta formatos específicos
        for formato in formatos_data:
            try:
                datas_convertidas = pd.to_datetime(serie_datas, format=formato, errors='coerce')
                if not datas_convertidas.isna().all():
                    logger.info(f"Datas convertidas usando formato: {formato}")
                    return datas_convertidas
            except:
                continue
        
        # Última tentativa: conversão forçada
        logger.warning("Usando conversão forçada de datas - pode haver inconsistências")
        return pd.to_datetime(serie_datas, errors='coerce', infer_datetime_format=True)
    
    def _processar_coluna_numerica(self, serie: pd.Series, nome_coluna: str) -> pd.Series:
        """
        Processa coluna numérica de forma robusta
        
        Args:
            serie: Série a ser processada
            nome_coluna: Nome da coluna para logging
            
        Returns:
            Série processada
        """
        try:
            # Converte para string primeiro
            serie_str = serie.astype(str)
            
            # Remove espaços e caracteres especiais
            serie_limpa = (serie_str
                          .str.replace(' ', '', regex=False)
                          .str.replace(',', '.', regex=False)
                          .str.replace('−', '-', regex=False)  # hífen especial
                          .str.replace('–', '-', regex=False)  # en dash
                          .str.replace('—', '-', regex=False)  # em dash
                          )
            
            # Converte para numérico
            serie_numerica = pd.to_numeric(serie_limpa, errors='coerce')
            
            # Estatísticas de conversão
            valores_invalidos = serie_numerica.isna().sum()
            if valores_invalidos > 0:
                logger.warning(f"Coluna {nome_coluna}: {valores_invalidos} valores inválidos convertidos para 0")
            
            # Preenche NaN com 0
            serie_final = serie_numerica.fillna(0)
            
            # Validação de valores negativos (apenas warning)
            valores_negativos = (serie_final < 0).sum()
            if valores_negativos > 0:
                logger.info(f"Coluna {nome_coluna}: {valores_negativos} valores negativos encontrados")
            
            return serie_final
            
        except Exception as e:
            logger.error(f"Erro ao processar coluna {nome_coluna}: {e}")
            return pd.Series([0] * len(serie), index=serie.index)
    
    def calcular_dias_mes_preciso(self, data_series: pd.Series) -> pd.Series:
        """
        Calcula o número de dias no mês para cada data de forma mais precisa
        
        Args:
            data_series: Série com datas
            
        Returns:
            Série com número de dias do mês
        """
        def dias_no_mes(data):
            try:
                if pd.isna(data):
                    return 30  # Valor padrão
                return monthrange(data.year, data.month)[1]
            except:
                return 30  # Valor padrão em caso de erro
        
        return data_series.apply(dias_no_mes)
    
    def calcular_volumes_otimizado(self) -> pd.DataFrame:
        """
        Calcula volumes mensais e acumulados para todas as vazões
        Versão otimizada com preservação das datas originais
        
        Returns:
            DataFrame com todos os volumes calculados
        """
        if self.df is None:
            raise ValueError("Dados não carregados. Use carregar_excel() primeiro.")
        
        logger.info("Iniciando cálculo de volumes...")
        start_time = datetime.now()
        
        # Cria cópia dos dados
        df_calc = self.df.copy()
        
        # Calcula dias do mês de forma precisa
        df_calc['dias_mes'] = self.calcular_dias_mes_preciso(df_calc['Data'])
        
        # Restaura as datas originais se disponíveis
        if 'Data_Original' in df_calc.columns:
            df_calc['Data'] = df_calc['Data_Original']
            logger.info("Datas originais restauradas nos resultados")
        
        # Contador de volumes calculados
        volumes_calculados = 0
        
        # Calcula volumes para todas as vazões simultaneamente
        for vazao_key, vazao_info in self.vazoes_config.items():
            col_vazao = vazao_info['coluna']
            
            if col_vazao in df_calc.columns:
                # Volume mensal = vazão * dias do mês
                col_volume = f'Vol_{vazao_key}'
                df_calc[col_volume] = df_calc[col_vazao] * df_calc['dias_mes']
                
                # Volume acumulado por poço usando groupby + cumsum (muito eficiente)
                col_acumulado = f'Acum_{vazao_key}'
                df_calc[col_acumulado] = df_calc.groupby('Poço')[col_volume].cumsum()
                
                volumes_calculados += 1
                
                # Log estatísticas básicas
                volume_total = df_calc[col_volume].sum()
                logger.info(f"{vazao_info['nome']}: Volume total = {volume_total:,.2f}")
        
        # Remove colunas auxiliares
        colunas_para_remover = ['dias_mes']
        if 'Data_Original' in df_calc.columns:
            colunas_para_remover.append('Data_Original')
        
        df_calc = df_calc.drop(columns=colunas_para_remover, errors='ignore')
        
        elapsed_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"Cálculo concluído em {elapsed_time:.2f} segundos")
        logger.info(f"Total de tipos de volume calculados: {volumes_calculados}")
        
        self.df_resultado = df_calc
        return df_calc
    
    def validar_resultado(self) -> Dict[str, bool]:
        """
        Valida o resultado dos cálculos
        
        Returns:
            Dicionário com resultados da validação
        """
        if self.df_resultado is None:
            return {'erro': True, 'mensagem': 'Nenhum resultado para validar'}
        
        validacao = {
            'dados_presentes': len(self.df_resultado) > 0,
            'datas_validas': not self.df_resultado['Data'].isna().any(),
            'pocos_presentes': not self.df_resultado['Poço'].isna().any(),
            'volumes_calculados': False,
            'acumulados_calculados': False,
            'valores_negativos_volumes': False,
            'consistencia_acumulados': True
        }
        
        # Verifica se volumes foram calculados
        colunas_volume = [col for col in self.df_resultado.columns if col.startswith('Vol_')]
        colunas_acumulado = [col for col in self.df_resultado.columns if col.startswith('Acum_')]
        
        validacao['volumes_calculados'] = len(colunas_volume) > 0
        validacao['acumulados_calculados'] = len(colunas_acumulado) > 0
        
        # Verifica valores negativos em volumes
        if colunas_volume:
            for col in colunas_volume:
                if (self.df_resultado[col] < 0).any():
                    validacao['valores_negativos_volumes'] = True
                    break
        
        # Verifica consistência dos acumulados (devem ser crescentes por poço)
        if colunas_acumulado:
            for poco in self.df_resultado['Poço'].unique():
                df_poco = self.df_resultado[self.df_resultado['Poço'] == poco].sort_values('Data')
                for col in colunas_acumulado:
                    if not df_poco[col].is_monotonic_increasing:
                        validacao['consistencia_acumulados'] = False
                        logger.warning(f"Inconsistência em acumulados para poço {poco}, coluna {col}")
                        break
                if not validacao['consistencia_acumulados']:
                    break
        
        # Log dos resultados da validação
        problemas = [k for k, v in validacao.items() if not v and k != 'valores_negativos_volumes']
        if problemas:
            logger.warning(f"Problemas encontrados na validação: {problemas}")
        else:
            logger.info("Validação concluída com sucesso")
        
        return validacao
    
    def gerar_relatorio_estatistico(self) -> Dict:
        """
        Gera relatório estatístico detalhado dos dados processados
        
        Returns:
            Dicionário com estatísticas completas
        """
        if self.df_resultado is None:
            raise ValueError("Execute calcular_volumes_otimizado() primeiro.")
        
        # Validação primeiro
        validacao = self.validar_resultado()
        
        relatorio = {
            'metadata': self.metadata,
            'validacao': validacao,
            'total_registros': len(self.df_resultado),
            'periodo': {
                'inicio': self.df_resultado['Data'].min(),
                'fim': self.df_resultado['Data'].max(),
                'dias_total': (self.df_resultado['Data'].max() - self.df_resultado['Data'].min()).days
            },
            'pocos_unicos': self.df_resultado['Poço'].nunique(),
            'campos_unicos': self.df_resultado['Campo'].nunique() if 'Campo' in self.df_resultado.columns else 0,
            'estatisticas_vazoes': {},
            'estatisticas_gerais': {}
        }
        
        # Lista de poços
        relatorio['lista_pocos'] = sorted(self.df_resultado['Poço'].unique().tolist())
        
        # Estatísticas por vazão
        for vazao_key, vazao_info in self.vazoes_config.items():
            col_vazao = vazao_info['coluna']
            col_volume = f'Vol_{vazao_key}'
            col_acumulado = f'Acum_{vazao_key}'
            
            if col_vazao in self.df_resultado.columns:
                vazao_stats = {
                    'nome': vazao_info['nome'],
                    'unidade': vazao_info['unidade'],
                    'vazao_media': float(self.df_resultado[col_vazao].mean()),
                    'vazao_mediana': float(self.df_resultado[col_vazao].median()),
                    'vazao_max': float(self.df_resultado[col_vazao].max()),
                    'vazao_min': float(self.df_resultado[col_vazao].min()),
                    'vazao_std': float(self.df_resultado[col_vazao].std()),
                    'registros_ativos': int((self.df_resultado[col_vazao] > 0).sum()),
                    'registros_zero': int((self.df_resultado[col_vazao] == 0).sum()),
                    'percentual_ativo': float((self.df_resultado[col_vazao] > 0).mean() * 100)
                }
                
                if col_volume in self.df_resultado.columns:
                    vazao_stats.update({
                        'volume_total': float(self.df_resultado[col_volume].sum()),
                        'volume_medio_mensal': float(self.df_resultado[col_volume].mean())
                    })
                
                if col_acumulado in self.df_resultado.columns:
                    vazao_stats['volume_acumulado_final'] = float(self.df_resultado[col_acumulado].max())
                
                relatorio['estatisticas_vazoes'][vazao_key] = vazao_stats
        
        # Estatísticas gerais
        relatorio['estatisticas_gerais'] = {
            'densidade_temporal': len(self.df_resultado) / max(1, relatorio['periodo']['dias_total']),
            'registros_por_poco': len(self.df_resultado) / max(1, relatorio['pocos_unicos']),
            'periodo_medio_por_poco': relatorio['periodo']['dias_total'] / max(1, relatorio['pocos_unicos'])
        }
        
        return relatorio
    
    def salvar_resultado(self, caminho_saida: str, incluir_detalhes: bool = True):
        """
        Salva resultados em arquivo Excel com múltiplas abas
        Preserva formatação das datas originais
        
        Args:
            caminho_saida: Caminho do arquivo de saída
            incluir_detalhes: Se deve incluir abas com detalhes e relatórios
        """
        if self.df_resultado is None:
            raise ValueError("Execute calcular_volumes_otimizado() primeiro.")
        
        logger.info(f"Salvando resultados em: {caminho_saida}")
        
        try:
            with pd.ExcelWriter(caminho_saida, engine='openpyxl', datetime_format='DD/MM/YYYY') as writer:
                # Aba principal com todos os dados
                df_para_salvar = self.df_resultado.copy()
                
                # Formatar colunas numéricas para melhor visualização
                colunas_numericas = [col for col in df_para_salvar.columns 
                                   if col.startswith(('Vol_', 'Acum_', '1 -', '2 -', '3 -', '4 -', '5 -', '6 -', '7 -'))]
                
                for col in colunas_numericas:
                    if col in df_para_salvar.columns:
                        df_para_salvar[col] = df_para_salvar[col].round(2)
                
                df_para_salvar.to_excel(writer, sheet_name='Dados_Completos', index=False)
                
                if incluir_detalhes:
                    # Aba com resumo por poço
                    resumo_poco = self._gerar_resumo_por_poco()
                    if not resumo_poco.empty:
                        resumo_poco.to_excel(writer, sheet_name='Resumo_por_Poco', index=False)
                    
                    # Aba com resumo mensal
                    resumo_mensal = self._gerar_resumo_mensal()
                    if not resumo_mensal.empty:
                        resumo_mensal.to_excel(writer, sheet_name='Resumo_Mensal', index=False)
                    
                    # Aba com relatório estatístico
                    relatorio = self.gerar_relatorio_estatistico()
                    
                    # Converte relatório para formato tabular
                    relatorio_rows = []
                    
                    # Informações gerais
                    relatorio_rows.extend([
                        ['Tipo', 'Métrica', 'Valor'],
                        ['Geral', 'Total de Registros', relatorio['total_registros']],
                        ['Geral', 'Poços Únicos', relatorio['pocos_unicos']],
                        ['Geral', 'Campos Únicos', relatorio['campos_unicos']],
                        ['Período', 'Data Início', relatorio['periodo']['inicio']],
                        ['Período', 'Data Fim', relatorio['periodo']['fim']],
                        ['Período', 'Dias Total', relatorio['periodo']['dias_total']],
                        ['', '', ''],  # Linha vazia
                    ])
                    
                    # Estatísticas por vazão
                    for vazao_key, stats in relatorio['estatisticas_vazoes'].items():
                        relatorio_rows.extend([
                            [f'Vazão - {stats["nome"]}', 'Média', f"{stats['vazao_media']:.2f}"],
                            [f'Vazão - {stats["nome"]}', 'Máximo', f"{stats['vazao_max']:.2f}"],
                            [f'Vazão - {stats["nome"]}', 'Volume Total', f"{stats.get('volume_total', 0):.2f}"],
                            [f'Vazão - {stats["nome"]}', 'Registros Ativos', stats['registros_ativos']],
                            [f'Vazão - {stats["nome"]}', '% Ativo', f"{stats['percentual_ativo']:.1f}%"],
                            ['', '', ''],  # Linha vazia
                        ])
                    
                    df_relatorio = pd.DataFrame(relatorio_rows[1:], columns=relatorio_rows[0])
                    df_relatorio.to_excel(writer, sheet_name='Relatorio_Estatistico', index=False)
                    
                    # Aba com validação
                    validacao_rows = [
                        ['Validação', 'Status'],
                        ['Dados Presentes', 'OK' if relatorio['validacao']['dados_presentes'] else 'ERRO'],
                        ['Datas Válidas', 'OK' if relatorio['validacao']['datas_validas'] else 'ERRO'],
                        ['Poços Presentes', 'OK' if relatorio['validacao']['pocos_presentes'] else 'ERRO'],
                        ['Volumes Calculados', 'OK' if relatorio['validacao']['volumes_calculados'] else 'ERRO'],
                        ['Acumulados Calculados', 'OK' if relatorio['validacao']['acumulados_calculados'] else 'ERRO'],
                        ['Consistência Acumulados', 'OK' if relatorio['validacao']['consistencia_acumulados'] else 'AVISO'],
                    ]
                    
                    df_validacao = pd.DataFrame(validacao_rows[1:], columns=validacao_rows[0])
                    df_validacao.to_excel(writer, sheet_name='Validacao', index=False)
            
            file_size = os.path.getsize(caminho_saida) / (1024 * 1024)  # MB
            logger.info(f"Arquivo salvo com sucesso! Tamanho: {file_size:.2f} MB")
            
        except Exception as e:
            logger.error(f"Erro ao salvar arquivo: {str(e)}")
            raise
    
    def _gerar_resumo_por_poco(self) -> pd.DataFrame:
        """Gera resumo consolidado por poço com formatação melhorada"""
        if self.df_resultado is None:
            return pd.DataFrame()
        
        try:
            # Identifica colunas de volume disponíveis
            colunas_volume = [col for col in self.df_resultado.columns if col.startswith('Vol_')]
            colunas_acumulado = [col for col in self.df_resultado.columns if col.startswith('Acum_')]
            
            # Agrupa por poço
            agg_dict = {
                'Data': ['min', 'max', 'count']
            }
            
            # Adiciona colunas de volume
            for col in colunas_volume:
                agg_dict[col] = ['sum', 'mean', 'max']
            
            # Para acumulados, pega apenas o valor máximo (final)
            for col in colunas_acumulado:
                agg_dict[col] = 'max'
            
            resumo = self.df_resultado.groupby('Poço').agg(agg_dict).round(2)
            
            # Achata o MultiIndex das colunas
            new_columns = []
            for col in resumo.columns:
                if isinstance(col, tuple):
                    if len(col) == 2:
                        new_columns.append(f"{col[0]}_{col[1]}")
                    else:
                        new_columns.append(str(col))
                else:
                    new_columns.append(str(col))
            
            resumo.columns = new_columns
            resumo = resumo.reset_index()
            
            # Renomeia colunas para melhor legibilidade
            rename_dict = {}
            for col in resumo.columns:
                if 'Data_min' in col:
                    rename_dict[col] = 'Data_Inicio'
                elif 'Data_max' in col:
                    rename_dict[col] = 'Data_Fim'
                elif 'Data_count' in col:
                    rename_dict[col] = 'Total_Registros'
            
            resumo = resumo.rename(columns=rename_dict)
            
            return resumo
            
        except Exception as e:
            logger.error(f"Erro ao gerar resumo por poço: {str(e)}")
            return pd.DataFrame()
    
    def _gerar_resumo_mensal(self) -> pd.DataFrame:
        """Gera resumo consolidado mensal com formatação melhorada"""
        if self.df_resultado is None:
            return pd.DataFrame()
        
        try:
            # Cria cópia para não modificar o original
            df_temp = self.df_resultado.copy()
            
            # Cria coluna ano-mês preservando o formato
            df_temp['Ano_Mes'] = df_temp['Data'].dt.to_period('M')
            
            # Identifica colunas de volume
            colunas_volume = [col for col in df_temp.columns if col.startswith('Vol_')]
            colunas_vazao = [col for col in df_temp.columns if any(col.startswith(f"{i} -") for i in range(1, 8))]
            
            agg_dict = {
                'Poço': 'nunique',
                'Data': 'count'
            }
            
            # Adiciona colunas de volume (soma mensal)
            for col in colunas_volume:
                agg_dict[col] = 'sum'
            
            # Adiciona colunas de vazão (média mensal)
            for col in colunas_vazao:
                agg_dict[col] = 'mean'
            
            resumo_mensal = df_temp.groupby('Ano_Mes').agg(agg_dict).round(2)
            resumo_mensal = resumo_mensal.reset_index()
            
            # Converte período para string legível
            resumo_mensal['Ano_Mes'] = resumo_mensal['Ano_Mes'].astype(str)
            
            # Renomeia colunas
            resumo_mensal = resumo_mensal.rename(columns={
                'Poço': 'Poços_Ativos',
                'Data': 'Total_Registros'
            })
            
            return resumo_mensal
            
        except Exception as e:
            logger.error(f"Erro ao gerar resumo mensal: {str(e)}")
            return pd.DataFrame()
    
    def exportar_para_csv(self, diretorio_saida: str):
        """
        Exporta dados para múltiplos arquivos CSV com encoding UTF-8
        
        Args:
            diretorio_saida: Diretório onde salvar os arquivos
        """
        if self.df_resultado is None:
            raise ValueError("Execute calcular_volumes_otimizado() primeiro.")
        
        try:
            # Cria diretório se não existe
            Path(diretorio_saida).mkdir(parents=True, exist_ok=True)
            logger.info(f"Exportando CSVs para: {diretorio_saida}")
            
            # Dados completos
            arquivo_completo = os.path.join(diretorio_saida, 'dados_completos.csv')
            self.df_resultado.to_csv(
                arquivo_completo, 
                index=False, 
                encoding='utf-8-sig',
                date_format='%d/%m/%Y',
                float_format='%.2f'
            )
            
            # Resumos
            resumo_poco = self._gerar_resumo_por_poco()
            if not resumo_poco.empty:
                arquivo_poco = os.path.join(diretorio_saida, 'resumo_por_poco.csv')
                resumo_poco.to_csv(
                    arquivo_poco, 
                    index=False, 
                    encoding='utf-8-sig',
                    float_format='%.2f'
                )
            
            resumo_mensal = self._gerar_resumo_mensal()
            if not resumo_mensal.empty:
                arquivo_mensal = os.path.join(diretorio_saida, 'resumo_mensal.csv')
                resumo_mensal.to_csv(
                    arquivo_mensal, 
                    index=False, 
                    encoding='utf-8-sig',
                    float_format='%.2f'
                )
            
            # Relatório estatístico em CSV
            relatorio = self.gerar_relatorio_estatistico()
            
            # Converte estatísticas principais para CSV
            stats_data = []
            for vazao_key, stats in relatorio['estatisticas_vazoes'].items():
                stats_data.append({
                    'Vazao': stats['nome'],
                    'Vazao_Media': stats['vazao_media'],
                    'Vazao_Max': stats['vazao_max'],
                    'Volume_Total': stats.get('volume_total', 0),
                    'Registros_Ativos': stats['registros_ativos'],
                    'Percentual_Ativo': stats['percentual_ativo']
                })
            
            if stats_data:
                df_stats = pd.DataFrame(stats_data)
                arquivo_stats = os.path.join(diretorio_saida, 'estatisticas_vazoes.csv')
                df_stats.to_csv(
                    arquivo_stats, 
                    index=False, 
                    encoding='utf-8-sig',
                    float_format='%.2f'
                )
            
            logger.info(f"Arquivos CSV salvos em: {diretorio_saida}")
            
        except Exception as e:
            logger.error(f"Erro ao exportar CSVs: {str(e)}")
            raise
    
    def gerar_relatorio_texto(self, caminho_arquivo: str):
        """
        Gera relatório detalhado em formato texto
        
        Args:
            caminho_arquivo: Caminho do arquivo de relatório
        """
        if self.df_resultado is None:
            raise ValueError("Execute calcular_volumes_otimizado() primeiro.")
        
        try:
            relatorio = self.gerar_relatorio_estatistico()
            
            with open(caminho_arquivo, 'w', encoding='utf-8') as f:
                f.write("="*80 + "\n")
                f.write("RELATÓRIO DE ANÁLISE DE VOLUMES DE PRODUÇÃO DE PETRÓLEO\n")
                f.write("="*80 + "\n\n")
                
                f.write(f"Data de Geração: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}\n")
                f.write(f"Arquivo de Origem: {relatorio['metadata'].get('arquivo_origem', 'N/A')}\n")
                f.write(f"Data de Carregamento: {relatorio['metadata'].get('data_carregamento', 'N/A')}\n\n")
                
                # Informações Gerais
                f.write("INFORMAÇÕES GERAIS\n")
                f.write("-"*40 + "\n")
                f.write(f"Total de Registros: {relatorio['total_registros']:,}\n")
                f.write(f"Poços Únicos: {relatorio['pocos_unicos']}\n")
                f.write(f"Campos Únicos: {relatorio['campos_unicos']}\n")
                f.write(f"Período: {relatorio['periodo']['inicio'].strftime('%d/%m/%Y')} a {relatorio['periodo']['fim'].strftime('%d/%m/%Y')}\n")
                f.write(f"Dias Totais: {relatorio['periodo']['dias_total']}\n\n")
                
                # Validação
                f.write("VALIDAÇÃO DOS DADOS\n")
                f.write("-"*40 + "\n")
                validacao = relatorio['validacao']
                for item, status in validacao.items():
                    if item != 'valores_negativos_volumes':
                        status_str = "✓ OK" if status else "✗ PROBLEMA"
                        f.write(f"{item.replace('_', ' ').title()}: {status_str}\n")
                f.write("\n")
                
                # Estatísticas por Vazão
                f.write("ESTATÍSTICAS POR TIPO DE VAZÃO\n")
                f.write("-"*40 + "\n")
                
                for vazao_key, stats in relatorio['estatisticas_vazoes'].items():
                    f.write(f"\n{stats['nome']} ({stats['unidade']}):\n")
                    f.write(f"  Vazão Média: {stats['vazao_media']:,.2f}\n")
                    f.write(f"  Vazão Máxima: {stats['vazao_max']:,.2f}\n")
                    f.write(f"  Vazão Mínima: {stats['vazao_min']:,.2f}\n")
                    f.write(f"  Desvio Padrão: {stats['vazao_std']:,.2f}\n")
                    if 'volume_total' in stats:
                        f.write(f"  Volume Total: {stats['volume_total']:,.2f}\n")
                        f.write(f"  Volume Médio Mensal: {stats['volume_medio_mensal']:,.2f}\n")
                    f.write(f"  Registros Ativos: {stats['registros_ativos']:,} ({stats['percentual_ativo']:.1f}%)\n")
                    f.write(f"  Registros Zero: {stats['registros_zero']:,}\n")
                
                # Lista de Poços
                f.write(f"\nLISTA DE POÇOS ({len(relatorio['lista_pocos'])} poços):\n")
                f.write("-"*40 + "\n")
                
                # Agrupa poços em linhas de 5 colunas
                pocos = relatorio['lista_pocos']
                for i in range(0, len(pocos), 5):
                    linha_pocos = pocos[i:i+5]
                    f.write(" | ".join(f"{poco:<15}" for poco in linha_pocos) + "\n")
                
                f.write("\n" + "="*80 + "\n")
                f.write("Fim do Relatório\n")
                f.write("="*80 + "\n")
            
            logger.info(f"Relatório em texto salvo: {caminho_arquivo}")
            
        except Exception as e:
            logger.error(f"Erro ao gerar relatório em texto: {str(e)}")
            raise

def main():
    """Função principal para execução via linha de comando com melhorias"""
    parser = argparse.ArgumentParser(
        description='Calculador de Volumes de Produção de Petróleo - Versão Melhorada',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemplos de uso:
  python volume_calculator.py dados.xlsx
  python volume_calculator.py dados.xlsx -o resultado.xlsx --relatorio
  python volume_calculator.py dados.xlsx --aba "Produção" --csv ./csv_output
  python volume_calculator.py dados.xlsx --txt relatorio.txt --validar
        """
    )
    
    parser.add_argument('arquivo_excel', help='Caminho para o arquivo Excel de entrada')
    parser.add_argument('-o', '--output', default='resultado_volumes.xlsx', 
                       help='Nome do arquivo de saída (default: resultado_volumes.xlsx)')
    parser.add_argument('--aba', help='Nome da aba do Excel (opcional)')
    parser.add_argument('--csv', help='Diretório para exportar CSVs (opcional)')
    parser.add_argument('--txt', help='Arquivo para relatório em texto (opcional)')
    parser.add_argument('--relatorio', action='store_true', 
                       help='Exibir relatório estatístico no console')
    parser.add_argument('--validar', action='store_true',
                       help='Executar validação detalhada dos resultados')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Logging detalhado')
    
    args = parser.parse_args()
    
    # Configura nível de logging
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("Modo verbose ativado")
    
    try:
        # Inicializa calculador
        calc = VolumeCalculator()
        
        # Carrega dados
        logger.info("Iniciando processamento...")
        calc.carregar_excel(args.arquivo_excel, args.aba)
        
        # Calcula volumes
        resultado = calc.calcular_volumes_otimizado()
        logger.info(f"Processados {len(resultado)} registros")
        
        # Validação se solicitada
        if args.validar:
            validacao = calc.validar_resultado()
            print("\n" + "="*50)
            print("RESULTADO DA VALIDAÇÃO")
            print("="*50)
            for item, status in validacao.items():
                if item != 'valores_negativos_volumes':
                    status_str = "✓ OK" if status else "✗ PROBLEMA"
                    print(f"{item.replace('_', ' ').title()}: {status_str}")
            print()
        
        # Salva resultado principal
        calc.salvar_resultado(args.output, incluir_detalhes=True)
        logger.info(f"Resultado principal salvo em: {args.output}")
        
        # Exporta CSVs se solicitado
        if args.csv:
            calc.exportar_para_csv(args.csv)
        
        # Gera relatório em texto se solicitado
        if args.txt:
            calc.gerar_relatorio_texto(args.txt)
        
        # Mostra relatório no console se solicitado
        if args.relatorio:
            relatorio = calc.gerar_relatorio_estatistico()
            print("\n" + "="*60)
            print("RELATÓRIO ESTATÍSTICO")
            print("="*60)
            print(f"Total de registros: {relatorio['total_registros']:,}")
            print(f"Período: {relatorio['periodo']['inicio'].strftime('%d/%m/%Y')} a {relatorio['periodo']['fim'].strftime('%d/%m/%Y')}")
            print(f"Poços únicos: {relatorio['pocos_unicos']}")
            print(f"Campos únicos: {relatorio['campos_unicos']}")
            print(f"Dias totais: {relatorio['periodo']['dias_total']}")
            
            print("\nEstatísticas por Vazão:")
            print("-" * 40)
            for key, stats in relatorio['estatisticas_vazoes'].items():
                print(f"  {stats['nome']}: {stats.get('volume_total', 0):,.2f} volume total | "
                      f"{stats['registros_ativos']:,} registros ativos ({stats['percentual_ativo']:.1f}%)")
            
            print(f"\nPoços processados: {', '.join(relatorio['lista_pocos'][:5])}" + 
                  (f" ... e mais {len(relatorio['lista_pocos']) - 5}" if len(relatorio['lista_pocos']) > 5 else ""))
        
        logger.info("Processamento concluído com sucesso!")
        
    except FileNotFoundError as e:
        logger.error(f"Arquivo não encontrado: {str(e)}")
        sys.exit(1)
    except ValueError as e:
        logger.error(f"Erro de validação: {str(e)}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Erro durante processamento: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)

# Exemplo de uso interativo melhorado
def exemplo_uso():
    """
    Exemplo detalhado de como usar a classe VolumeCalculator
    """
    print("="*60)
    print("CALCULADOR DE VOLUMES DE PRODUÇÃO DE PETRÓLEO")
    print("Versão 3.1 - Melhorada")
    print("="*60)
    print()
    
    print("🔧 COMO USAR ESTE SCRIPT:")
    print()
    
    print("1️⃣ VIA LINHA DE COMANDO:")
    print("   Básico:")
    print("   python volume_calculator.py dados.xlsx")
    print()
    print("   Completo:")
    print("   python volume_calculator.py dados.xlsx -o resultado.xlsx --relatorio --validar")
    print()
    print("   Com exportação CSV:")
    print("   python volume_calculator.py dados.xlsx --csv ./csvs --txt relatorio.txt")
    print()
    
    print("2️⃣ VIA CÓDIGO PYTHON:")
    print("   from volume_calculator import VolumeCalculator")
    print("   ")
    print("   calc = VolumeCalculator()")
    print("   calc.carregar_excel('dados.xlsx')")
    print("   resultado = calc.calcular_volumes_otimizado()")
    print("   calc.salvar_resultado('resultado.xlsx')")
    print("   relatorio = calc.gerar_relatorio_estatistico()")
    print()
    
    print("3️⃣ RECURSOS PRINCIPAIS:")
    print("   ✓ Preservação das datas originais")
    print("   ✓ Validação automática dos dados")
    print("   ✓ Relatórios estatísticos detalhados")
    print("   ✓ Exportação para múltiplos formatos")
    print("   ✓ Tratamento robusto de erros")
    print("   ✓ Logging detalhado")
    print("   ✓ Cálculos otimizados com pandas")
    print()
    
    print("4️⃣ COLUNAS ESPERADAS NO EXCEL:")
    calc = VolumeCalculator()
    print("   Obrigatórias: Data, Poço")
    print("   Vazões suportadas:")
    for key, info in calc.vazoes_config.items():
        print(f"     • {info['coluna']} - {info['nome']} ({info['unidade']})")
    print()
    
    print("5️⃣ ARQUIVOS DE SAÍDA:")
    print("   • Excel com múltiplas abas (dados, resumos, relatórios)")
    print("   • CSVs separados por tipo de informação")
    print("   • Relatório detalhado em texto")
    print("   • Log de processamento")
    print()
    
    print("📋 PARÂMETROS DA LINHA DE COMANDO:")
    print("   arquivo_excel    : Arquivo Excel de entrada (obrigatório)")
    print("   -o, --output     : Nome do arquivo de saída Excel")
    print("   --aba           : Nome da aba específica do Excel")
    print("   --csv           : Diretório para exportar CSVs")
    print("   --txt           : Arquivo para relatório em texto")
    print("   --relatorio     : Mostra estatísticas no console")
    print("   --validar       : Executa validação detalhada")
    print("   --verbose, -v   : Logging detalhado")
    print()

if __name__ == "__main__":
    if len(sys.argv) == 1:
        exemplo_uso()
    else:
        main()

# Dependências necessárias (requirements.txt):
"""
pandas>=1.5.0
numpy>=1.20.0
openpyxl>=3.0.0
pathlib2>=2.3.0
argparse
calendar
"""