import datetime
import os
import pandas as pd
import numpy as np
from tqdm import tqdm
from ajustadorPN import ler_ordem_projetos
from Auxiliares.modules.logging_module import log_execution

@log_execution
def processar_dados_planilha(arquivo_entrada, arquivo_saida):
    """
    Processa um arquivo Excel aplicando multiplicadores conforme regras de dispersão
    e adiciona novos registros com cenário modificado.

    Args:
        arquivo_entrada (str): Caminho do arquivo Excel de entrada
        arquivo_saida (str): Caminho do arquivo Excel de saída
    """
    # Carregar dados
    print("\nCarregando arquivo...")
    df = carregar_dados(arquivo_entrada)

    # Obter configurações
    config = obter_configuracoes()
    identifiers = config['identifiers']
    value_columns = config['value_columns']
    dispersao = config['dispersao']
    reference = config.get('reference', {})
    output_config = config.get('output', {})

    # Filtrar dados de referência
    print("Filtrando dados base...")
    df_referencia = filtrar_referencia(df, reference)

    # Processar dados de referência
    if not df_referencia.empty:
        # Processar regras
        print("Extraindo regras...")
        rules = extrair_regras(dispersao, identifiers, value_columns)
        default_multipliers = obter_multipliers_padrao(dispersao, value_columns)

        # Aplicar regras com progresso
        print("Aplicando multiplicadores:")
        df_processado = aplicar_regras(df_referencia, rules, default_multipliers,
                                     identifiers, value_columns)

        # Ajustar processado com base nas tendências do referencia
        print("Ajustando processado baseado nas tendências do referencia...")
        df_ref = df_referencia.copy()
        df_proc = df_processado.copy()
        df_proc_ajustado = ajustar_proc_base_ref(df_ref, df_proc, config, identifiers, value_columns)
        df_processado = df_proc_ajustado

        # Aplicar configurações de output
        print("Configurando output...")
        df_processado = aplicar_output(df_processado, output_config)

        # Concatenar dados
        print("Gerando dataset final...")
        df_final = pd.concat([df, df_processado], ignore_index=True)
    else:
        df_final = df
    # Salvar resultado
    print("\nSalvando resultado...")
    salvar_resultado(df_final, arquivo_saida, df_processado)
    # salvar_resultado(df_final, arquivo_saida)

@log_execution
def ajustar_proc_base_ref(df_ref, df_proc, config, identifiers, value_columns):
    """
    Ajusta o dataframe processado garantindo a consistência nas reduções de Qo
    com base na referência, seguindo a ordem cumulativa dos projetos.
    Filtra hierarquicamente por Campo, ZP, UEP e Well.
    """
    # Carregar ordem dos projetos
    ordem_projetos_path = config.get('ordem_projetos_path')
    ordem_projetos = ler_ordem_projetos(ordem_projetos_path)

    # Configurações para debug
    debug_enabled = False
    data_alvo = pd.to_datetime('2025-10-01')
    poco_alvo = '6-JUB-48A-ESS'
    iupi_alvo = "NO.P-58"

    # Contador de ajustes totais
    total_ajustes = 0

    # Para cada IUPI na ordem de projetos
    for i in tqdm(range(1, len(ordem_projetos)+1), desc="Processando projetos"):
        try:
            iupis_atuais = ordem_projetos[:i]
            iupi_atual = iupis_atuais[-1]

            # Verificar se o IUPI existe no dataframe de referência
            if not any(df_ref['IUPI'] == iupi_atual):
                tqdm.write(f"IUPI {iupi_atual} não encontrado nos dados de referência. Pulando.")
                continue

            projeto_atual = df_ref.loc[df_ref['IUPI'] == iupi_atual, 'Projeto'].iloc[0]
            tqdm.write(f"Processando IUPI: {iupi_atual}, Projeto: {projeto_atual}")

            # Para cada iteração do projeto, precisamos recarregar todos os dataframes filtrados
            # Filtrar dados para projetos acumulados
            df_ref_acumulado = df_ref[df_ref['IUPI'].isin(iupis_atuais)].copy()
            df_proc_acumulado = df_proc[df_proc['IUPI'].isin(iupis_atuais)].copy()

            # Obter todos os campos únicos
            campos = df_ref_acumulado['Campo'].unique()

            # Para cada campo
            for campo in tqdm(campos, desc=f"Campos", leave=False):
                # Filtrar por Campo
                df_ref_campo = df_ref_acumulado[df_ref_acumulado['Campo'] == campo]
                df_proc_campo = df_proc_acumulado[df_proc_acumulado['Campo'] == campo]

                # Obter todas as ZPs únicas para este Campo
                zps = df_ref_campo['ZP'].unique()

                # Para cada ZP
                for zp in tqdm(zps, desc=f"ZPs", leave=False):
                    # Filtrar por ZP
                    df_ref_zp = df_ref_campo[df_ref_campo['ZP'] == zp]
                    df_proc_zp = df_proc_campo[df_proc_campo['ZP'] == zp]

                    # Obter todas as UEPs únicas para esta ZP
                    ueps = df_ref_zp['UEP'].unique()

                    # Para cada UEP
                    for uep in tqdm(ueps, desc=f"UEPs", leave=False):
                        # Filtrar por UEP
                        df_ref_uep = df_ref_zp[df_ref_zp['UEP'] == uep]
                        df_proc_uep = df_proc_zp[df_proc_zp['UEP'] == uep]

                        # Obter todos os poços únicos para esta UEP
                        wells = df_ref_uep['Well'].unique()

                        # Para cada poço
                        for well in tqdm(wells, desc=f"Poços", leave=False):
                            # Contador de ajustes para este poço
                            ajustes_poco = 0

                            # Limite de iterações para evitar loops infinitos
                            max_iteracoes = 100  # Limite mais razoável

                            # Processo iterativo para o poço
                            for iteracao in range(max_iteracoes):
                                # Recarregar os dados atualizados após cada iteração
                                df_ref_well = df_ref[df_ref['Well'] == well]
                                df_proc_well = df_proc[df_proc['Well'] == well]

                                # Se não houver dados suficientes, pular
                                if len(df_ref_well) < 2 or len(df_proc_well) < 2:
                                    break

                                # Agrupar por data
                                df_ref_by_date = df_ref_well.groupby('Date')[value_columns].sum().reset_index()
                                df_proc_by_date = df_proc_well.groupby('Date')[value_columns].sum().reset_index()

                                # Ordenar por data
                                df_ref_by_date = df_ref_by_date.sort_values('Date')
                                df_proc_by_date = df_proc_by_date.sort_values('Date')

                                # Flag para verificar se algum ajuste foi feito nesta iteração
                                ajuste_feito = False

                                # Verificar cada par de datas consecutivas
                                for idx in range(1, len(df_ref_by_date)):
                                    ref_atual = df_ref_by_date.iloc[idx]
                                    ref_anterior = df_ref_by_date.iloc[idx-1]

                                    # Encontrar entradas correspondentes no processado
                                    proc_atual_mask = df_proc_by_date['Date'] == ref_atual['Date']
                                    proc_anterior_mask = df_proc_by_date['Date'] == ref_anterior['Date']

                                    if not any(proc_atual_mask) or not any(proc_anterior_mask):
                                        continue

                                    proc_atual = df_proc_by_date[proc_atual_mask].iloc[0]
                                    proc_anterior = df_proc_by_date[proc_anterior_mask].iloc[0]

                                    # Debug para data e poço específicos
                                    debug_ativo = debug_enabled and (
                                        pd.to_datetime(ref_atual['Date']) == data_alvo and
                                        well == poco_alvo and
                                        iupi_atual == iupi_alvo
                                    )

                                    # Verificar necessidade de ajuste
                                    if (ref_atual['Qo Pot(m3/day)'] < ref_anterior['Qo Pot(m3/day)'] * 100.1 / 100 and
                                        proc_atual['Qo Pot(m3/day)'] > proc_anterior['Qo Pot(m3/day)']):

                                        # Calcular diferenças
                                        dqo = proc_atual['Qo Pot(m3/day)'] - proc_anterior['Qo Pot(m3/day)']

                                        # Calcular parâmetros auxiliares com tratamento de erros
                                        with np.errstate(divide='ignore', invalid='ignore'):
                                            rgo = proc_atual['Qg Pot(mil m3/day)'] / proc_atual['Qo Pot(m3/day)']
                                            bsw = proc_atual['Qw Pot(m3/day)'] / (proc_atual['Qo Pot(m3/day)'] + proc_atual['Qw Pot(m3/day)'])
                                            tco2 = proc_atual['Qgco2 Pot(mil m3/day)'] / proc_atual['Qg Pot(mil m3/day)']

                                        # Substituir valores inválidos
                                        rgo = rgo if not np.isinf(rgo) and not np.isnan(rgo) else 0
                                        bsw = bsw if not np.isinf(bsw) and not np.isnan(bsw) else 0
                                        tco2 = tco2 if not np.isinf(tco2) and not np.isnan(tco2) else 0

                                        # Calcular demais ajustes
                                        dqw = dqo * bsw / (1 - bsw) if (1 - bsw) != 0 else 0
                                        dqg = dqo * rgo
                                        dqgco2 = dqg * tco2
                                        dqghc = dqg - dqgco2

                                        # Obter um registro representativo para os campos de identificação
                                        ref_row = df_ref_well[df_ref_well['Date'] == ref_atual['Date']].iloc[0]
                                        proc_row = df_proc_well[df_proc_well['Date'] == ref_atual['Date']].iloc[0]

                                        # Criar nova linha de ajuste
                                        new_line = {}

                                        # Adicionar todos os campos de identificação do registro original
                                        for id_col in identifiers:
                                            if id_col in proc_row.index:
                                                new_line[id_col] = proc_row[id_col]
                                            elif id_col == 'Campo':
                                                new_line[id_col] = campo
                                            elif id_col == 'ZP':
                                                new_line[id_col] = zp
                                            elif id_col == 'UEP':
                                                new_line[id_col] = uep
                                            elif id_col == 'Well':
                                                new_line[id_col] = well
                                            elif id_col == 'Projeto':
                                                new_line[id_col] = projeto_atual
                                            elif id_col == 'IUPI':
                                                new_line[id_col] = iupi_atual
                                            elif id_col == 'Date':
                                                new_line[id_col] = ref_atual['Date']
                                            else:
                                                # Para outras colunas, tente pegar da referência ou defina como vazio
                                                new_line[id_col] = ref_row.get(id_col, "") if id_col in ref_row.index else ""

                                        # Adicionar valores calculados
                                        new_line.update({
                                            "Qo Pot(m3/day)": -dqo,
                                            "Qw Pot(m3/day)": -dqw,
                                            "Qg Pot(mil m3/day)": -dqg,
                                            "Qgco2 Pot(mil m3/day)": -dqgco2,
                                            "Qghc Pot(mil m3/day)": -dqghc,
                                            "Qwi Pot(m3/day)": 0.0,
                                            "Qgi Pot(mil m3/day)": 0.0,
                                            "Qgl Pot(mil m3/day)": 0.0,
                                            "Qgco2i Pot(mil m3/day)": 0.0,
                                            "Qghci Pot(mil m3/day)": 0.0,
                                        })

                                        # Adicionar a linha ao dataframe processado
                                        df_proc = pd.concat([df_proc, pd.DataFrame([new_line])], ignore_index=True)

                                        # Marcar que um ajuste foi feito
                                        ajuste_feito = True
                                        ajustes_poco += 1
                                        total_ajustes += 1

                                        if debug_ativo:
                                            tqdm.write(f"Ajuste {ajustes_poco} feito para poço {well} na data {ref_atual['Date']}")

                                        # Sair do loop de datas para reprocessar o poço com os novos valores
                                        break

                                # Se nenhum ajuste foi feito nesta iteração, não precisamos continuar
                                if not ajuste_feito:
                                    break

                            # Relatar número de ajustes para este poço
                            if ajustes_poco > 0:
                                tqdm.write(f"Poço {well}: {ajustes_poco} ajustes realizados")

                            # Verificar se atingimos o limite de iterações
                            if iteracao == max_iteracoes - 1 and ajuste_feito:
                                tqdm.write(f"Aviso: Poço {well} atingiu o limite de {max_iteracoes} iterações. Pode haver ajustes pendentes.")

        except Exception as e:
            tqdm.write(f"Erro ao processar projeto {i} ({iupi_atual if 'iupi_atual' in locals() else 'desconhecido'}): {str(e)}")
            import traceback
            traceback_str = traceback.format_exc()
            for line in traceback_str.split('\n'):
                tqdm.write(line)
            continue

    tqdm.write(f"Total de ajustes realizados: {total_ajustes}")
    return df_proc

@log_execution
def filtrar_referencia(df, reference):
    """Filtra o DataFrame com base na configuração de referência"""
    if not reference:
        return pd.DataFrame()

    mask = (df[list(reference.keys())] == pd.Series(reference)).all(axis=1)
    return df[mask].copy()

@log_execution
def aplicar_output(df_processado, output_config):
    """Aplica as configurações de output ao DataFrame processado"""
    if output_config:
        for col, value in output_config.items():
            df_processado[col] = value
    return df_processado

@log_execution
def carregar_dados(arquivo):
    """Carrega o arquivo Excel para um DataFrame"""
    return pd.read_excel(arquivo)

@log_execution
def obter_configuracoes():
    """Retorna as configurações do processamento"""
    output = {
        'ordem_projetos_path': r"L:\res\campos\jubarte\er\er02\00_Programas\python\script\atual\ordem_projetos.txt",
        'reference': {"Cenario": "P50"},
        'output': {"Cenario": "P90"},
        'identifiers': [
            "Versao", "Cenario", "Campo", "ZP", "NZP", "UEP",
            "Projeto", "IUPI", "Well", "Date"
        ],
        'value_columns': [
            "Qo Pot(m3/day)", "Qw Pot(m3/day)", "Qwi Pot(m3/day)",
            "Qg Pot(mil m3/day)", "Qgi Pot(mil m3/day)", "Qgl Pot(mil m3/day)",
            "Qgco2 Pot(mil m3/day)", "Qgco2i Pot(mil m3/day)",
            "Qghc Pot(mil m3/day)", "Qghci Pot(mil m3/day)"
        ],
        "dispersao": {
            'transition_start_date': "2025-06-01",
            'transition_end_date': "2027-01-01",
            "default": {
                "default": 0.7,
                "Qo Pot(m3/day)": 0.7,
                "Qw Pot(m3/day)": 0.7 * 1.1,
                "Qwi Pot(m3/day)": 0.7,
                "Qg Pot(mil m3/day)": 0.7,
                "Qgi Pot(mil m3/day)": 0.7,
                "Qgl Pot(mil m3/day)": 1,
                "Qgco2 Pot(mil m3/day)": 0.7,
                "Qgco2i Pot(mil m3/day)": 0.7,
                "Qghc Pot(mil m3/day)": 0.7,
                "Qghci Pot(mil m3/day)": 0.7,
            },
            "ZP": {
                "BR100": {
                    "Qo Pot(m3/day)": 0.8,
                    "Qw Pot(m3/day)": 0.8 * 1.2,
                    "Qwi Pot(m3/day)": 0.8,
                    "Qg Pot(mil m3/day)": 0.8,
                    "Qgi Pot(mil m3/day)": 0.8,
                    "Qgl Pot(mil m3/day)": 1,
                    "Qgco2 Pot(mil m3/day)": 0.8,
                    "Qgco2i Pot(mil m3/day)": 0.8,
                    "Qghc Pot(mil m3/day)": 0.8,
                    "Qghci Pot(mil m3/day)": 0.8,
                },
                "CO140-ESS116": {
                    "Qo Pot(m3/day)": 0.7,
                    "Qw Pot(m3/day)": 0.7 * 1.3,
                    "Qwi Pot(m3/day)": 0.7,
                    "Qg Pot(mil m3/day)": 0.7,
                    "Qgi Pot(mil m3/day)": 0.7,
                    "Qgl Pot(mil m3/day)": 1,
                    "Qgco2 Pot(mil m3/day)": 0.7,
                    "Qgco2i Pot(mil m3/day)": 0.7,
                    "Qghc Pot(mil m3/day)": 0.7,
                    "Qghci Pot(mil m3/day)": 0.7,
                },
                "CO140-ESS116": {
                    "Qo Pot(m3/day)": 0.7,
                    "Qw Pot(m3/day)": 0.7 * 1.3,
                    "Qwi Pot(m3/day)": 0.7,
                    "Qg Pot(mil m3/day)": 0.7,
                    "Qgi Pot(mil m3/day)": 0.7,
                    "Qgl Pot(mil m3/day)": 1,
                    "Qgco2 Pot(mil m3/day)": 0.7,
                    "Qgco2i Pot(mil m3/day)": 0.7,
                    "Qghc Pot(mil m3/day)": 0.7,
                    "Qghci Pot(mil m3/day)": 0.7,
                },
                "MCB/COQ-ESS103A": {
                    "Qo Pot(m3/day)": 0.7,
                    "Qw Pot(m3/day)": 0.7 * 1.3,
                    "Qwi Pot(m3/day)": 0.7,
                    "Qg Pot(mil m3/day)": 0.7,
                    "Qgi Pot(mil m3/day)": 0.7,
                    "Qgl Pot(mil m3/day)": 1,
                    "Qgco2 Pot(mil m3/day)": 0.7,
                    "Qgco2i Pot(mil m3/day)": 0.7,
                    "Qghc Pot(mil m3/day)": 0.7,
                    "Qghci Pot(mil m3/day)": 0.7,
                },
                "MCB/COQ-ESS172": {
                    "Qo Pot(m3/day)": 0.7,
                    "Qw Pot(m3/day)": 0.7 * 1.3,
                    "Qwi Pot(m3/day)": 0.7,
                    "Qg Pot(mil m3/day)": 0.7,
                    "Qgi Pot(mil m3/day)": 0.7,
                    "Qgl Pot(mil m3/day)": 1,
                    "Qgco2 Pot(mil m3/day)": 0.7,
                    "Qgco2i Pot(mil m3/day)": 0.7,
                    "Qghc Pot(mil m3/day)": 0.7,
                    "Qghci Pot(mil m3/day)": 0.7,
                },
                "MCB/COQ-PRB2": {
                    "Qo Pot(m3/day)": 0.7,
                    "Qw Pot(m3/day)": 0.7 * 1.3,
                    "Qwi Pot(m3/day)": 0.7,
                    "Qg Pot(mil m3/day)": 0.7,
                    "Qgi Pot(mil m3/day)": 0.7,
                    "Qgl Pot(mil m3/day)": 1,
                    "Qgco2 Pot(mil m3/day)": 0.7,
                    "Qgco2i Pot(mil m3/day)": 0.7,
                    "Qghc Pot(mil m3/day)": 0.7,
                    "Qghci Pot(mil m3/day)": 0.7,
                },
                "MRL700-153": {
                    "Qo Pot(m3/day)": 0.7,
                    "Qw Pot(m3/day)": 0.7 * 1.3,
                    "Qwi Pot(m3/day)": 0.7,
                    "Qg Pot(mil m3/day)": 0.7,
                    "Qgi Pot(mil m3/day)": 0.7,
                    "Qgl Pot(mil m3/day)": 1,
                    "Qgco2 Pot(mil m3/day)": 0.7,
                    "Qgco2i Pot(mil m3/day)": 0.7,
                    "Qghc Pot(mil m3/day)": 0.7,
                    "Qghci Pot(mil m3/day)": 0.7,
                },
                "MRL700-136": {
                    "Qo Pot(m3/day)": 0.7,
                    "Qw Pot(m3/day)": 0.7 * 1.3,
                    "Qwi Pot(m3/day)": 0.7,
                    "Qg Pot(mil m3/day)": 0.7,
                    "Qgi Pot(mil m3/day)": 0.7,
                    "Qgl Pot(mil m3/day)": 1,
                    "Qgco2 Pot(mil m3/day)": 0.7,
                    "Qgco2i Pot(mil m3/day)": 0.7,
                    "Qghc Pot(mil m3/day)": 0.7,
                    "Qghci Pot(mil m3/day)": 0.7,
                },
                "RO300": {
                    "Qo Pot(m3/day)": 0.7,
                    "Qw Pot(m3/day)": 1.05,
                    "Qwi Pot(m3/day)": 0.99,
                    "Qg Pot(mil m3/day)": 0.7,
                    "Qgi Pot(mil m3/day)": 0.7,
                    "Qgl Pot(mil m3/day)": 1,
                    "Qgco2 Pot(mil m3/day)": 0.7,
                    "Qgco2i Pot(mil m3/day)": 0.7,
                    "Qghc Pot(mil m3/day)": 0.7,
                    "Qghci Pot(mil m3/day)": 0.7,
                },
            }
        }
    }

    return output

@log_execution
def extrair_regras(dispersao, identifiers, value_columns):
    """Extrai todas as regras de dispersão em formato padronizado"""
    rules = []

    @log_execution
    def _extract_rules(node, current_conditions, depth):
        multipliers = {}

        if isinstance(node, dict):
            per_col = {}
            scalar = None

            for key in node:
                if key == 'default':
                    continue

                if key in value_columns:
                    per_col[key] = node[key]
                elif key in identifiers:
                    for value in node[key]:
                        new_conditions = current_conditions.copy()
                        new_conditions[key] = value
                        _extract_rules(node[key][value], new_conditions, depth + 1)
                else:
                    scalar = node[key]

            if scalar is not None:
                per_col = {col: scalar for col in value_columns}

            multipliers.update(per_col)
        else:
            scalar = node
            multipliers = {col: scalar for col in value_columns}

        if multipliers:
            rules.append({
                'conditions': current_conditions.copy(),
                'multipliers': multipliers,
                'depth': depth
            })

    # Extrair regras a partir das chaves superiores (excluindo 'default')
    for key in dispersao:
        if key == 'default':
            continue
        if key not in identifiers:
            continue

        for value in dispersao[key]:
            _extract_rules(dispersao[key][value], {key: value}, 1)

    # Ordenar por profundidade (mais específicas primeiro)
    rules.sort(key=lambda x: -x['depth'])

    return rules

@log_execution
def obter_multipliers_padrao(dispersao, value_columns):
    """Obtém os multiplicadores padrão"""
    default_multipliers = dispersao['default']
    return {col: default_multipliers.get(col, default_multipliers['default'])
            for col in value_columns}

@log_execution
def aplicar_regras(df, rules, default_multipliers, identifiers, value_columns):
    """
    Aplica todas as regras ao DataFrame com base na ordem dos IUPIs e exclusividade dos poços
    """
    df = df.copy()

    # Obter datas de transição da configuração
    config = obter_configuracoes()
    dispersao = config['dispersao']
    transition_start = pd.to_datetime(dispersao.get('transition_start_date'))
    transition_end = pd.to_datetime(dispersao.get('transition_end_date'))

    # Carregar ordem dos projetos
    ordem_projetos_path = config.get('ordem_projetos_path')
    ordem_iupis = ler_ordem_projetos(ordem_projetos_path)

    # Dicionário para armazenar poços já processados
    pocos_processados = set()

    # Dicionário para armazenar os multiplicadores por poço
    multiplicadores_por_poco = {}

    print("Determinando poços exclusivos por IUPI na ordem definida...")

    # Para cada IUPI na ordem especificada
    for idx, iupi in enumerate(tqdm(ordem_iupis, desc="Processando IUPIs")):
        # Filtrar dados para o IUPI atual
        df_iupi = df[df['IUPI'] == iupi]

        if df_iupi.empty:
            print(f"Aviso: IUPI {iupi} não encontrado nos dados")
            continue

        # Obter o projeto correspondente
        projeto = df_iupi['Projeto'].iloc[0] if not df_iupi.empty else None

        if not projeto:
            print(f"Aviso: Não foi possível determinar o projeto para IUPI {iupi}")
            continue

        # Obter todos os poços deste IUPI
        pocos_iupi = df_iupi['Well'].unique()

        # Filtrar apenas os poços que ainda não foram processados
        pocos_exclusivos = [poco for poco in pocos_iupi if poco not in pocos_processados]

        # Adicionar os poços exclusivos à lista de processados
        pocos_processados.update(pocos_exclusivos)

        print(f"IUPI: {iupi}, Projeto: {projeto}, Poços exclusivos: {len(pocos_exclusivos)}")

        # Para cada poço exclusivo deste IUPI
        for poco in tqdm(pocos_exclusivos, desc=f"Poços de {iupi}", leave=False):
            # Filtrar dados para este poço específico
            df_poco = df_iupi[df_iupi['Well'] == poco]

            if df_poco.empty:
                continue

            # Obter uma linha representativa para determinar os identificadores
            row = df_poco.iloc[0]
            row_identifiers = {col: row[col] for col in identifiers if col in row.index}

            # Determinar os multiplicadores para este poço
            multipliers = default_multipliers.copy()

            # Verificar regras
            for rule in rules:
                if verificar_condicoes(rule['conditions'], row_identifiers):
                    atualizar_multipliers(multipliers, rule['multipliers'])
                    break

            # Armazenar os multiplicadores para este poço
            multiplicadores_por_poco[poco] = multipliers.copy()

    # Aplicar os multiplicadores a todos os registros do DataFrame
    print("Aplicando multiplicadores aos registros...")

    total_linhas = len(df)
    barra_format = "{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]"

    with tqdm(total=total_linhas, desc="Processando linhas", bar_format=barra_format) as pbar:
        for index, row in df.iterrows():
            poco = row['Well']

            # Se o poço tem multiplicadores definidos
            if poco in multiplicadores_por_poco:
                multipliers = multiplicadores_por_poco[poco]

                # Aplicar transição progressiva
                try:
                    data = pd.to_datetime(row['Date'])
                    if data < transition_start:
                        fator = 0.0
                    elif data > transition_end:
                        fator = 1.0
                    else:
                        dias_totais = (transition_end - transition_start).days
                        dias_decorridos = (data - transition_start).days
                        fator = min(max(dias_decorridos / dias_totais, 0.0), 1.0)

                    # Aplicar interpolação linear apenas se houver multiplicador diferente de 1
                    for col in value_columns:
                        if col in row.index:  # Verificar se a coluna existe nesta linha
                            base = multipliers[col]
                            if base != 1.0:
                                multiplicador_final = 1 + (base - 1) * fator
                                df.at[index, col] *= multiplicador_final

                except Exception as e:
                    print(f"\nAviso: Erro ao processar data na linha {index}: {str(e)}")

            pbar.update(1)

    return df

@log_execution
def verificar_condicoes(conditions, row_identifiers):
    """Verifica se todas as condições são atendidas"""
    for cond_col, cond_val in conditions.items():
        if row_identifiers.get(cond_col) != cond_val:
            return False
    return True

@log_execution
def atualizar_multipliers(multipliers, new_multipliers):
    """Atualiza os multiplicadores com novos valores"""
    for col, val in new_multipliers.items():
        multipliers[col] = val

@log_execution
def salvar_resultado(df, arquivo_saida, df_processado=None):
    """Salva o DataFrame processado em um arquivo Excel"""
    if df_processado is not None:
         arquivo_saida_new = os.path.splitext(arquivo_saida)[0] + ".new.xlsx"
         df_processado.to_excel(arquivo_saida_new, index=False)
    df.to_excel(arquivo_saida, index=False)

@log_execution
def main():
    input_file  = r"L:\res\campos\jubarte\er\er03\PN\2630\C1\P90\PN2630C1_P50baseP10P90_novoGL_CorrCO140_BAZ1.xlsx"

    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M")
    output_file = os.path.splitext(input_file)[0] + f".output.{timestamp}.xlsx"

    print(f"Iniciando processamento do arquivo: {input_file}")
    processar_dados_planilha(input_file, output_file)
    print(f"\nProcessamento concluído! Arquivo salvo em: {output_file}")

if __name__ == "__main__":
    main()