# ensemble_report.py
# Usage:
# python ensemble_report.py /path/to/your/directory
# python ensemble_report.py /path/to/your/directory --initial_positions_path /path/to/your/excel/file.xlsx

import sys
import os
import glob
import re
import pandas as pd
import numpy as np
from scipy import stats
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from yattag import Doc, indent
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment
from datetime import datetime
import copy
import pickle
import xarray as xr
import math
import argparse
import time
from openpyxl import load_workbook
from Auxiliares.modules.logging_module import log_execution

# import debugpy
# # Start the debug server and wait for VSCode to attach
# debugpy.listen(("0.0.0.0", 5678))
# print("Waiting for debugger to attach...")
# debugpy.wait_for_client()  # Optional: Remove if you don't want to pause until the debugger connects
# # You should see the message Waiting for debugger to attach...
# # In VSCode, select the Python: Attach to Remote configuration.
# # Click Start Debugging (or press F5).
# # Once attached, you can set breakpoints and debug your script.

# ======================
# METADADOS DO SISTEMA
# ======================
__version__ = "1.0"
__release_date__ = "2025-06-30"
__author__ = "Iury Zottele Medeiros"
__contact__ = "<EMAIL>"

# ======================
# STATUS DE DESENVOLVIMENTO
# ======================
__status__ = {
    'implementado': [
r"""Geração de relatórios HTML para análise de ensemble
""",
r"""Processamento de arquivos .data e .wcoord
""",
r"""Seleção de simulações representativas (P10, P50, P90)
""",
r"""Gráficos de distribuição, scatter plots e séries temporais
""",
r"""Mapas de propriedades de reservatório (hphiso)
""",
    ],
    'em_desenvolvimento': [
r"""A funcao subtract_dataframes precisa ser corrigida.
    Quando a coluna conter 'On-tme Fraction' ao inves de pegarmos os valores de df_proj_, devemos pegar os valores de df_proj substituindo os valores vazios pelos valores em df_base.
""",
    ],
    'planejado': [
r"""Suporte a visualização 3D interativa
""",
r"""Análise de sensibilidade automatizada
""",
    ]
}

# ======================
# INSTRUCOES DE USO
# ======================
__usage__ = r"""
COMO USAR:
    python ensemble_report.py [OPCOES] <diretorio>

OPCOES:
    --version              Mostra versao e sai
    --docs                 Exibe documentacao completa
    --status               Mostra status de implementacao
    --initial_positions_path  Caminho para arquivo Excel com posicoes iniciais

EXEMPLOS:
  # Processamento normal:
  python ensemble_report.py /caminho/para/diretorio

  # Especificar arquivo de posições iniciais:
  python ensemble_report.py /caminho/para/diretorio --initial_positions_path /caminho/para/posicoes.xlsx

  # Verificar documentação:
  python ensemble_report.py --docs

  # Verificar status:
  python ensemble_report.py --status
"""

# ======================
# FUNCOES DE EXIBICAO
# ======================
@log_execution
def show_version():
    print(f"\nENSEMBLE REPORT v{__version__}")
    print(f"Data de lançamento: {__release_date__}")
    print(f"Autor: {__author__} | Contato: {__contact__}")

@log_execution
def show_usage():
    print(__usage__)

@log_execution
def show_status():
    print("\nSTATUS DE DESENVOLVIMENTO:")
    print("\n✅ IMPLEMENTADO:")
    for item in __status__["implementado"]:
        print(f"  • {item}")

    print("\n🚧 EM DESENVOLVIMENTO:")
    for item in __status__["em_desenvolvimento"]:
        print(f"  • {item}")

    print("\n📅 PLANEJADO:")
    for item in __status__["planejado"]:
        print(f"  • {item}")

@log_execution
def show_full_docs():
    show_version()
    print("\n" + "="*50)
    show_usage()
    print("\n" + "="*50)
    show_status()

@log_execution
def documentacao():
    # Controle de argumentos
    if "--version" in sys.argv:
        show_version()
        sys.exit(0)

    if "--docs" in sys.argv:
        show_full_docs()
        sys.exit(0)

    if "--status" in sys.argv:
        show_status()
        sys.exit(0)

    if "--help" in sys.argv or "-h" in sys.argv:
        show_usage()
        sys.exit(0)

# ======================
# FUNCOES DE PROCESSAMENTO
# ======================
class TimeVar:
    @log_execution
    def __init__(self, variable, unit):
        self.variable = variable
        self.unit = unit

    @log_execution
    def __str__(self):
        return f"(variable={self.variable}, unit={self.unit})"

    @log_execution
    def __repr__(self):
        return self.__str__()

class TimeEntity:
    @log_execution
    def __init__(self, entity, *args):
        self.entity = entity
        if len(args) == 1 and isinstance(args[0], TimeVar):
            self.time_var = args[0]
        elif len(args) == 2:
            self.time_var = TimeVar(args[0], args[1])
        else:
            raise ValueError("Invalid arguments. Use either (entity, TimeVar) or (entity, variable, unit)")

    @property
    @log_execution
    def variable(self):
        return self.time_var.variable

    @property
    @log_execution
    def unit(self):
        return self.time_var.unit

    @log_execution
    def __str__(self):
        return f"(entity={self.entity}, {str(self.time_var)[1:-1]})"

    @log_execution
    def __repr__(self):
        return self.__str__()

# ======================
# Utility Functions
# ======================

@log_execution
def read_single_data_file(file_path: str) -> pd.DataFrame:
    """
    Reads a single .data file and returns a DataFrame.

    Parameters:
        file_path (str): Path to the .data file.

    Returns:
        df (DataFrame): The parsed DataFrame, or None if the file cannot be read.
    """
    if not os.path.isfile(file_path):
        print(f"[read_single_data_file] File does not exist: {file_path}")
        return None

    try:
        # Example read: adjust 'sep', 'header', 'index_col', 'parse_dates' as needed
        df = pd.read_csv(file_path, sep='\t', header=[0, 1, 2], index_col=0, parse_dates=True)

        # 1) Cria uma máscara que identifica quais colunas NÃO têm "Unnamed:" no nível 0
        mask = ~df.columns.get_level_values(0).str.startswith("Unnamed:")
        # 2) Aplica a máscara nas colunas
        df = df.loc[:, mask]

        # # Filtra as colunas em que o nível 2 for "On-time Fraction"
        # cols = df.columns[df.columns.get_level_values(1) == "On-time Fraction"]
        # # Imprime as colunas encontradas
        # print(cols[0])

        # df.columns = df.columns.set_levels(
        #     df.columns.levels[2].str.replace(r'^Unnamed:.*', 'fraction', regex=True),
        #     level=2
        # )
        # print(cols[0])
        # input()
        return df
    except Exception as e:
        print(f"[read_single_data_file] Error reading {file_path}: {e}")
        return None

@log_execution
def read_data_files(directory_path):
    """
    Reads all .data files in the specified directory and returns a dictionary of DataFrames.

    Parameters:
        directory_path (str): Path to the directory containing .data files.

    Returns:
        dataframes (dict): Dictionary where keys are simulation numbers and values are DataFrames.
    """
    dataframes = {}
    pattern = os.path.join(directory_path, "*.data")
    files = glob.glob(pattern)

    # for file_path in tqdm(files, desc=f"Reading files from {os.path.basename(directory_path)}", unit="file"):
    for file_path in files:
        match = re.search(r'_(\d{3})\.data$', file_path)
        if match:
            file_number = match.group(1)
            df = read_single_data_file(file_path=file_path)
            dataframes[file_number] = df

    return dataframes

@log_execution
def read_wcoord_file(path):
    """
    Reads the .wcoord file from the specified path.

    Parameters:
        path (str): Path to the directory containing the .wcoord file,
                    or path to a file (extension will be changed to .wcoord).

    Returns:
        wcoord_df (DataFrame): DataFrame containing well coordinates.
    """
    if os.path.isdir(path):
        # Se for um diretório, mantenha o comportamento original
        wcoord_files = glob.glob(os.path.join(path, "*.wcoord"))
        if not wcoord_files:
            print(f"No .wcoord file found in {path}")
            return None
        file_to_read = wcoord_files[0]
    else:
        # Se for um arquivo, mude a extensão para .wcoord
        file_to_read = os.path.splitext(path)[0] + ".wcoord"

    # Tenta ler o arquivo
    try:
        return pd.read_csv(file_to_read)
    except FileNotFoundError:
        print(f"File not found: {file_to_read}")
        return None
    except pd.errors.EmptyDataError:
        print(f"File is empty: {file_to_read}")
        return None
    except Exception as e:
        print(f"Error reading file {file_to_read}: {str(e)}")
        return None

@log_execution
def read_dist_file(directory_path):
    """
    Reads the .dist file in the specified directory.

    Parameters:
        directory_path (str): Path to the directory containing the .dist file.

    Returns:
        dist_df (DataFrame): DataFrame containing distances.
    """
    dist_files = glob.glob(os.path.join(directory_path, "*.dist"))
    if not dist_files:
        print(f"No .dist file found in {directory_path}")
        return None
    return pd.read_csv(dist_files[0], index_col=0)

@log_execution
def get_new_well_coordinates(directory_path, new_well_name):
    """
    Retrieves the coordinates of the new well from the .wcoord file.

    Parameters:
        directory_path (str): Path to the directory containing the .wcoord file.
        new_well_name (str): Name of the new well.

    Returns:
        x_coord (float): X coordinate of the new well.
        y_coord (float): Y coordinate of the new well.
    """
    wcoord_df = read_wcoord_file(directory_path)
    if wcoord_df is None:
        return None, None

    new_well_row = wcoord_df[wcoord_df['Poço'] == new_well_name]
    if new_well_row.empty:
        print(f"Well {new_well_name} not found in the .wcoord file")
        return None, None

    x_coord = new_well_row['x'].values[0]
    y_coord = new_well_row['y'].values[0]

    return x_coord, y_coord

@log_execution
def get_well_production(dataframes, well_name):
    """
    Retrieves production data (Np and Gp) for a specific well from multiple simulations.

    Parameters:
        dataframes (dict): Dictionary of DataFrames from simulations.
        well_name (str): Name of the well.

    Returns:
        Np_values (list): List of cumulative oil production values.
        Gp_values (list): List of cumulative gas production values.
    """
    Np_values = []
    Gp_values = []
    for df in dataframes.values():
        Np = df[(well_name, "Np", "m3")].iloc[-1] if (well_name, "Np", "m3") in df.columns else 0
        Gp = df[(well_name, "Gp", "m3")].iloc[-1] if (well_name, "Gp", "m3") in df.columns else 0
        Np_values.append(Np)
        Gp_values.append(Gp)
    return Np_values, Gp_values

@log_execution
def calculate_differences(dataframes, base_dataframes, wells):
    """
    Calculates the differences in Np and Gp between current and base simulations for each well.

    Parameters:
        dataframes (dict): Dictionary of current simulation DataFrames.
        base_dataframes (dict): Dictionary of base simulation DataFrames.
        wells (list): List of well names.

    Returns:
        np_diff (list): List of Np differences for each well.
        gp_diff (list): List of Gp differences for each well.
    """
    np_diff = []
    gp_diff = []
    for well in wells:
        np_current, gp_current = get_well_production(dataframes, well)
        np_base, gp_base = get_well_production(base_dataframes, well)

        # Ensure both lists are the same length
        max_len = max(len(np_current), len(np_base))
        np_current += [0] * (max_len - len(np_current))
        np_base += [0] * (max_len - len(np_base))
        gp_current += [0] * (max_len - len(gp_current))
        gp_base += [0] * (max_len - len(gp_base))

        np_difference = [c - b for c, b in zip(np_current, np_base)]
        gp_difference = [(c / 1e3) - (b / 1e3) for c, b in zip(gp_current, gp_base)]

        np_diff.append(np_difference)
        gp_diff.append(gp_difference)
    return np_diff, gp_diff

@log_execution
def get_percentile_values(values, percentile=50):
    """
    Calculates the specified percentile value for each list of values.

    Parameters:
        values (list of lists): List containing lists of values.
        percentile (int): Percentile to calculate.

    Returns:
        percentile_values (list): List of percentile values.
    """
    return [np.percentile(well_values, 100 - percentile) if well_values else 0 for well_values in values]

# ======================
# Plotting Functions
# ======================

@log_execution
def create_distribution_plot(scatter_info, var_name):
    """
    'var_name' é algo como 'VOIP' ou 'Npe*'.
    Precisamos achar qual item em scatter_info['variables'] tem 'name' == var_name.
    """
    import plotly.graph_objects as go
    # 1) Achar o index do var
    var_index = None
    for i, vdict in enumerate(scatter_info['variables']):
        if vdict['name'] == var_name:
            var_index = i
            break
    if var_index is None:
        raise ValueError(f"Variable '{var_name}' not found in scatter_info")

    var_dict = scatter_info['variables'][var_index]  # ex: { 'values': {...}, 'name':..., ...}
    # Pegar a array de valores
    values = list(var_dict['values'].values())

    # Montar hist
    fig = go.Figure()
    fig.add_trace(go.Histogram(x=values, name=f"{var_name} Distribution",
                               marker_color='lightblue', opacity=0.7))

    # max_y
    max_y = 0
    if fig.data[0].y is not None:
        max_y = max(fig.data[0].y) if len(fig.data[0].y)>0 else 1

    # Agora, se existirem 'regions' no scatter_info e a index var_index for válido:
    if 'regions' in scatter_info and var_index < len(scatter_info['regions']):
        (p10_min, p10_max, p50_min, p50_max, p90_min, p90_max) = scatter_info['regions'][var_index]
        # Adicionar linhas verticais
        # p10 => red, p50 => green, p90 => red
        # ou algo assim:
        lines = [
          (p10_min, 'red', 'P10_min'),
          (p10_max, 'red', 'P10_max'),
          (p50_min, 'green', 'P50_min'),
          (p50_max, 'green', 'P50_max'),
          (p90_min, 'red', 'P90_min'),
          (p90_max, 'red', 'P90_max'),
        ]
        for xval, color, nm in lines:
            fig.add_shape(type="line", x0=xval, x1=xval, y0=0, y1=max_y,
                          line=dict(color=color, width=2, dash='dash'),
                          name=nm)

    fig.update_layout(
        title_text=f"{var_name} Distribution",
        xaxis_title_text=f"{var_dict['name']} ({var_dict['unit']})",
        yaxis_title_text="Count",
        bargap=0.1,
        showlegend=False
    )
    return fig

@log_execution
def create_scatter_with_marginals_plot(scatter_info):
    """
    Supondo que scatter_info['variables'] tem EXATAMENTE 2 variáveis: var1 e var2.
    E scatter_info['colors'] é {sim -> 'blue'/'green'/'red'/'darkgrey'}.
    """
    import numpy as np
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots

    if len(scatter_info['variables']) != 2:
        raise ValueError("create_scatter_with_marginals_plot only works with exactly 2 variables")

    var1 = scatter_info['variables'][0]
    var2 = scatter_info['variables'][1]

    # Montar a lista de simulações => sorted do union de var1 e var2
    sims_union = set(var1['values'].keys()) | set(var2['values'].keys())
    sims_sorted = sorted(sims_union)

    x_list = []
    y_list = []
    color_list = []
    sim_text = []

    for sim in sims_sorted:
        x_val = var1['values'].get(sim, None)
        y_val = var2['values'].get(sim, None)
        if x_val is None or y_val is None:
            # Se quiser pular, pode
            # ou tratar None como 0
            # Aqui resolvo pular:
            continue
        c = scatter_info['colors'].get(sim, 'darkgrey')  # se não existir, assume 'darkgrey'
        x_list.append(x_val)
        y_list.append(y_val)
        color_list.append(c)
        sim_text.append(sim)  # "Simulation 001"

    fig = make_subplots(
        rows=2, cols=2,
        column_widths=[0.7, 0.3],
        row_heights=[0.3, 0.7],
        specs=[
            [{"type": "histogram"}, None],
            [{"type": "scatter"}, {"type": "histogram"}]
        ],
        shared_xaxes='columns',
        shared_yaxes='rows',
        horizontal_spacing=0.02,
        vertical_spacing=0.02
    )

    # Scatter principal
    fig.add_trace(
        go.Scatter(
            x=x_list,
            y=y_list,
            mode='markers',
            marker=dict(color=color_list, opacity=0.7),
            name=f'{var2["name"]} vs {var1["name"]}',
            text=[f"Simulation {s}" for s in sim_text],
            hovertemplate=(
                "Simulation: %{text}<br>"
                f"{var1['name']}: {{x}}<br>"
                f"{var2['name']}: {{y}}<extra></extra>"
            )
        ),
        row=2, col=1
    )

    # Histograma x
    fig.add_trace(
        go.Histogram(x=x_list, nbinsx=20,
                     marker=dict(color='blue', opacity=0.7),
                     showlegend=False),
        row=1, col=1
    )

    # Histograma y (vertical)
    fig.add_trace(
        go.Histogram(y=y_list, nbinsy=20,
                     marker=dict(color='blue', opacity=0.7),
                     showlegend=False),
        row=2, col=2
    )

    fig.update_xaxes(title_text=f'{var1["name"]} ({var1["unit"]})', row=2, col=1)
    fig.update_yaxes(title_text=f'{var2["name"]} ({var2["unit"]})', row=2, col=1)

    fig.update_layout(
        title=f'{var2["name"]} vs {var1["name"]}',
        bargap=0.05,
        hovermode='closest',
        showlegend=False
    )

    return fig

@log_execution
def calculate_scatter_info(*vars_dicts, initial_dpercentile=0.5, max_dpercentile=10):
    """
    Recebe um número variável de dicionários no formato:
        {
          'values': { '001': 1234.0, '002': 5678.0, ... },
          'entity': ...,
          'name': ...,
          'unit': ...,
          'offset_str': ... (opcional)
        }
    Tenta achar uma dpercentile que resulte em >=1 sim na P10, P50 e P90.
    Se não achar, vai aumentando dpercentile até 'max_dpercentile'.
    Retorna um scatter_info no formato:
        {
          'dpercentile': X,
          'counts': { 'P10':..., 'P50':..., 'P90':..., 'other':... },
          'colors': { 'sim_number': 'blue'/'green'/'red'/'darkgrey', ... },
          'regions': [ (p10_min,p10_max,p50_min,p50_max,p90_min,p90_max), ...],
          'variables': [ o mesmo list(vars_dicts) ],
        }
    """
    dpercentile = initial_dpercentile
    # Precisamos de um loop que tenta:
    while True:
        # define_regions_and_count retorna: counts, key_to_color, regions
        counts, key_to_color, regions = define_regions_and_count(dpercentile, *vars_dicts)
        # checar se counts['P10']>0, etc
        if (counts['P10']>0 and counts['P50']>0 and counts['P90']>0):
            break
        dpercentile += 0.1
        if dpercentile > max_dpercentile:
            if len(vars_dicts) > 1:
                print(f"Warning: Could not find regions with at least one point each. Trying with {[f"({var["entity"]},{var['name']},{var["unit"]},{var['offset_str']})" for var in vars_dicts[:-1]]}.")
                # Chama recursivamente a função sem o último argumento
                return calculate_scatter_info(*vars_dicts[:-1], initial_dpercentile=initial_dpercentile, max_dpercentile=max_dpercentile)
            else:
                print("Warning: could not find non-empty P10/P50/P90 using the given variables.")
                break

    # Montar a estrutura final
    scatter_info = {
        'dpercentile': dpercentile,
        'counts': counts,
        'colors': key_to_color,    # dict { sim -> color }
        'regions': regions,        # lista de tuplas p10/p50/p90 p/ cada var
        'variables': list(vars_dicts)  # guardamos a lista
    }

    # Também podemos criar a lista "simulations", com sublistas P10, P50, P90 etc
    # extra: invertendo 'colors'
    sims_p10 = []
    sims_p50 = []
    sims_p90 = []
    sims_other = []
    for sim, color in key_to_color.items():
        if color == 'blue':
            sims_p10.append(sim)
        elif color == 'green':
            sims_p50.append(sim)
        elif color == 'red':
            sims_p90.append(sim)
        else:
            sims_other.append(sim)

    scatter_info['simulations'] = {
        'P10': sorted(sims_p10),
        'P50': sorted(sims_p50),
        'P90': sorted(sims_p90),
        'other': sorted(sims_other),
        'all': sorted(key_to_color.keys())
    }

    return scatter_info

@log_execution
def define_regions_and_count(dpercentile, *args):
    """
    Ajusta-se para que cada 'arg' seja um dicionário do tipo:
      {
        'values': { '001': valor, '002': valor, ... },
        'entity': str,
        'name': str,
        'unit': str,
        'offset_str': ... (opcional)
      }

    Precisamos:
      1) Achar a interseção das chaves de 'values' em todos os args, p.ex:
           common_keys = set(args[0]['values'].keys()) ∩ set(args[1]['values'].keys()) ∩ ...
      2) Calcular array de dados SOMENTE para as common_keys em cada arg.
      3) Calcular region (p10/p50/p90 ± dpercentile) para cada array.
      4) Para cada common_key (na mesma ordem), checar se esse ponto está
         simultaneamente na faixa p10, p50 ou p90 de TODAS as variáveis.
         - Se sim, colorimos (blue, green, red).
         - Senão, “other”.

    5) Para as simulações (chaves) que não estiverem em common_keys,
       mas aparecerem em **pelo menos** um dos args, também definimos
       cor = 'darkgrey'.
    """
    # 1) Montar a lista de sets de chaves
    list_of_sets = []
    all_keys_set = set()  # unindo todas as simulações que aparecem em qualquer arg
    for arg in args:
        keys_arg = set(arg['values'].keys())
        list_of_sets.append(keys_arg)
        all_keys_set.update(keys_arg)

    # 2) Interseção de todas as chaves
    common_keys = set.intersection(*list_of_sets)

    # 3) Construir arrays (list_of_arrays), um para cada arg,
    #    contendo SOMENTE as chaves em common_keys (ordenadas).
    sorted_common_keys = sorted(common_keys)
    list_of_arrays = []
    for arg in args:
        arr = [arg['values'][k] for k in sorted_common_keys]
        list_of_arrays.append(np.array(arr, dtype=float))

    # 4) Definir regiões (p10/p50/p90) para cada array
    #    Precisamos de algo tipo:
    #      (p10_min, p10_max, p50_min, p50_max, p90_min, p90_max)
    #    baseado na “faixa +/- dpercentile” do percentil nominal
    #    Ex: p10 = percentil(90); p50 = percentil(50); p90 = percentil(10)
    @log_execution
    def define_percentile_region(data, nominal_percentile, dpercentile):
        """
        Se nominal_percentile=10 => real_percentile = 100 - 10 = 90
          mas subimos e descemos dpercentile => [90-dp, 90+dp].
        """
        lower_p = 100 - nominal_percentile - dpercentile
        upper_p = 100 - nominal_percentile + dpercentile
        # clamp to [0, 100]
        lower_p = max(0, min(100, lower_p))
        upper_p = max(0, min(100, upper_p))

        p_min = np.percentile(data, lower_p)
        p_max = np.percentile(data, upper_p)
        return (p_min, p_max)

    regions = []  # para guardar (p10_min, p10_max, p50_min, p50_max, p90_min, p90_max) de cada arg
    for arr in list_of_arrays:
        p10_min, p10_max = define_percentile_region(arr, 10, dpercentile)
        p50_min, p50_max = define_percentile_region(arr, 50, dpercentile)
        p90_min, p90_max = define_percentile_region(arr, 90, dpercentile)
        regions.append((p10_min, p10_max, p50_min, p50_max, p90_min, p90_max))

    # 5) Fazer a classificação. Precisamos:
    #    - 'colors' será um dicionário { key -> 'blue'/'green'/... }
    #    - 'counts' = { 'P10': 0, 'P50': 0, 'P90': 0, 'other': 0 }
    #    - Para cada common_key, a gente vê se “TODOS” arrays satisfazem
    #      p50_min <= arr[i] <= p50_max. Se sim => 'green'
    #      senão, p10?
    #      senão, p90?
    #      senão, 'other'.
    counts = {'P10': 0, 'P50': 0, 'P90': 0, 'other': 0}
    key_to_color = {}

    # Montar arrays = [arr0[i], arr1[i], ...] para cada i
    for idx, ck in enumerate(sorted_common_keys):
        # arr[k][idx] = valor da k-ésima variável
        is_p10 = True
        is_p50 = True
        is_p90 = True
        for var_idx, arr in enumerate(list_of_arrays):
            (p10_min, p10_max, p50_min, p50_max, p90_min, p90_max) = regions[var_idx]
            val = arr[idx]
            # se val NÃO estiver em [p10_min, p10_max], is_p10=False
            if not (p10_min <= val <= p10_max):
                is_p10 = False
            if not (p50_min <= val <= p50_max):
                is_p50 = False
            if not (p90_min <= val <= p90_max):
                is_p90 = False

        if is_p50:
            key_to_color[ck] = 'green'
            counts['P50'] += 1
        elif is_p10:
            key_to_color[ck] = 'blue'
            counts['P10'] += 1
        elif is_p90:
            key_to_color[ck] = 'red'
            counts['P90'] += 1
        else:
            key_to_color[ck] = 'darkgrey'
            counts['other'] += 1

    # 6) Para as chaves que NÃO estão em common_keys mas existem em all_keys_set,
    #    definir cor = 'darkgrey', e incrementamos 'other' no counts.
    leftover_keys = all_keys_set - common_keys
    for k in leftover_keys:
        key_to_color[k] = 'darkgrey'
        counts['other'] += 1

    return counts, key_to_color, regions

@log_execution
def set_group_members(scatter_info, group, new_members):
    """
    Force an entire group (P10, P50, P90, or 'other') in scatter_info
    to be exactly the set of new_members passed in. Any simulations
    currently in that group but *not* in new_members are removed, and
    if any new_members appear in other groups, they are removed from
    those other groups.

    Parameters
    ----------
    scatter_info : dict
        The dictionary returned by calculate_scatter_info, with a structure like:
            {
                'dpercentile': float,
                'counts': {'P10': int, 'P50': int, 'P90': int, 'other': int},
                'simulations': {
                    'P10': [...],
                    'P50': [...],
                    'P90': [...],
                    'other': [...],
                    'all': [...]
                },
                'colors': [ ... ],  # same length as 'simulations']['all']
                'regions': [...],
                'variables': [...]
            }
    group : str
        Name of the group to override. One of "P10", "P50", "P90", "other".
    new_members : list
        List of simulation names/IDs (e.g. ["068", "135"]) to be the entire membership
        of the specified group.

    Returns
    -------
    dict
        The updated scatter_info (modified in-place, but also returned for convenience).
    """
    # How to use
    # scatter_info_field = set_group_members(scatter_info_field, "P90", ["068", "135"])
    # then "P90" becomes exactly ["068", "135"] (and only those). All other simulations that were in "P90" are removed, and if "068" or "135" were in other groups, they are removed from those groups.

    valid_groups = ['P10', 'P50', 'P90', 'other']
    if group not in valid_groups:
        raise ValueError(f"group must be one of {valid_groups}, got '{group}'.")

    # Convert to set for easy membership checks
    new_members_set = set(new_members)

    # 1. Remove new_members from *any* group they might already be in
    for grp in valid_groups:
        for sim in list(scatter_info['simulations'][grp]):  # copy the list to avoid mutation problems
            if sim in new_members_set and grp != group:
                scatter_info['simulations'][grp].remove(sim)

    # 2. Clear the existing membership of the target group
    scatter_info['simulations'][group].clear()

    # 3. Add each simulation in new_members to the target group
    #    But only if it exists in "all" so we don't create extraneous entries
    for sim in new_members:
        if sim not in scatter_info['simulations']['all']:
            # If a sim isn't in 'all', we could skip or raise an error. Here we skip with a warning.
            print(f"Warning: Simulation {sim} not found in scatter_info['simulations']['all']. Skipping.")
            continue
        scatter_info['simulations'][group].append(sim)

    # 4. Update colors for each sim in 'all', based on its new group
    color_map = {
        'P10': 'blue',
        'P50': 'green',
        'P90': 'red',
        'other': 'darkgrey'
    }
    # Build a quick reverse-lookup: which group is each sim in now?
    sim_to_group = {}
    for grp in valid_groups:
        for sim in scatter_info['simulations'][grp]:
            sim_to_group[sim] = grp

    # Now reassign colors based on the final group membership
    for idx, sim in enumerate(scatter_info['simulations']['all']):
        grp = sim_to_group.get(sim, 'other')  # default to 'other' if not found
        scatter_info['colors'][idx] = color_map[grp]

    # 5. Recompute counts
    for grp in valid_groups:
        scatter_info['counts'][grp] = len(scatter_info['simulations'][grp])

    return scatter_info

@log_execution
def extended_set_group_members(scatter_info, group, new_members):
    valid_groups = ['P10', 'P50', 'P90', 'other', 'user']
    if group not in valid_groups:
        raise ValueError(f"group must be one of {valid_groups}, got '{group}'.")

    # Se 'user' não existe em scatter_info['simulations'], criamos
    if 'user' not in scatter_info['simulations']:
        scatter_info['simulations']['user'] = []

    # Convert to set for easy membership checks
    new_members_set = set(new_members)

    # 1. Remove new_members from any group
    for grp in valid_groups:
        for sim in list(scatter_info['simulations'][grp]):  # copy the list to avoid mutation
            if sim in new_members_set and grp != group:
                scatter_info['simulations'][grp].remove(sim)

    # 2. Clear the existing membership of the target group
    scatter_info['simulations'][group].clear()

    # 3. Add each simulation in new_members
    for sim in new_members:
        if sim not in scatter_info['simulations']['all']:
            print(f"Warning: Simulation {sim} not found in scatter_info['simulations']['all']. Skipping.")
            continue
        scatter_info['simulations'][group].append(sim)

    # 4. Rebuild `colors`
    color_map = {
        'P10': 'blue',
        'P50': 'green',
        'P90': 'red',
        'other': 'darkgrey',
        'user': 'orange',  # color for user
    }
    sim_to_group = {}
    for grp in valid_groups:
        for sim in scatter_info['simulations'][grp]:
            sim_to_group[sim] = grp

    # Reassign colors based on final group membership
    for sim in scatter_info['simulations']['all']:
        grp = sim_to_group.get(sim, 'other')
        scatter_info['colors'][sim] = color_map[grp]

    # 5. Recompute counts
    for grp in valid_groups:
        scatter_info['counts'][grp] = len(scatter_info['simulations'][grp])

    return scatter_info

# Overwrite the imported set_group_members function with the extended one
set_group_members = extended_set_group_members

@log_execution
def define_regions_and_count_3d(x, y, z, dpercentile):
    """
    @log_execution
    Defines regions in 3D space based on percentiles and assigns points to regions.

    Parameters:
        x, y, z (array-like): Arrays of data points.
        dpercentile (float): Delta for percentile calculation.

    Returns:
        counts (dict): Counts of points in each region.
        colors (list): List of colors assigned to each point.
        p10_region_3d, p50_region_3d, p90_region_3d (tuples): Percentile regions for P10, P50, P90.
    """
    @log_execution
    def define_percentile_region(data, lower_percentile, upper_percentile):
        min_val = np.percentile(data, lower_percentile)
        max_val = np.percentile(data, upper_percentile)
        return min_val, max_val

    # Define regions for x
    x_P10_min, x_P10_max = define_percentile_region(x, 100 - (10 + dpercentile), 100 - (10 - dpercentile))
    x_P50_min, x_P50_max = define_percentile_region(x, 100 - (50 + dpercentile), 100 - (50 - dpercentile))
    x_P90_min, x_P90_max = define_percentile_region(x, 100 - (90 + dpercentile), 100 - (90 - dpercentile))

    # Define regions for y
    y_P10_min, y_P10_max = define_percentile_region(y, 100 - (10 + dpercentile), 100 - (10 - dpercentile))
    y_P50_min, y_P50_max = define_percentile_region(y, 100 - (50 + dpercentile), 100 - (50 - dpercentile))
    y_P90_min, y_P90_max = define_percentile_region(y, 100 - (90 + dpercentile), 100 - (90 - dpercentile))

    # Define regions for z
    z_P10_min, z_P10_max = define_percentile_region(z, 100 - (10 + dpercentile), 100 - (10 - dpercentile))
    z_P50_min, z_P50_max = define_percentile_region(z, 100 - (50 + dpercentile), 100 - (50 - dpercentile))
    z_P90_min, z_P90_max = define_percentile_region(z, 100 - (90 + dpercentile), 100 - (90 - dpercentile))

    counts = {'P10': 0, 'P50': 0, 'P90': 0, 'other': 0}
    colors = ['grey'] * len(x)
    color_mapping = {'P10': 'blue', 'P50': 'green', 'P90': 'red'}

    for i in range(len(x)):
        # Check if point is within P10 region in all variables
        if (x_P10_min <= x[i] <= x_P10_max) and (y_P10_min <= y[i] <= y_P10_max) and (z_P10_min <= z[i] <= z_P10_max):
            colors[i] = color_mapping['P10']
            counts['P10'] += 1
        elif (x_P50_min <= x[i] <= x_P50_max) and (y_P50_min <= y[i] <= y_P50_max) and (z_P50_min <= z[i] <= z_P50_max):
            colors[i] = color_mapping['P50']
            counts['P50'] += 1
        elif (x_P90_min <= x[i] <= x_P90_max) and (y_P90_min <= y[i] <= y_P90_max) and (z_P90_min <= z[i] <= z_P90_max):
            colors[i] = color_mapping['P90']
            counts['P90'] += 1
        else:
            colors[i] = 'grey'
            counts['other'] += 1

    return counts, colors, \
           (x_P10_min, x_P10_max, y_P10_min, y_P10_max, z_P10_min, z_P10_max), \
           (x_P50_min, x_P50_max, y_P50_min, y_P50_max, z_P50_min, z_P50_max), \
           (x_P90_min, x_P90_max, y_P90_min, y_P90_max, z_P90_min, z_P90_max)

@log_execution
def get_production_difference_for_simulation(sim_number, dataframes, base_dataframes, wells):
    """
    Calculates the production differences for a specific simulation.

    Parameters:
        sim_number (str): Simulation number.
        dataframes (dict): Dictionary of current simulation DataFrames.
        base_dataframes (dict): Dictionary of base simulation DataFrames.
        wells (list): List of well names.

    Returns:
        np_diff (list): List of Np differences per well.
        gp_diff (list): List of Gp differences per well.
    """
    df_current = dataframes[sim_number]
    df_base = base_dataframes[sim_number]
    np_diff = []
    gp_diff = []
    for well in wells:
        Np_current = df_current[(well, "Np", "m3")].iloc[-1] if (well, "Np", "m3") in df_current.columns else 0
        Np_base = df_base[(well, "Np", "m3")].iloc[-1] if (well, "Np", "m3") in df_base.columns else 0
        Gp_current = df_current[(well, "Gp", "m3")].iloc[-1] if (well, "Gp", "m3") in df_current.columns else 0
        Gp_base = df_base[(well, "Gp", "m3")].iloc[-1] if (well, "Gp", "m3") in df_base.columns else 0
        np_diff.append(Np_current - Np_base)
        gp_diff.append((Gp_current - Gp_base) / 1e3)  # Adjust units if necessary
    return np_diff, gp_diff

@log_execution
def get_last_difference_for_simulation(sim_number, dataframes, base_dataframes, wells, var: TimeVar):
    df_current = dataframes[sim_number]
    df_base = base_dataframes[sim_number]
    dvarf = []
    for well in wells:
        var_current_f = df_current[(well, var.variable, var.unit)].iloc[-1] if (well, var.variable, var.unit) in df_current.columns else 0
        var_base_f = df_base[(well, var.variable, var.unit)].iloc[-1] if (well, var.variable, var.unit) in df_base.columns else 0
        dvarf.append(var_current_f - var_base_f)
    return dvarf

@log_execution
def create_well_production_chart(wells, np_diff_p90, gp_diff_p90, np_diff_p50, gp_diff_p50, np_diff_p10, gp_diff_p10, distances):
    """
    Creates a bar chart showing production differences per well for P90, P50, P10 simulations.

    Parameters:
        wells (list): List of well names.
        np_diff_p90, gp_diff_p90: Production differences for P90 simulation.
        np_diff_p50, gp_diff_p50: Production differences for P50 simulation.
        np_diff_p10, gp_diff_p10: Production differences for P10 simulation.
        distances (list): List of distances to the new well.

    Returns:
        fig (Figure): Plotly Figure object.
    """
    # Adjust data as per requirements
    # Convert np_diff and gp_diff to Mm³ (divide by 1e6)
    # Compute ΔNpe for each case
    # Convert distances to km (divide by 1000)

    # Create DataFrame
    df = pd.DataFrame({
        'Well': wells,
        'Distance_km': np.array(distances) / 1000,  # Convert to km
        'np_diff_p90': np.array(np_diff_p90) / 1e6,
        'gp_diff_p90': np.array(gp_diff_p90) / 1e6,
        'np_diff_p50': np.array(np_diff_p50) / 1e6,
        'gp_diff_p50': np.array(gp_diff_p50) / 1e6,
        'np_diff_p10': np.array(np_diff_p10) / 1e6,
        'gp_diff_p10': np.array(gp_diff_p10) / 1e6
    })

    # Compute ΔNpe for each case
    df['npe_diff_p90'] = df['np_diff_p90'] + df['gp_diff_p90']
    df['npe_diff_p50'] = df['np_diff_p50'] + df['gp_diff_p50']
    df['npe_diff_p10'] = df['np_diff_p10'] + df['gp_diff_p10']

    # Format distances to have 2 decimal places
    df['Distance_km'] = df['Distance_km'].round(2)

    # Sort wells by distance
    df.sort_values('Distance_km', inplace=True)
    wells = df['Well'].tolist()

    # Assign x positions
    x_positions = np.arange(len(wells))
    width = 0.2  # Width for offsetting bars

    fig = go.Figure()

    # Prepare customdata as a 2D array for hovertemplate
    # customdata will be a list of lists: [Well, ΔNpe, ΔNp, ΔGp, Distance]
    # We need to prepare customdata for each case (P90, P50, P10)
    customdata_p90 = np.stack((
        df['Well'],
        df['npe_diff_p90'],
        df['np_diff_p90'],
        df['gp_diff_p90'],
        df['Distance_km']
    ), axis=-1)

    customdata_p50 = np.stack((
        df['Well'],
        df['npe_diff_p50'],
        df['np_diff_p50'],
        df['gp_diff_p50'],
        df['Distance_km']
    ), axis=-1)

    customdata_p10 = np.stack((
        df['Well'],
        df['npe_diff_p10'],
        df['np_diff_p10'],
        df['gp_diff_p10'],
        df['Distance_km']
    ), axis=-1)

    # Define hovertemplate
    hovertemplate = (
        "Poço: %{customdata[0]}<br>"
        "ΔNpe: %{customdata[1]:.3f} Mm³<br>"
        "ΔNp: %{customdata[2]:.3f} Mm³<br>"
        "ΔGp: %{customdata[3]:.3f} Mm³<br>"
        "Distância: %{customdata[4]:.2f} km<extra></extra>"
    )

    # Add P90 bars
    fig.add_trace(go.Bar(
        x=x_positions - width,
        y=df['np_diff_p90'],
        name=f'ΔNp P90',
        marker_color='green',
        width=0.2,
        offsetgroup='P90',
        hovertemplate=hovertemplate,
        customdata=customdata_p90
    ))
    fig.add_trace(go.Bar(
        x=x_positions - width,
        y=df['gp_diff_p90'],
        name=f'ΔGp P90',
        marker_color='red',
        width=0.2,
        base=df['np_diff_p90'],
        offsetgroup='P90',
        hovertemplate=hovertemplate,
        customdata=customdata_p90,
        showlegend=False
    ))

    # Add P50 bars
    fig.add_trace(go.Bar(
        x=x_positions,
        y=df['np_diff_p50'],
        name=f'ΔNp P50',
        marker_color='green',
        width=0.2,
        offsetgroup='P50',
        hovertemplate=hovertemplate,
        customdata=customdata_p50
    ))
    fig.add_trace(go.Bar(
        x=x_positions,
        y=df['gp_diff_p50'],
        name=f'ΔGp P50',
        marker_color='red',
        width=0.2,
        base=df['np_diff_p50'],
        offsetgroup='P50',
        hovertemplate=hovertemplate,
        customdata=customdata_p50,
        showlegend=False
    ))

    # Add P10 bars
    fig.add_trace(go.Bar(
        x=x_positions + width,
        y=df['np_diff_p10'],
        name=f'ΔNp P10',
        marker_color='green',
        width=0.2,
        offsetgroup='P10',
        hovertemplate=hovertemplate,
        customdata=customdata_p10
    ))
    fig.add_trace(go.Bar(
        x=x_positions + width,
        y=df['gp_diff_p10'],
        name=f'ΔGp P10',
        marker_color='red',
        width=0.2,
        base=df['np_diff_p10'],
        offsetgroup='P10',
        hovertemplate=hovertemplate,
        customdata=customdata_p10,
        showlegend=False
    ))

    # Update x-axis
    fig.update_layout(
        xaxis=dict(
            tickmode='array',
            tickvals=x_positions,
            ticktext=wells
        ),
        xaxis_title='Poço',
        yaxis_title=f'ΔProdução (Mm³)',
        barmode='group',
        title=f'ΔProdução por Poço (P90, P50, P10)',
        xaxis_tickangle=-45,
        showlegend=False  # This line removes the legend
    )

    return fig

@log_execution
def create_bubble_chart(wells, np_diff, gp_diff, wcoord_df):
    """
    Creates a bubble chart of all wells, where bubble size represents DNpe difference.

    Parameters:
        wells (list): List of all well names.
        np_diff (list): List of Np differences for all wells.
        gp_diff (list): List of Gp differences for all wells.
        wcoord_df (DataFrame): DataFrame containing well coordinates.

    Returns:
        fig (Figure): Plotly Figure object.
    """
    x_coords = []
    y_coords = []
    bubble_sizes = []
    bubble_colors = []
    texts = []

    for well, dnp, dgp in zip(wells, np_diff, gp_diff):
        well_row = wcoord_df[wcoord_df['Poço'] == well]
        if not well_row.empty:
            x = well_row['x'].values[0]
            y = well_row['y'].values[0]
            dnpe = (dnp + dgp)/1e6
            # print(f"well={well};dnpe={dnpe};dnp={dnp};dgp={dgp};")
            # input();
            x_coords.append(x)
            y_coords.append(y)
            bubble_sizes.append(abs(dnpe))
            color = 'green' if dnpe >= 0 else 'red'
            bubble_colors.append(color)
            texts.append(f"Poço: {well}<br>ΔNpe: {dnpe:.2f} Mm3<br>ΔNp: {dnp/1e6:.2f}<br>ΔGp: {dgp/1e6:.2f}<br>{x:.2f}, {y:.2f}")
        else:
            print(f"Coordinates not found for well {well}")
            continue

    if bubble_sizes:
        max_size = max(bubble_sizes)
    else:
        max_size = 1
    sizeref = 2. * max_size / (40. ** 2)

    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=x_coords,
        y=y_coords,
        mode='markers',
        marker=dict(
            size=[size for size in bubble_sizes],
            color=bubble_colors,
            sizemode='area',
            sizeref=sizeref,
            sizemin=4,
            line=dict(width=2, color='DarkSlateGrey')
        ),
        text=texts,
        hoverinfo='text'
    ))

    # Ensure equal aspect ratio
    x_range = max(x_coords) - min(x_coords)
    y_range = max(y_coords) - min(y_coords)
    max_range = max(x_range, y_range)

    # Center the data
    x_center = (max(x_coords) + min(x_coords)) / 2
    y_center = (max(y_coords) + min(y_coords)) / 2

    fig.update_layout(
        title=f'Mapa dos Poços com ΔNpe',
        xaxis=dict(
            title='Coordenada X',
            range=[x_center - max_range/2, x_center + max_range/2],
            scaleanchor="y",
            scaleratio=1,
        ),
        yaxis=dict(
            title='Coordenada Y',
            range=[y_center - max_range/2, y_center + max_range/2],
        ),
        showlegend=False,
        # width=600,
        # height=600,
    )

    return fig

@log_execution
def create_bubble_chart_distances(wells, np_diff, wcoord_df, distances):
    """
    Creates a bubble chart of wells, where bubble size represents Np difference and color indicates positive or negative difference.

    Parameters:
        wells (list): List of well names.
        np_diff (list): List of Np differences.
        wcoord_df (DataFrame): DataFrame containing well coordinates.
        distances (list): List of distances to the new well.

    Returns:
        fig (Figure): Plotly Figure object.
    """
    x_coords = []
    y_coords = []
    bubble_sizes = []
    bubble_colors = []
    texts = []

    for well, npe_diff, distance in zip(wells, np_diff, distances):
        well_row = wcoord_df[wcoord_df['Poço'] == well]
        if not well_row.empty:
            x = well_row['x'].values[0]
            y = well_row['y'].values[0]
            x_coords.append(x)
            y_coords.append(y)
            bubble_sizes.append(abs(npe_diff))
            color = 'green' if npe_diff >= 0 else 'red'
            bubble_colors.append(color)
            texts.append(f"Poço: {well}<br>ΔNp: {npe_diff:.2f}<br>Distância: {distance:.2f}")
        else:
            print(f"Coordinates not found for well {well}")
            continue

    if bubble_sizes:
        max_size = max(bubble_sizes)
    else:
        max_size = 1
    sizeref = 2. * max_size / (40. ** 2)

    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=x_coords,
        y=y_coords,
        mode='markers',
        marker=dict(
            size=[size for size in bubble_sizes],
            color=bubble_colors,
            sizemode='area',
            sizeref=sizeref,
            sizemin=4,
            line=dict(width=2, color='DarkSlateGrey')
        ),
        text=texts,
        hoverinfo='text'
    ))

    # Ensure equal aspect ratio
    x_range = max(x_coords) - min(x_coords)
    y_range = max(y_coords) - min(y_coords)
    max_range = max(x_range, y_range)

    # Center the data
    x_center = (max(x_coords) + min(x_coords)) / 2
    y_center = (max(y_coords) + min(y_coords)) / 2

    fig.update_layout(
        title=f'Mapa dos Poços com ΔNp',
        xaxis=dict(
            title='Coordenada X',
            range=[x_center - max_range/2, x_center + max_range/2],
            scaleanchor="y",
            scaleratio=1,
        ),
        yaxis=dict(
            title='Coordenada Y',
            range=[y_center - max_range/2, y_center + max_range/2],
        ),
        showlegend=False,
        # width=600,
        # height=600,
    )

    return fig

@log_execution
def plot_3d_bars(x, y, z_values, labels, distances):
    """
    Generates a 3D bar plot based on the provided data.

    Parameters:
        x (array-like): X coordinates.
        y (array-like): Y coordinates.
        z_values (array-like): Z values (heights of the bars).
        labels (array-like): Labels for each bar.
        distances (list): Distances to the new well.

    Returns:
        fig (Figure): Plotly Figure object.
    """
    # Convert to numpy arrays
    x = np.array(x)
    y = np.array(y)
    z_values = np.array(z_values)
    labels = np.array(labels)

    # Calculate ranges for equal aspect ratio
    max_range = max(x.max() - x.min(), y.max() - y.min())
    x_center = (x.max() + x.min()) / 2
    y_center = (y.max() + y.min()) / 2
    x_min = x_center - max_range / 2
    x_max = x_center + max_range / 2
    y_min = y_center - max_range / 2
    y_max = y_center + max_range / 2

    # Axis dimensions
    bar_width = max_range / 40  # Adjust proportionally

    fig = go.Figure()

    for xi, yi, zi, label, distance in zip(x, y, z_values, labels, distances):
        x0, y0 = xi, yi
        z0 = 0
        z1 = zi

        # Vertices of the cuboid (bar)
        vertices_x = [
            x0 - bar_width / 2, x0 + bar_width / 2, x0 + bar_width / 2, x0 - bar_width / 2,
            x0 - bar_width / 2, x0 + bar_width / 2, x0 + bar_width / 2, x0 - bar_width / 2
        ]
        vertices_y = [
            y0 - bar_width / 2, y0 - bar_width / 2, y0 + bar_width / 2, y0 + bar_width / 2,
            y0 - bar_width / 2, y0 - bar_width / 2, y0 + bar_width / 2, y0 + bar_width / 2
        ]
        vertices_z = [
            z0, z0, z0, z0,
            z1, z1, z1, z1
        ]

        # Faces of the cuboid
        faces = [
            (0, 1, 2), (0, 2, 3),
            (4, 5, 6), (4, 6, 7),
            (0, 1, 5), (0, 5, 4),
            (1, 2, 6), (1, 6, 5),
            (2, 3, 7), (2, 7, 6),
            (3, 0, 4), (3, 4, 7)
        ]

        # Color based on sign of z1
        bar_color = "rgb(0, 200, 0)" if z1 >= 0 else "rgb(200, 0, 0)"

        # Add the bar to the plot
        fig.add_trace(go.Mesh3d(
            x=vertices_x,
            y=vertices_y,
            z=np.abs(vertices_z),
            color=bar_color,
            opacity=1.0,
            i=[f[0] for f in faces],
            j=[f[1] for f in faces],
            k=[f[2] for f in faces],
            hoverinfo="text",
            text=f"Poço: {label}<br>ΔNpe: {z1:.2f}<br>Distância: {distance:.2f}"
        ))

    # Initial camera angle
    camera = dict(
        eye=dict(x=-0.1, y=-1.25, z=1.25)
    )

    fig.update_layout(
        scene=dict(
            xaxis=dict(
                title='Coordenada X',
                range=[x_min, x_max],
                nticks=10,
            ),
            yaxis=dict(
                title='Coordenada Y',
                range=[y_min, y_max],
                nticks=10,
            ),
            zaxis=dict(
                title=f'ΔNpe',
                nticks=10,
            ),
            aspectratio=dict(x=1, y=1, z=0.5),
            aspectmode='manual',
        ),
        title=f"Variação de Npe por Poço",
        scene_camera=camera
    )

    return fig

@log_execution
def create_multiple_delta_time_series_plot(proj_dataframes, base_dataframes, scatter_info, entity, variables, units):
    """
    Creates multiple time series plots for the specified variables.

    Parameters:
        proj_dataframes (dict): Dictionary of current simulation DataFrames.
        base_dataframes (dict): Dictionary of base simulation DataFrames.
        scatter_info (dict): Information about simulations categorized into regions.
        entity (str): Entity name (e.g., "FIELD-PRO" or "FIELD").
        variables (list): List of variable names.
        units (list): List of units corresponding to the variables.

    Returns:
        figs (list): List of Plotly Figure objects.
    """
    figs = []
    for variable, unit in zip(variables, units):
        fig = create_delta_time_series_plot(proj_dataframes, base_dataframes, scatter_info, entity, variable, unit)
        figs.append(fig)
    return figs

@log_execution
def get_common_realizations(proj_dfs, base_dfs):
    if base_dfs == None:
        return proj_dfs, None

    # Pegamos o conjunto de simulações em comum
    common_realizations = set(proj_dfs.keys()) & set(base_dfs.keys())

    # Filtramos ambos os dicionários para manter apenas as simulações em comum
    proj_dfs_out = {k: v for k, v in proj_dfs.items() if k in common_realizations}
    base_dfs_out = {k: v for k, v in base_dfs.items() if k in common_realizations}

    return proj_dfs_out, base_dfs_out

@log_execution
def subtract_dataframes(df_proj: pd.DataFrame, df_base: pd.DataFrame) -> pd.DataFrame:
    if df_base is None:
        return df_proj

    # 1. Identificar colunas com 'On-time Fraction'
    on_time_fraction_cols = [
        col for col in df_proj.columns
        if "On-time Fraction" in col
    ]

    # 2. Reindexar mantendo a união de índices e colunas
    union_index = df_proj.index.union(df_base.index)
    union_cols = df_proj.columns.union(df_base.columns)

    # 3. Para colunas especiais: usar df_proj original preenchido com df_base
    special_cols = {}
    for col in on_time_fraction_cols:
        # Valores originais do projeto
        s_proj = df_proj[col].reindex(union_index)

        # Preencher ausentes com valores da base (se existirem)
        if col in df_base.columns:
            s_base = df_base[col].reindex(union_index)
            s_proj = s_proj.fillna(s_base)

        special_cols[col] = s_proj.fillna(0)  # Preencher restante com 0

    # 4. Para outras colunas: subtração normal com preenchimento de 0
    df_proj_reindexed = df_proj.reindex(index=union_index, columns=union_cols, fill_value=0)
    df_base_reindexed = df_base.reindex(index=union_index, columns=union_cols, fill_value=0)
    df_result = df_proj_reindexed - df_base_reindexed

    # 5. Restaurar valores das colunas especiais
    for col, values in special_cols.items():
        if col in df_result.columns:
            df_result[col] = values

    return df_result

@log_execution
def get_delta_time_series_df(
    proj_dataframes_in: dict,
    base_dataframes_in: dict,
    entity: str,
    variable: str,
    unit: str
) -> pd.DataFrame:
    """
    Retorna um DataFrame cujas colunas são as simulações (renomeadas como "Simulation XXX")
    e o índice é o tempo. O conteúdo das colunas é 'delta = proj - base' para (entity, variable, unit).

    Se base_dataframes_in for None, retorna apenas o 'proj' (sem subtrair nada).
    """
    proj_dataframes, base_dataframes = get_common_realizations(proj_dataframes_in, base_dataframes_in)

    current_series = {}
    for number, df in proj_dataframes.items():
        if (entity, variable, unit) in df.columns:
            current_series[number] = df[(entity, variable, unit)]
        else:
            print(
                f"Warning: Column ({entity}, {variable}, {unit}) not found "
                f"in CURRENT simulation {number}."
            )
            continue
    current = pd.DataFrame(current_series)

    if base_dataframes is not None:
        base_series = {}
        for number, df in base_dataframes.items():
            if (entity, variable, unit) in df.columns:
                base_series[number] = df[(entity, variable, unit)]
            else:
                # print(
                #     f"Warning: Column ({entity}, {variable}, {unit}) not found "
                #     f"in BASE simulation {number}. Skipping."
                # )
                base_series[number] = pd.Series(0, index=df.index)
        base = pd.DataFrame(base_series)
    else:
        # If there's no base_dataframes, interpret delta as just the raw data
        base = None

    if variable in ["On-time Fraction", "Oil Volume SC SCTR", "VOIP"]:
        delta = current
    else:
        delta = subtract_dataframes(
            df_proj=current,
            df_base=base
        )

    return delta, proj_dataframes, base_dataframes

import pandas as pd
import numpy as np
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font, Alignment

@log_execution
def sanitize_sheet_name(name):
    name = name.replace("*", "p")
    invalid_chars = ['\\', '/', '*', '?', ':', '[', ']']
    for char in invalid_chars:
        name = name.replace(char, '_')
    return name[:31]

@log_execution
def export_multiple_delta_time_series_to_excel(
    list_of_specs,          # lista de tuplas (entity, variable, unit)
    proj_dataframes_in,
    base_dataframes_in,
    output_excel_path
):
    """
    Gera um arquivo Excel, criando uma aba para cada (entity, variable, unit).
    Em cada aba, cada Realization vira uma coluna (linha = Date, coluna = Realization).

    Parameters
    ----------
    list_of_specs : list of tuple
        Lista de tuplas (entity, variable, unit) a serem processadas.
    proj_dataframes_in : dict
        Dicionário de DataFrames das simulações do caso "projeto".
    base_dataframes_in : dict
        Dicionário de DataFrames das simulações base (pode ser None).
    output_excel_path : str
        Caminho do arquivo Excel de saída.
    """

    # Cria um Workbook novo e remove a planilha inicial que vem por padrão
    wb = Workbook()
    default_ws = wb.active
    wb.remove(default_ws)

    for (entity, variable, unit) in list_of_specs:
        # 1) Obter o DataFrame delta (wide)
        delta_df, _, _ = get_delta_time_series_df(
            proj_dataframes_in,
            base_dataframes_in,
            entity,
            variable,
            unit
        )

        # Se não houver dados, simplesmente pula
        if delta_df.empty:
            print(f"Aviso: nenhum dado encontrado para ({entity}, {variable}, {unit}). Pulando...")
            continue

        # 2) Cria uma aba com nome sanitizado
        sheet_name = sanitize_sheet_name(f"{entity}_{variable}_{unit}")
        ws = wb.create_sheet(title=sheet_name)

        # 3) Preenche o cabeçalho
        ws.cell(row=1, column=1, value="Date").font = Font(bold=True)
        for col_idx, col_name in enumerate(delta_df.columns, start=2):
            cell = ws.cell(row=1, column=col_idx, value=str(col_name))
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 4) Preenche as linhas (Data no índice)
        for row_idx, (date_idx, row_data) in enumerate(delta_df.iterrows(), start=2):
            # Escreve a data na primeira coluna
            ws.cell(row=row_idx, column=1, value=date_idx)
            # Escreve os valores das realizações
            for col_idx, col_name in enumerate(delta_df.columns, start=2):
                value = row_data[col_name]
                ws.cell(row=row_idx, column=col_idx, value=value)

        # 5) Formatação opcional de colunas
        ws.column_dimensions[get_column_letter(1)].width = 14  # Data
        for i in range(2, delta_df.shape[1] + 2):
            ws.column_dimensions[get_column_letter(i)].width = 12

    # Salvar o arquivo Excel resultante
    wb.save(output_excel_path)
    print(f"Arquivo Excel salvo em: {output_excel_path}")

@log_execution
def export_delta_time_series_to_excel(
    proj_dataframes_in, base_dataframes_in,
    entity, variable, unit,
    output_excel_path
):
    """
    Gera um arquivo Excel com cada Realization em uma coluna
    (linha = data, coluna = Realization).
    """
    import pandas as pd
    from openpyxl import Workbook
    from openpyxl.utils import get_column_letter
    from openpyxl.styles import Font, Alignment

    # 1) Obtemos o DataFrame delta (que já vem no formato wide: colunas = Realizations)
    delta_df, _, _ = get_delta_time_series_df(
        proj_dataframes_in,
        base_dataframes_in,
        entity,
        variable,
        unit
    )
    # Se delta_df estiver vazio, apenas retornar
    if delta_df.empty:
        print("Aviso: nenhum dado encontrado para exportar.")
        return

    # 2) Criar uma planilha e adicionar cabeçalhos
    wb = Workbook()
    ws = wb.active
    ws.title = "TimeSeries"

    # Cabeçalho: primeira coluna = "Date", depois cada Realization
    ws.cell(row=1, column=1, value="Date").font = Font(bold=True)
    for col_idx, col_name in enumerate(delta_df.columns, start=2):
        cell = ws.cell(row=1, column=col_idx, value=col_name)
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center')

    # 3) Preencher as linhas com datas e valores
    for row_idx, (date_idx, row_data) in enumerate(delta_df.iterrows(), start=2):
        # Escreve a data na primeira coluna
        ws.cell(row=row_idx, column=1, value=date_idx)
        for col_idx, col_name in enumerate(delta_df.columns, start=2):
            value = row_data[col_name]
            ws.cell(row=row_idx, column=col_idx, value=value)

    # 4) Ajustar tamanho das colunas
    #    (opcional, apenas para estética)
    # Largura da coluna de datas
    ws.column_dimensions[get_column_letter(1)].width = 14
    # Largura das colunas de realizações
    for i in range(2, delta_df.shape[1] + 2):
        ws.column_dimensions[get_column_letter(i)].width = 12

    # 5) Salvar
    wb.save(output_excel_path)
    print(f"Excel salvo em: {output_excel_path}")

@log_execution
def create_delta_time_series_plot(proj_dataframes_in, base_dataframes_in, scatter_info, entity, variable, unit):
    """
    Creates a time series plot of the delta between current and base simulations for a specified variable,
    grouping the legend entries by simulation categories using legendgroup.

    Parameters:
        proj_dataframes (dict): Dictionary of current simulation DataFrames.
        base_dataframes (dict): Dictionary of base simulation DataFrames.
        scatter_info (dict): Information about simulations categorized into regions.
        entity (str): Entity name (e.g., "FIELD-PRO").
        variable (str): Variable name (e.g., "Npe*").
        unit (str): Unit of the variable.

    Returns:
        fig (Figure): Plotly Figure object.
    """
    # Obter o delta_df
    delta, proj_dataframes, base_dataframes = get_delta_time_series_df(proj_dataframes_in, base_dataframes_in, entity, variable, unit)


    fig = go.Figure()

    # Color mapping for simulations
    color_map = color_map_default

    # Keep track of legend groups to display legend only once per group
    legend_displayed = {
        'P10': False,
        'P50': False,
        'P90': False,
        'user': False,
        'other': False,
    }

    for column in delta.columns:
        if column in scatter_info['simulations']['P10']:
            category = 'P10'
        elif column in scatter_info['simulations']['P50']:
            category = 'P50'
        elif column in scatter_info['simulations']['P90']:
            category = 'P90'
        elif 'user' in scatter_info['simulations'] and column in scatter_info['simulations']['user']:
            category = 'user'
        else:
            category = 'other'

        color = color_map[category]

        # Show legend only once per group
        if not legend_displayed[category]:
            show_legend = True
            legend_displayed[category] = True
        else:
            show_legend = False

        fig.add_trace(go.Scatter(
            x=delta.index,
            y=delta[column],
            mode='lines',
            name=category if show_legend else None,
            legendgroup=category,
            showlegend=show_legend,
            line=dict(color=color, width=2 if color != 'darkgrey' else 1),
            opacity=1 if color != 'darkgrey' else 0.5,
            hovertemplate=f'Date: %{{x|%d/%m/%Y}}<br>Simulation {column}<br>Δ{variable}: %{{y:.2f}} {unit}<extra></extra>'
        ))

    # # Add mean line
    # mean_delta = delta.mean(axis=1)
    # fig.add_trace(go.Scatter(
    #     x=delta.index,
    #     y=mean_delta,
    #     mode='lines',
    #     name='Mean',
    #     legendgroup='Mean',
    #     line=dict(color='black', width=4, dash='dash'),
    #     showlegend=True
    # ))
    median_delta = delta.median(axis=1)
    fig.add_trace(go.Scatter(
        x=delta.index,
        y=median_delta,
        mode='lines',
        name='Median',
        legendgroup='Median',
        line=dict(color='black', width=4, dash='dot'),
        showlegend=True
    ))

    if base_dataframes == None:
        figure_title = f'{entity} {variable} over time'
        figure_yaxis_title = f'{variable} ({unit})'
    else:
        figure_title = f'{entity} Δ{variable} over time'
        figure_yaxis_title = f'Δ{variable} ({unit})'
    fig.update_layout(
        title=figure_title,
        xaxis_title='Date',
        yaxis_title=figure_yaxis_title,
        showlegend=True,
        legend=dict(
            title='Simulations',
            itemsizing='constant',
            itemwidth=30
        )
    )

    return fig

color_map_default = {
    'P10': 'blue',
    'P50': 'green',
    'P90': 'red',
    'user': 'orange',
    'other': 'darkgrey',
}

@log_execution
def create_time_series_plot(dataframes, scatter_info, entity, variable, unit):
    """
    Creates a time series plot for a specified variable across simulations,
    grouping the legend entries by simulation categories using legendgroup.

    Parameters:
        dataframes (dict): Dictionary of simulation DataFrames.
        scatter_info (dict): Information about simulations categorized into regions.
        entity (str): Entity name (e.g., "FIELD").
        variable (str): Variable name (e.g., "Pressure").
        unit (str): Unit of the variable.

    Returns:
        fig (Figure): Plotly Figure object.
    """
    # Extract time series
    series = {}
    for number, df in dataframes.items():
        if (entity, variable, unit) in df.columns:
            series[number] = df[(entity, variable, unit)]
        else:
            print(f"Warning: Column ({entity}, {variable}, {unit}) not found in simulation {number}")

    if not series:
        print(f"Error: No data found for ({entity}, {variable}, {unit})")
        return None  # Return None if no data is found

    current = pd.DataFrame(series)

    fig = go.Figure()

    # Color mapping for simulations
    color_map = color_map_default

    # Keep track of legend groups to display legend only once per group
    legend_displayed = {
        'P10': False,
        'P50': False,
        'P90': False,
        'user': False,
        'other': False
    }

    for column in current.columns:
        if column in scatter_info['simulations']['P10']:
            category = 'P10'
        elif column in scatter_info['simulations']['P50']:
            category = 'P50'
        elif column in scatter_info['simulations']['P90']:
            category = 'P90'
        elif 'user' in scatter_info['simulations'] and column in scatter_info['simulations']['user']:
            category = 'user'
        else:
            category = 'other'

        color = color_map[category]

        # Show legend only once per group
        if not legend_displayed[category]:
            show_legend = True
            legend_displayed[category] = True
        else:
            show_legend = False

        fig.add_trace(go.Scatter(
            x=current.index,
            y=current[column],
            mode='lines',
            name=category if show_legend else None,
            legendgroup=category,
            showlegend=show_legend,
            line=dict(color=color, width=2 if color != 'darkgrey' else 1),
            opacity=1 if color != 'darkgrey' else 0.5,
            hovertemplate=f'Date: %{{x|%d/%m/%Y}}<br>Simulation {column}<br>{variable}: %{{y:.2f}} {unit}<extra></extra>'
        ))

    # Add mean line
    mean_current = current.mean(axis=1)
    fig.add_trace(go.Scatter(
        x=current.index,
        y=mean_current,
        mode='lines',
        name='Mean',
        legendgroup='Mean',
        line=dict(color='black', width=4, dash='dash'),
        showlegend=True
    ))

    fig.update_layout(
        title=f'{entity} ({variable}) over time',
        xaxis_title='Date',
        yaxis_title=f'{variable} ({unit})',
        showlegend=True,
        legend=dict(
            title='Simulations',
            itemsizing='constant',
            itemwidth=30
        )
    )

    return fig

@log_execution
def create_simple_histogram(var_out):
    """
    Gera um histograma dos valores (um por simulação) armazenados em var_out['values'].
    Adiciona linhas verticais para P10, P50, P90.
    """
    # 1) Extrair os valores num array
    all_vals = list(var_out['values'].values())
    # Filtrar Nones / NaNs
    all_vals = [v for v in all_vals if v is not None and not np.isnan(v)]

    if len(all_vals) == 0:
        # Cria uma figura "vazia" com mensagem
        fig = go.Figure()
        fig.add_annotation(
            text="Sem dados para histograma",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False
        )
        fig.update_layout(
            title="Histograma indisponível",
            xaxis_title="Valores",
            yaxis_title="Contagem"
        )
        return fig

    # 2) Calcular p10, p50, p90
    p10_val = np.percentile(all_vals, 10)
    p50_val = np.percentile(all_vals, 50)
    p90_val = np.percentile(all_vals, 90)

    # 3) Criar histograma
    fig = go.Figure()
    fig.add_trace(go.Histogram(
        x=all_vals,
        nbinsx=20,  # ou outro valor
        marker_color='lightblue',
        opacity=0.75,
    ))

    # Vamos descobrir o max_y para "esticar" as linhas
    # Depois que a trace foi adicionada, a contagem fica em fig.data[0].y,
    # mas só é calculada a partir do layout. Em chart offline, podemos forçar
    # ou estimar. Uma forma rápida é usar np.histogram:
    counts, bins = np.histogram(all_vals, bins=20)
    max_y = counts.max() if len(counts) else 1

    # Adicionar linhas verticais:
    for val, label in [(p10_val, 'P10'), (p50_val, 'P50'), (p90_val, 'P90')]:
        fig.add_shape(
            type="line",
            x0=val,
            x1=val,
            y0=0,
            y1=max_y,
            line=dict(color='red', width=2, dash='dash'),
        )
        # Adicionar rótulo
        fig.add_annotation(
            x=val, y=max_y,
            text=label,
            showarrow=False,
            xanchor='left',
            yanchor='top',
            font=dict(color='red', size=10)
        )

    # 4) Ajustar layout
    ent   = var_out['entity']
    var   = var_out['name']
    unit  = var_out['unit']
    offs  = var_out.get('offset_str', '')
    fig.update_layout(
        title=f"Histograma de {ent}.{var} ({unit}) — offset={offs}",
        xaxis_title=f"{var} ({unit})",
        yaxis_title="Contagem",
        bargap=0.1,
        showlegend=False
    )

    return fig

@log_execution
def create_3d_scatter_plot(voips, dnpepv_values, dataframes, base_dataframes, new_well_name):
    """
    Creates a 3D scatter plot of VOIP vs ΔNpe* FIELD-PRO vs ΔNpe* of the new well.

    Parameters:
        voips (dict): Dictionary of VOIP values.
        dnpepv_values (dict): Dictionary of ΔNpe* FIELD-PRO values.
        dataframes (dict): Dictionary of current simulation DataFrames.
        base_dataframes (dict): Dictionary of base simulation DataFrames.
        new_well_name (str): Name of the new well.

    Returns:
        fig (Figure): Plotly Figure object.
    """
    # Calculate ΔNpe* for the new well
    new_well_dnpepv = {}
    for number in voips.keys():
        if (new_well_name, "Npe*", "m3") in dataframes[number].columns:
            new_well_npe = dataframes[number][(new_well_name, "Npe*", "m3")].iloc[-1]
            base_new_well_npe = base_dataframes[number][(new_well_name, "Npe*", "m3")].iloc[-1] if (new_well_name, "Npe*", "m3") in base_dataframes[number].columns else 0
            new_well_dnpepv[number] = new_well_npe - base_new_well_npe
        else:
            new_well_dnpepv[number] = 0

    fig = go.Figure(data=[go.Scatter3d(
        x=list(voips.values()),
        y=list(dnpepv_values.values()),
        z=list(new_well_dnpepv.values()),
        mode='markers',
        marker=dict(
            size=5,
            color=list(new_well_dnpepv.values()),
            colorscale='Viridis',
            opacity=0.8
        ),
        text=[f"Simulation {num}" for num in voips.keys()],
        hoverinfo='text'
    )])

    fig.update_layout(
        scene=dict(
            xaxis_title='VOIP (m3)',
            yaxis_title='ΔNpe* FIELD-PRO (m3)',
            zaxis_title=f'ΔNpe* {new_well_name} (m3)'
        ),
        title=f'3D Scatter Plot: VOIP vs ΔNpe* FIELD-PRO vs ΔNpe* {new_well_name}'
    )

    return fig

@log_execution
def create_scatter_plot(scatter_info, var_id_x, var_id_y):
    """
    var_id_x e var_id_y podem ser algo como "FIELD-PRO.Npe*(m3)" ou, se preferir,
    a gente acha na marra dentro de scatter_info['variables'] quem bate com var_id_x...

    Aqui é igual ao create_scatter_with_marginals_plot mas sem hist marginal.
    """
    import plotly.graph_objects as go
    from math import isnan

    # Achar varX e varY
    @log_execution
    def find_vardict(var_id):
        for v in scatter_info['variables']:
            # Construa a string 'ENTITY.VAR_NAME(UNIT)' ou algo similar
            cand = f"{v['entity']}.{v['name']}({v['unit']})"
            if cand == var_id:
                return v
        return None

    varX = find_vardict(var_id_x)
    varY = find_vardict(var_id_y)
    if varX is None or varY is None:
        raise ValueError(f"Could not find {var_id_x} or {var_id_y} in scatter_info['variables']")

    # Montar a lista de sim
    sims_union = set(varX['values'].keys()) | set(varY['values'].keys())
    sims_sorted = sorted(sims_union)

    xvals, yvals, colors, texts = [], [], [], []

    for sim in sims_sorted:
        xv = varX['values'].get(sim, None)
        yv = varY['values'].get(sim, None)
        if xv is None or yv is None:
            continue
        c = scatter_info['colors'].get(sim, 'darkgrey')
        xvals.append(xv)
        yvals.append(yv)
        colors.append(c)
        texts.append(sim)

    # Correlação:
    from scipy.stats import pearsonr
    valid_xy = [(x,y) for x,y in zip(xvals, yvals) if x is not None and y is not None]
    if len(valid_xy) >= 2:
        X = [p[0] for p in valid_xy]
        Y = [p[1] for p in valid_xy]
        corr, _ = pearsonr(X, Y)
    else:
        corr = float('nan')

    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=xvals, y=yvals,
        mode='markers',
        marker=dict(color=colors, size=8),
        text=[f"Sim {s}" for s in texts],
        hovertemplate="Sim %{text}<br>X=%{x:.2f}<br>Y=%{y:.2f}<extra></extra>"
    ))

    fig.update_layout(
        title=f"{var_id_y} vs {var_id_x} (corr={corr:.2f})",
        xaxis_title=var_id_x,
        yaxis_title=var_id_y,
        showlegend=False
    )
    return fig

@log_execution
def calculate_new_var(dataframes1, dataframes2, entity, var1, var2, unit1, unit2, factor1, factor2, operation, var_out, unit_out):
    """
    Calculates new_var (dataframes1(entity, var1, unit1)*factor1 operation dataframes2(entity, var2, unit2)*factor2)
    for the specified entity across all simulations.

    Parameters:
        dataframes1 (dict): Dictionary of simulation DataFrames for the first variable.
        dataframes2 (dict): Dictionary of simulation DataFrames for the second variable.
        entity (str): The entity to be used in the DataFrames (e.g., well name).
        var1 (str): Variable name in dataframes1 (e.g., "Qo").
        var2 (str): Variable name in dataframes2 (e.g., "Qg").
        unit1 (str): Unit of var1 (e.g., "m3/day").
        unit2 (str): Unit of var2 (e.g., "m3/day").
        factor1 (float): Factor to multiply var1.
        factor2 (float): Factor to multiply var2.
        operation (str): Operation to perform ('+', '-', '*', '/').

    Returns:
        new_var_df (DataFrame): DataFrame containing new_var time series for each simulation.
    """
    new_var = {}

    # For each simulation, perform the calculation
    for number in dataframes1.keys():
        df1 = dataframes1[number]
        df2 = dataframes2.get(number, None)  # In case dataframes2 doesn't have this simulation

        if df1 is None or df2 is None:
            continue  # Skip if DataFrames are missing

        # Try to extract the columns; if missing, create a Series of zeros
        if (entity, var1, unit1) in df1.columns:
            v1 = df1[(entity, var1, unit1)] * factor1
        else:
            v1 = pd.Series(0, index=df1.index)

        if (entity, var2, unit2) in df2.columns:
            v2 = df2[(entity, var2, unit2)] * factor2
        else:
            v2 = pd.Series(0, index=df1.index)  # Use df1 index to align

        # Now perform the operation
        if operation == "+":
            result = v1 + v2
        elif operation == "-":
            result = v1 - v2
        elif operation == "/":
            # Handle division by zero
            with np.errstate(divide='ignore', invalid='ignore'):
                result = v1 / v2.replace({0: np.nan})
        elif operation == "*":
            result = v1 * v2
        else:
            raise ValueError(f"Invalid operation '{operation}'. Supported operations are '+', '-', '/', '*'.")

        new_var[number] = result

    # Combine the series into a DataFrame
    new_var_out = {"df": pd.DataFrame(new_var), "entity": entity, "variable": var_out, "unit": unit_out}

    return new_var_out

@log_execution
def calculate_combined_var(new_var1, new_var2, factor1, factor2, operation, var_out, unit_out):
    """
    Calculates a new variable based on the formula:
    new_var = new_var1 * factor1 operation new_var2 * factor2

    Parameters:
        new_var1 (dict): Dictionary of DataFrames for the first variable.
        new_var2 (dict): Dictionary of DataFrames for the second variable.
        factor1 (float): Factor to multiply new_var1.
        factor2 (float): Factor to multiply new_var2.
        operation (str): Operation to perform ('+', '-', '*', '/').
        var_out (str): Name of the output variable.
        unit_out (str): Unit of the output variable.

    Returns:
        combined_var_out (dict): Dictionary containing the resulting DataFrame, variable name, and unit.
    """
    combined_var = {}

    for number in new_var1["df"].columns:
        # Get the first variable (new_var1)
        if number in new_var1["df"].columns:
            var1_series = new_var1["df"][number] * factor1
        else:
            var1_series = pd.Series(0, index=new_var1["df"].index)

        # Get the second variable (new_var2)
        if number in new_var2["df"].columns:
            var2_series = new_var2["df"][number] * factor2
        else:
            var2_series = pd.Series(np.nan, index=new_var1["df"].index)

        # Perform the operation
        with np.errstate(divide='ignore', invalid='ignore'):
            if operation == '+':
                result_series = var1_series + var2_series
            elif operation == '-':
                result_series = var1_series - var2_series
            elif operation == '*':
                result_series = var1_series * var2_series
            elif operation == '/':
                result_series = var1_series / var2_series.replace({0: np.nan})
            else:
                raise ValueError(f"Invalid operation '{operation}'. Supported operations are '+', '-', '*', '/'.")

        # Replace infinities and NaNs with zeros
        result_series.replace([np.inf, -np.inf], np.nan, inplace=True)
        result_series.fillna(0, inplace=True)

        # Store in the combined_var dictionary
        combined_var[number] = result_series

    # Combine the series into a DataFrame
    combined_var_out = {"df": pd.DataFrame(combined_var), "variable": var_out, "unit": unit_out}

    return combined_var_out

@log_execution
def create_new_var_time_series_plot(new_var, scatter_info, new_well_name):
    """
    Creates a time series plot of new_var for the new well across simulations,
    grouping the legend entries by simulation categories using legendgroup.

    Parameters:
        new_var: corrigir DataFrame containing new_var time series for each simulation.
        scatter_info (dict): Information about simulations categorized into regions.
        new_well_name (str): Name of the new well.

    Returns:
        fig (Figure): Plotly Figure object.
    """
    fig = go.Figure()

    # Color mapping for simulations
    color_map = {
        'P10': 'blue',
        'P50': 'green',
        'P90': 'red',
        'other': 'darkgrey'
    }

    # Keep track of legend groups to display legend only once per group
    legend_displayed = {
        'P10': False,
        'P50': False,
        'P90': False,
        'user': False,
        'other': False
    }

    for column in new_var["df"].columns:
        if column in scatter_info['simulations']['P10']:
            category = 'P10'
        elif column in scatter_info['simulations']['P50']:
            category = 'P50'
        elif column in scatter_info['simulations']['P90']:
            category = 'P90'
        else:
            category = 'other'

        color = color_map[category]

        # Show legend only once per group
        if not legend_displayed[category]:
            show_legend = True
            legend_displayed[category] = True
        else:
            show_legend = False

        fig.add_trace(go.Scatter(
            x=new_var["df"].index,
            y=new_var["df"][column],
            mode='lines',
            name=category if show_legend else None,
            legendgroup=category,
            showlegend=show_legend,
            line=dict(color=color, width=2 if color != 'darkgrey' else 1),
            opacity=1 if color != 'darkgrey' else 0.5,
            hovertemplate=f'Date: %{{x|%d/%m/%Y}}<br>Simulation {column}<br>{new_var["variable"]}: %{{y:.2f}} {new_var["unit"]}<extra></extra>'
        ))

    # Add mean line
    mean_new_var = new_var["df"].mean(axis=1)
    fig.add_trace(go.Scatter(
        x=new_var["df"].index,
        y=mean_new_var,
        mode='lines',
        name='Mean',
        legendgroup='Mean',
        line=dict(color='black', width=4, dash='dash'),
        showlegend=True
    ))

    fig.update_layout(
        title=f'{new_var["variable"]} over time for {new_well_name}',
        xaxis_title='Date',
        yaxis_title=f'{new_var["variable"]} ({new_var["unit"]})',
        showlegend=True,
        legend=dict(
            title='Simulations',
            itemsizing='constant',
            itemwidth=30
        )
    )

    return fig

@log_execution
def generate_html_reports(directory_path, dataframes, base_dataframes, new_wells, scatter_info_field, scatter_info_well, all_distances):
    # Generate main HTML report
    doc, tag, text, line = Doc().ttl()

    @log_execution
    def main_content_generator():
        generate_field_html(doc, tag, text, line, dataframes, base_dataframes, scatter_info_field, scatter_info_well, directory_path, new_wells)

    create_html_structure(doc, tag, text, main_content_generator, 'Ensemble Report')

    main_html_path = os.path.join(directory_path, 'ensemble_report.html')
    with open(main_html_path, 'w', encoding='utf-8') as f:
        f.write(indent(doc.getvalue()))

    # Generate individual well reports
    for well in new_wells:
        doc, tag, text, line = Doc().ttl()

        @log_execution
        def well_content_generator():
            generate_well_html(doc, tag, text, line, well, dataframes, base_dataframes, scatter_info_well[well], all_distances)

        create_html_structure(doc, tag, text, well_content_generator, f'{well} Report')

        well_html_path = os.path.join(directory_path, f'{well}_report.html')
        with open(well_html_path, 'w', encoding='utf-8') as f:
            f.write(indent(doc.getvalue()))

@log_execution
def generate_field_html(doc, tag, text, line, dataframes, base_dataframes, scatter_info_field, scatter_info_well, directory_path, new_wells):
    """
    Gera o HTML para a seção do FIELD.
    """
    # Adicionar sidebar
    with tag('div', id='mySidebar', klass='w3-sidebar w3-bar-block w3-collapse w3-card w3-animate-left', style='width:200px;'):
        with tag('button', klass='w3-bar-item w3-button w3-large w3-hide-large', onclick='w3_close()'):
            text('Close ×')
        with tag('a', href='#voip', klass='w3-bar-item w3-button'):
            text('VOIP')
        with tag('a', href='#dnpe', klass='w3-bar-item w3-button'):
            text('ΔNpe*')
        with tag('a', href='#representative_selection', klass='w3-bar-item w3-button'):
            text('Representative Selection')
        with tag('a', href='#dnpe_vs_voip', klass='w3-bar-item w3-button'):
            text('ΔNpe* vs VOIP')
        # Adicionar links para os novos gráficos
        field_pro_variables = ["Npe*", "Npe", "Np", "Gp", "Qo", "Qg", "Qw", "BSW", "RGO"]
        for var in field_pro_variables:
            with tag('a', href=f'#field_pro_{var.lower().replace("*", "")}', klass='w3-bar-item w3-button'):
                text(f'FIELD-PRO {var}')
        with tag('a', href='#field_dapre', klass='w3-bar-item w3-button'):
            text('FIELD DAPRE')
        # Na função generate_field_html, dentro do bloco que cria a barra lateral
        with tag('a', href='#bubble_chart', klass='w3-bar-item w3-button'):
            text('Mapa dos Poços')
        with tag('a', href='#new_wells', klass='w3-bar-item w3-button'):
            text('Novos Poços')

    # Conteúdo principal
    with tag('div', klass='w3-main', style='margin-left:200px'):
        with tag('div', klass='w3-teal'):
            with tag('button', klass='w3-button w3-teal w3-xlarge w3-hide-large', onclick='w3_open()'):
                text('☰')
            with tag('div', klass='w3-container'):
                with tag('h1'):
                    text('Ensemble Report')

        with tag('div', klass='w3-container'):
            with tag('h3', id='voip'):
                text('VOIP')
            fig_voip = create_distribution_plot(scatter_info_field, 'VOIP')
            with tag('div'):
                doc.asis(fig_voip.to_html(full_html=False, include_plotlyjs='cdn'))

            with tag('h3', id='dnpe'):
                text('ΔNpe*')
            fig_dnpepv = create_distribution_plot(scatter_info_field, 'Npe*')
            with tag('div'):
                doc.asis(fig_dnpepv.to_html(full_html=False, include_plotlyjs='cdn'))

            # Informações sobre as simulações representativas
            with tag('h2', id='representative_selection'):
                text('Representative Selection')

            fig_scatter = create_scatter_with_marginals_plot(scatter_info_field)

            with tag('p'):
                text(f"Valor de dpercentile: {scatter_info_field['dpercentile']:.2f}")
            with tag('p'):
                text("Número de simulações em cada grupo:")
            with tag('ul'):
                for group in ['P10', 'P50', 'P90']:
                    with tag('li'):
                        text(f"{group}: {scatter_info_field['counts'][group]} simulações")
            with tag('p'):
                text("Simulações em cada grupo:")
            for group in ['P10', 'P50', 'P90']:
                with tag('h4'):
                    text(f"Grupo {group}:")
                with tag('ul'):
                    for sim in scatter_info_field['simulations'][group]:
                        with tag('li'):
                            text(f"Simulação {sim}")

            with tag('h3', id='dnpe_vs_voip'):
                text('ΔNpe* vs VOIP')
            with tag('div'):
                doc.asis(fig_scatter.to_html(full_html=False, include_plotlyjs='cdn'))

            # Adicionar novos gráficos de séries temporais
            field_pro_variables = ["Npe*", "Npe", "Np", "Gp", "Qo", "Qg", "Qw", "BSW", "RGO"]
            field_pro_units = ["m3", "m3", "m3", "m3", "m3/day", "m3/day", "m3/day", "fraction", "m3/m3"]
            field_pro_figs = create_multiple_delta_time_series_plot(dataframes, base_dataframes, scatter_info_field, "FIELD-PRO", field_pro_variables, field_pro_units)

            for var, fig in zip(field_pro_variables, field_pro_figs):
                with tag('h3', id=f'field_pro_{var.lower().replace("*", "")}'):
                    text(f'FIELD-PRO {var}')
                with tag('div'):
                    doc.asis(fig.to_html(full_html=False, include_plotlyjs='cdn'))

            field_variables = ["Ave DAPRE HC POVO SCTR"]
            field_units = ["kPa"]
            field_figs = create_multiple_delta_time_series_plot(dataframes, base_dataframes, scatter_info_field, "FIELD", field_variables, field_units)

            with tag('h3', id='field_dapre'):
                text('FIELD Ave DAPRE HC POVO SCTR')
            with tag('div'):
                doc.asis(field_figs[0].to_html(full_html=False, include_plotlyjs='cdn'))

            # Adicionar o gráfico de bolhas
            with tag('h2', id='bubble_chart'):
                text('Mapa dos Poços com DNpe (P50)')

            # Obter as coordenadas dos poços
            wcoord_df = read_wcoord_file(directory_path)

            # Obter todos os poços
            all_wells = get_all_wells(directory_path)

            # Calcular DNpe para P50
            sim_p50 = scatter_info_field['simulations']['P50'][0]
            np_diff_p50, gp_diff_p50 = get_production_difference_for_simulation(sim_p50, dataframes, base_dataframes, all_wells)

            # Criar o gráfico de bolhas
            fig_bubble = create_bubble_chart(all_wells, np_diff_p50, gp_diff_p50, wcoord_df)
            with tag('div'):
                doc.asis(fig_bubble.to_html(full_html=False, include_plotlyjs='cdn'))

            # Ler e processar os arquivos .hphiso
            grouped_hphiso_data = read_and_process_hphiso(directory_path)

            # Criar mapas para cada grupo e percentil
            for group in grouped_hphiso_data.keys():
                with tag('h2'):
                    text(f'{group} Maps')
                for percentile in ['P10', 'P50', 'P90']:
                    fig_hphiso = create_hphiso_map(grouped_hphiso_data, group, percentile)
                    with tag('div'):
                        doc.asis(fig_hphiso.to_html(full_html=False, include_plotlyjs='cdn'))

            # Adicionar a tabela no final
            variables = ['DNpe*', 'DNpe', 'DNp', 'DGp']
            create_new_wells_table_and_excel(doc, tag, text, new_wells, dataframes, base_dataframes, scatter_info_field, scatter_info_well, variables, directory_path)
            entities = [
                'FIELD-PRO',
                'P58-PRO',
                'CDAN-PRO',
                'IPB-PRO',
                'ICSPB-PRO',
            ]
            create_ensemble_csv(entities, dataframes, base_dataframes, scatter_info_field, variables, directory_path)

@log_execution
def create_new_wells_table_and_excel(doc, tag, text, new_wells, dataframes, base_dataframes, scatter_info_field, scatter_info_well, variables, directory_path):
    """
    Cria uma tabela HTML com informações sobre os novos poços e um arquivo Excel correspondente.

    Parameters:
        doc, tag, text: Objetos do yattag para geração de HTML.
        new_wells (list): Lista de novos poços.
        dataframes (dict): Dicionário de DataFrames das simulações atuais.
        base_dataframes (dict): Dicionário de DataFrames das simulações base.
        scatter_info_field (dict): Informações sobre as simulações categorizadas para o campo.
        scatter_info_well (dict): Informações sobre as simulações categorizadas para cada poço.
        variables (list): Lista de variáveis a serem incluídas na tabela.
        directory_path (str): Caminho do diretório onde o arquivo Excel será salvo.

    Returns:
        None (a tabela é adicionada diretamente ao documento HTML e o arquivo Excel é criado)
    """
    data = []

    with tag('h2', id='new_wells'):
        text('Novos Poços')

    with tag('table', id='newWellsTable'):
        with tag('thead'):
            with tag('tr'):
                with tag('th'):
                    text('Novo Poço')
                for var in variables:
                    with tag('th'):
                        text(f'{var} (Campo)')
                    with tag('th'):
                        text(f'{var} (Poço)')

        with tag('tbody'):
            for well in new_wells:
                with tag('tr'):
                    with tag('td'):
                        with tag('a', href=f'{well}_report.html'):
                            text(well)

                    sim_number_field = get_simulation_for_percentile('P50', scatter_info_field)
                    sim_number_well = get_simulation_for_percentile('P50', scatter_info_well[well])

                    values_field = get_final_values(sim_number_field, dataframes, base_dataframes, well)
                    values_well = get_final_values(sim_number_well, dataframes, base_dataframes, well)

                    row_data = [well]
                    for value_field, value_well in zip(values_field, values_well):
                        with tag('td'):
                            text(f"{value_field/1e6:.2f}")
                        with tag('td'):
                            text(f"{value_well/1e6:.2f}")
                        row_data.extend([value_field/1e6, value_well/1e6])

                    data.append(row_data)

    # Criar e salvar o arquivo Excel
    columns = ['Novo Poço']
    for var in variables:
        columns.extend([f'{var} (Campo)', f'{var} (Poço)'])
    df = pd.DataFrame(data, columns=columns)
    excel_path = os.path.join(directory_path, 'ensemble_report.xlsx')

    # Usando openpyxl para mais controle sobre o formato
    wb = Workbook()
    ws = wb.active
    ws.title = "Novos Poços"

    # Adicionar cabeçalho
    for col, header in enumerate(columns, start=1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center')

    # Adicionar dados
    for row, well_data in enumerate(data, start=2):
        for col, value in enumerate(well_data, start=1):
            cell = ws.cell(row=row, column=col, value=value)
            if col > 1:  # Para as colunas numéricas
                cell.number_format = '0.00'
            cell.alignment = Alignment(horizontal='center')

    # Ajustar largura das colunas
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(cell.value)
            except:
                pass
        adjusted_width = (max_length + 2) * 1.2
        ws.column_dimensions[column_letter].width = adjusted_width

    wb.save(excel_path)

    # Adicionar link para download do arquivo Excel
    with tag('p'):
        with tag('a', href=os.path.basename(excel_path), download=os.path.basename(excel_path)):
            text('Download Excel Report')

    # Adicionar scripts necessários para DataTables
    doc.asis('<script type="text/javascript">')
    doc.asis('''
        $(document).ready( function () {
            $('#newWellsTable').DataTable();
        } );
    ''')
    doc.asis('</script>')

@log_execution
def create_ensemble_csv(entities, dataframes, base_dataframes, scatter_info_field, variables, directory_path):
    """
    Cria um arquivo CSV com informações sobre os novos poços.

    Parameters:
        new_wells (list): Lista de novos poços.
        dataframes (dict): Dicionário de DataFrames das simulações atuais.
        base_dataframes (dict): Dicionário de DataFrames das simulações base.
        scatter_info_field (dict): Informações sobre as simulações categorizadas para o campo.
        variables (list): Lista de variáveis a serem incluídas no CSV.
        directory_path (str): Caminho do diretório onde o arquivo CSV será salvo.

    Returns:
        None (o arquivo CSV é criado no diretório especificado)
    """
    data = []

    for well in entities:
        sim_number = get_simulation_for_percentile('P50', scatter_info_field)
        values = get_final_values(sim_number, dataframes, base_dataframes, well)

        row_data = [well]
        for value in values:
            row_data.append(value/1e6)

        data.append(row_data)

    # Criar o DataFrame
    columns = ['Entity'] + variables
    df = pd.DataFrame(data, columns=columns)

    # Salvar como CSV
    csv_path = os.path.join(directory_path, 'ensemble_report.csv')
    df.to_csv(csv_path, index=False)

    print(f"Arquivo CSV criado em: {csv_path}")

@log_execution
def generate_well_html(doc, tag, text, line, well_name, dataframes, base_dataframes, scatter_info, all_distances):
    distances = all_distances[well_name]
    all_wells = distances.index.tolist()

    # Adicionar sidebar
    basic_variables = [
                ('Cumulative Oil Equivalent Production Present Value', "Npe*", "m3"),
                ('Cumulative Oil Equivalent Production', "Npe", "m3"),
                ('Cumulative Oil Production', "Np", "m3"),
                ('Cumulative Gas Production', "Gp", "m3"),
                ('Oil Production Rate', "Qo", "m3/day"),
                ('Basic Sediment and Water', "BSW", "fraction"),
                ('Gas-Oil Ratio', "RGO", "m3/m3"),
            ]
    with tag('div', id='mySidebar', klass='w3-sidebar w3-bar-block w3-collapse w3-card w3-animate-left', style='width:200px;'):
        with tag('button', klass='w3-bar-item w3-button w3-large w3-hide-large', onclick='w3_close()'):
            text('Close ×')
        with tag('a', href='#well_info', klass='w3-bar-item w3-button'):
            text('Well Info')
        with tag('a', href='#representative_selection', klass='w3-bar-item w3-button'):
            text('Representative Selection')
        with tag('a', href='#dnpe_field_vs_well', klass='w3-bar-item w3-button'):
            text('ΔNpe* FIELD-PRO vs Well')
        with tag('a', href='#voip_vs_dnpe_well', klass='w3-bar-item w3-button'):
            text('VOIP vs ΔNpe* Well')
        with tag('a', href='#production_charts', klass='w3-bar-item w3-button'):
            text('Production Charts')
        with tag('a', href='#well_production_chart', klass='w3-bar-item w3-button'):
            text('Well Production Chart')
        with tag('a', href='#liquid_production_rate', klass='w3-bar-item w3-button'):
            text('Liquid Production Rate')
        with tag('a', href='#bhp', klass='w3-bar-item w3-button'):
            text('BHP')
        with tag('a', href='#ip', klass='w3-bar-item w3-button'):
            text('IP')
        for title, variable, unit in basic_variables:
            with tag('a', href=f'#{title.lower().replace(" ", "_")}', klass='w3-bar-item w3-button'):
                text(title)
        with tag('a', href='#qoe', klass='w3-bar-item w3-button'):
            text('Qoe')

    # Conteúdo principal
    with tag('div', klass='w3-main', style='margin-left:200px'):
        with tag('div', klass='w3-teal'):
            with tag('button', klass='w3-button w3-teal w3-xlarge w3-hide-large', onclick='w3_open()'):
                text('☰')
            with tag('div', klass='w3-container'):
                with tag('h1'):
                    text(f'Well Report: {well_name}')

        with tag('div', klass='w3-container'):
            # Informações do poço
            with tag('h2', id='well_info'):
                text('Well Information')
            with tag('p'):
                text(f"Well Name: {well_name}")

            # Seleção dos representativos
            with tag('h2', id='representative_selection'):
                text('Representative Selection')
            with tag('p'):
                lista_vars = [f"{var['entity']}.{var['name']}" for var in scatter_info['variables']]
                text(f"Variáveis usadas para seleção de representativo: {', '.join(lista_vars)}")
            with tag('p'):
                text(f"Valor de dpercentile: {scatter_info['dpercentile']:.2f}")
            with tag('p'):
                text("Número de simulações em cada grupo:")
            with tag('ul'):
                for group in ['P10', 'P50', 'P90']:
                    with tag('li'):
                        text(f"{group}: {scatter_info['counts'][group]} simulações")
            with tag('p'):
                text("Simulações em cada grupo:")
            for group in ['P10', 'P50', 'P90']:
                with tag('h4'):
                    text(f"Grupo {group}:")
                with tag('ul'):
                    for sim in scatter_info['simulations'][group]:
                        with tag('li'):
                            text(f"Simulação {sim}")

            # Gráfico ΔNpe* FIELD-PRO vs ΔNpe* do novo poço
            with tag('h2', id='dnpe_field_vs_well'):
                text(f'ΔNpe* FIELD-PRO vs ΔNpe* {well_name}')
            try:
                fig_dnpepv_field_vs_new_well = create_scatter_plot(scatter_info, "FIELD-PRO.Npe*(m3)", f"{well_name}.Npe*(m3)")
                with tag('div'):
                    doc.asis(fig_dnpepv_field_vs_new_well.to_html(full_html=False, include_plotlyjs='cdn'))
            except Exception as e:
                text(f"Gráfico indisponível.")
                print(f"Erro ao criar gráfico ΔNpe* FIELD-PRO vs ΔNpe* {well_name}: {str(e)}")

            # Gráfico VOIP vs ΔNpe* do novo poço
            with tag('h2', id='voip_vs_dnpe_well'):
                text(f'VOIP vs ΔNpe* {well_name}')
            try:
                fig_voip_vs_new_well_dnpepv = create_scatter_plot(scatter_info, "FIELD.VOIP(m3)", f"{well_name}.Npe*(m3)")
                with tag('div'):
                    doc.asis(fig_voip_vs_new_well_dnpepv.to_html(full_html=False, include_plotlyjs='cdn'))
            except Exception as e:
                text(f"Gráfico indisponível.")
                print(f"Erro ao criar gráfico VOIP vs ΔNpe* {well_name}: {str(e)}")


            # Outros gráficos
            with tag('h2', id='production_charts'):
                text('Production Charts')
            # Select simulations from each region
            simulations_p90 = scatter_info['simulations']['P90']
            simulations_p50 = scatter_info['simulations']['P50']
            simulations_p10 = scatter_info['simulations']['P10']
            # Pick one simulation from each region
            sim_p90 = simulations_p90[0]
            sim_p50 = simulations_p50[0]
            sim_p10 = simulations_p10[0]
            # Calculate production differences for the selected simulations
            np_diff_p90, gp_diff_p90 = get_production_difference_for_simulation(sim_p90, dataframes, base_dataframes, all_wells)
            np_diff_p50, gp_diff_p50 = get_production_difference_for_simulation(sim_p50, dataframes, base_dataframes, all_wells)
            np_diff_p10, gp_diff_p10 = get_production_difference_for_simulation(sim_p10, dataframes, base_dataframes, all_wells)
            # Create the updated well production chart
            fig_well_production = create_well_production_chart(
                all_wells,
                np_diff_p90, gp_diff_p90,
                np_diff_p50, gp_diff_p50,
                np_diff_p10, gp_diff_p10,
                distances
            )
            with tag('h3', id='well_production_chart'):
                text('Well Production Chart')
                with tag('div'):
                    doc.asis(fig_well_production.to_html(full_html=False, include_plotlyjs='cdn'))

            print("Calculating Ql for the new well...")
            ql_var = calculate_new_var(
                dataframes1=dataframes,
                dataframes2=dataframes,
                entity=well_name,
                var1="Qo",
                var2="Qw",
                unit1="m3/day",
                unit2="m3/day",
                factor1=1,
                factor2=1,
                operation="+",
                var_out="Ql",
                unit_out="m3/day"
            )
            print("Creating Ql time series plot...")
            fig_ql_time_series = create_new_var_time_series_plot(ql_var, scatter_info, well_name)
            with tag('h3', id='liquid_production_rate'):
                text('Liquid Production Rate')
                with tag('div'):
                    doc.asis(fig_ql_time_series.to_html(full_html=False, include_plotlyjs='cdn'))

            fig_bhp_time_series = create_time_series_plot(dataframes, scatter_info, well_name, "BHP", "kPa")
            with tag('h3', id='bhp'):
                text('BHP')
                with tag('div'):
                    doc.asis(fig_bhp_time_series.to_html(full_html=False, include_plotlyjs='cdn'))

            print("Calculating Pressure Difference for the new well...")
            # Calculate pressure difference: (Well Mobility-Weighted Datum Pressure - 9P) - BHP
            pressure_diff_var = calculate_new_var(
                dataframes1=dataframes,
                dataframes2=dataframes,
                entity=well_name,
                var1="Well Mobility-Weighted Datum Pressure - 9P",
                var2="BHP",
                unit1="kPa",
                unit2="kPa",
                factor1=1,
                factor2=1,
                operation="-",
                var_out="Pressure Difference",
                unit_out="kPa"
            )
            print("Calculating IP for the new well...")
            # Calculate IP = Ql / Pressure Difference * 98.0665
            ip_var = calculate_combined_var(
                new_var1=ql_var,
                new_var2=pressure_diff_var,
                factor1=98.0665,
                factor2=1,
                operation="/",
                var_out="IP",
                unit_out="(m3/day)(kgf/cm2)"
            )
            print("Creating IP time series plot...")
            fig_ip_time_series = create_new_var_time_series_plot(ip_var, scatter_info, well_name)
            with tag('h3', id='ip'):
                text('IP')
                with tag('div'):
                    doc.asis(fig_ip_time_series.to_html(full_html=False, include_plotlyjs='cdn'))

            for title, variable, unit in basic_variables:
                with tag('h3', id=title.lower().replace(" ", "_")):
                    text(title)
                fig = create_time_series_plot(dataframes, scatter_info, well_name, variable, unit)
                if fig is not None:
                    with tag('div'):
                        doc.asis(fig.to_html(full_html=False, include_plotlyjs='cdn'))
                else:
                    with tag('p'):
                        text(f"No data available for {title}")

            print("Calculating Qoe for the new well...")
            qoe_var = calculate_new_var(dataframes, dataframes, well_name, "Qo", "Qg", "m3/day", "m3/day", 1, 1/1e3, "+", "Qoe", "m3/day")
            print("Creating Qoe time series plot...")
            fig_qoe_time_series = create_new_var_time_series_plot(qoe_var, scatter_info, well_name)
            with tag('h3', id="qoe"):
                text('Qoe')
                with tag('div'):
                    doc.asis(fig_qoe_time_series.to_html(full_html=False, include_plotlyjs='cdn'))

import copy
import pandas as pd
import numpy as np

@log_execution
def find_first_nonzero_date(curr_dataframes, entity, variable, unit, tolerance=1e-12):
    """
    Finds the earliest date in each simulation DataFrame at which the specified
    (entity, variable, unit) column becomes nonzero (and not NaN).

    Parameters:
    -----------
    curr_dataframes : dict
        Dictionary of simulation dataframes, keyed by simulation number (or name).
    entity -> e.g. "FIELD-PRO" or "WELL-10"
    var   -> e.g. "Npe" or "VOIP" or "BHP"
    unit   -> e.g. "m3" or "kPa"
    If variable == 'VOIP', we automatically map that to the column "Oil Volume SC SCTR".
    tolerance : float
        Values whose absolute magnitude is below this tolerance are treated as zero.

    Returns:
    --------
    dict
        A dictionary of {simulation_number: first_date},
        where 'first_date' is a Timestamp or None if no nonzero value is found.
    """
    # Because VOIP columns often have a special internal name
    variable = "Oil Volume SC SCTR" if variable == 'VOIP' else variable

    results = {}
    for number, df in curr_dataframes.items():
        # If the column does not exist in this simulation, skip
        if (entity, variable, unit) not in df.columns:
            results[number] = None
            continue

        series = df[(entity, variable, unit)]

        # Create a boolean mask for "non-NaN" AND "absolute value > tolerance"
        nonzero_mask = series.notnull() & (series.abs() > tolerance)

        # If no values are nonzero in the entire time series, store None
        if not nonzero_mask.any():
            results[number] = None
        else:
            # The first date is the index of the first True entry
            # We can get it by filtering the series:
            first_nonzero_date = series[nonzero_mask].index[0]
            results[number] = first_nonzero_date

    return results

@log_execution
def calculate_values(curr_dataframes, base_dataframes, var, n_periods):
    """
    Calcula (valor_projeto - valor_base) em uma determinada data/offset,
    onde n_periods pode ser:
      - int >= 0  => n meses após primeira data válida
      - int < 0   => deslocamento negativo relativo à última data válida
      - "YYYY-MM-DD" => data absoluta (datetime)

    Parâmetros
    ----------
    curr_dataframes : dict
        Dicionário {sim_number: DataFrame}
    base_dataframes : dict ou None
        Dicionário {sim_number: DataFrame} ou None, se não há base
    var : dict
        Exemplo:
            {
                'values': {},
                'entity': 'FIELD-PRO',
                'name': 'Npe*',
                'unit': 'm3',
                'offset_str': offset_str,
            }
    n_periods : int ou str
        Pode ser um inteiro (positivo/negativo) ou uma string no formato yyyy-mm-dd.

    Retorna
    -------
    var_out : dict
        Mesmo formato que 'var', mas com 'values' preenchido para cada sim_number.
    """

    var_out = copy.deepcopy(var)
    var_name = "Oil Volume SC SCTR" if var['name'] == 'VOIP' else var['name']

    # 1) Convertemos n_periods para algo manipulável: int ou datetime
    date_mode = False
    offset_int = None
    dt = None
    if isinstance(n_periods, int):
        # É offset em meses
        offset_int = n_periods
    else:
        # Se n_periods é string, tentamos converter para inteiro ou data
        try:
            offset_int = int(n_periods)
        except ValueError:
            # Então assumimos que seja data no formato YYYY-MM-DD
            try:
                dt = datetime.strptime(n_periods, "%Y-%m-%d")
                date_mode = True
            except ValueError:
                raise ValueError(f"[calculate_values] '{n_periods}' não é int nem data YYYY-MM-DD válido.")

    @log_execution
    def get_index_from_series(series, offset_value, date_mode=False):
        """
        Devolve o Timestamp alvo onde devemos pegar o valor em 'series',
        que pode ser:
            - date_mode=True => offset_value é uma datetime
            - date_mode=False => offset_value é int (n meses após/before algo)
        Se o índice não existir em series, retorna None.
        """
        # Exemplo: só consideramos pontos em que a série não é nula.
        non_empty_mask = series.notnull() & (series.abs() > 1e-14)
        if not non_empty_mask.any():
            return None  # série toda vazia

        if date_mode:
            # offset_value é datetime
            dt_target = offset_value
        else:
            # offset_value é int (p.ex. +2, -1, etc.)
            if offset_value >= 0:
                first_valid = series[non_empty_mask].index[0]
                dt_target = first_valid + pd.DateOffset(months=offset_value)
            else:
                last_valid = series[non_empty_mask].index[-1]
                k = abs(offset_value)
                dt_target = last_valid - pd.DateOffset(months=(k - 1))

        # Verifica se dt_target está no índice:
        if dt_target not in series.index:
            return None

        return dt_target

    var_out['values'] = {}

    # 2) Iteramos sobre cada sim_number do ensemble e pegamos o valor
    for sim_number, curr_df in curr_dataframes.items():

        # Verifica se existe a coluna (entity, var_name, unit)
        if (var['entity'], var_name, var['unit']) not in curr_df.columns:
            # Nenhuma informação dessa var nesse sim
            continue

        series = curr_df[(var['entity'], var_name, var['unit'])]

        # Determina qual offset_value vamos enviar p/ get_index_from_series
        # Se for date_mode, usamos dt; se não, usamos offset_int
        offset_value = dt if date_mode else offset_int

        # 3) Encontramos o índice alvo
        index_candidate = get_index_from_series(
            series,
            offset_value=offset_value,
            date_mode=date_mode
        )
        if index_candidate is None:
            # não existe esse índice -> pular
            continue

        curr_value = series.loc[index_candidate]

        # 4) Calcula o valor da base, se houver
        if base_dataframes is None:
            base_value = 0.0
        else:
            if sim_number not in base_dataframes:
                continue  # não tem esse sim na base
            base_df = base_dataframes[sim_number]
            # if var['name'] == 'VOIP':
            if var['name'] in ['VOIP', "Oil Volume SC SCTR"]:
                base_value = 0.0
            else:
                if (var['entity'], var_name, var['unit']) in base_df.columns:
                    # base series
                    base_series = base_df[(var['entity'], var_name, var['unit'])]
                    # chamamos get_index_from_series ou algo similar,
                    # ou tentamos usar o mesmo index_candidate se for válido:
                    if index_candidate in base_series.index:
                        base_value = base_series.loc[index_candidate]
                    else:
                        base_value = 0.0
                else:
                    base_value = 0.0

        var_out['values'][sim_number] = curr_value - base_value

    return var_out

@log_execution
def find_new_wells(directory_path, base_path):
    """
    Identifica novos poços comparando os arquivos .wcoord no diretório atual e no diretório base.

    Parameters:
        directory_path (str): Caminho para o diretório atual.
        base_path (str): Caminho para o diretório base.

    Returns:
        new_wells (list): Lista de nomes dos novos poços.
    """
    current_file = glob.glob(os.path.join(directory_path, "*.wcoord"))[0]
    current_df = pd.read_csv(current_file)
    current_wells = set(current_df['Poço'])

    base_file = None
    base_df = None
    base_wells = None
    if base_path != None:
        base_file = glob.glob(os.path.join(base_path, "*.wcoord"))[0]
        # Lê os arquivos .wcoord
        base_df = pd.read_csv(base_file)
        # Obtém os conjuntos de poços de cada arquivo
        base_wells = set(base_df['Poço'])

    new_wells = None
    if base_path != None:
        # Identifica os novos poços
        new_wells = list(current_wells - base_wells)
    else:
        new_wells = list(current_wells)

    return new_wells

@log_execution
def get_all_wells(directory_path):
    """
    Obtém todos os poços listados no arquivo .wcoord do diretório especificado.

    Parameters:
        directory_path (str): Caminho para o diretório contendo o arquivo .wcoord.

    Returns:
        all_wells (list): Lista de nomes de todos os poços.
    """
    # Encontra o primeiro arquivo .wcoord no diretório
    wcoord_file = glob.glob(os.path.join(directory_path, "*.wcoord"))[0]

    # Lê o arquivo .wcoord
    df = pd.read_csv(wcoord_file)

    # Obtém a lista de todos os poços
    all_wells = df['Poço'].tolist()

    return all_wells

@log_execution
def create_html_structure(doc, tag, text, content_generator, title):
    doc.asis('<!DOCTYPE html>')
    with tag('html', lang='en'):
        with tag('head'):
            doc.stag('meta', charset='utf-8')
            doc.stag('meta', name='viewport', content='width=device-width, initial-scale=1')
            with tag('title'):
                text(title)
            doc.stag('link', rel='stylesheet', href='https://www.w3schools.com/w3css/4/w3.css')
            doc.asis('<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.25/css/jquery.dataTables.css">')
            doc.asis('<script type="text/javascript" charset="utf8" src="https://code.jquery.com/jquery-3.6.0.min.js"></script>')
            doc.asis('<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.js"></script>')
            with tag('style'):
                text('''
                    body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 0; }
                    h1, h2, h3 { color: #333; }
                    .plotly-graph-div { margin-bottom: 20px; }
                ''')
        with tag('body'):
            content_generator()
            with tag('script'):
                doc.asis('''
                    function w3_open() {
                        document.getElementById("mySidebar").style.display = "block";
                    }
                    function w3_close() {
                        document.getElementById("mySidebar").style.display = "none";
                    }
                    // Close sidebar on small screens when clicking outside of it
                    document.addEventListener("click", function(event) {
                        var sidebar = document.getElementById("mySidebar");
                        var openNavButton = document.querySelector(".w3-button.w3-teal.w3-xlarge.w3-hide-large");
                        if (!sidebar.contains(event.target) && event.target !== openNavButton && window.innerWidth < 993) {
                            w3_close();
                        }
                    });
                ''')

@log_execution
def get_simulation_for_percentile(percentile, scatter_info):
    return scatter_info['simulations'][percentile][0]

@log_execution
def get_final_values(sim_number, dataframes, base_dataframes, well_name):
    var_npe = TimeVar("Npe*", "m3")
    var_np = TimeVar("Np", "m3")
    var_gp = TimeVar("Gp", "m3")
    dnpe_star = get_last_difference_for_simulation(sim_number, dataframes, base_dataframes, [well_name], var_npe)[0]
    dnp = get_last_difference_for_simulation(sim_number, dataframes, base_dataframes, [well_name], var_np)[0]
    dgp = get_last_difference_for_simulation(sim_number, dataframes, base_dataframes, [well_name], var_gp)[0]/1e3
    dnpe = dnp + dgp
    return dnpe_star, dnpe, dnp, dgp

@log_execution
def read_and_process_hphiso(directory_path):
    hphiso_files = glob.glob(os.path.join(directory_path, "*.hphiso"))
    grouped_data = {}

    for file_path in hphiso_files:
        filename = os.path.basename(file_path)
        match = re.search(r'(.+?)_(\d{3})\.(.+?)\.hphiso', filename)
        if match:
            sim_name = match.group(1)
            sim_number = match.group(2)
            group = match.group(3)

            with open(file_path, 'rb') as f:
                data = pickle.load(f)

            if group not in grouped_data:
                grouped_data[group] = {}
            grouped_data[group][sim_number] = data

    return grouped_data

@log_execution
def create_hphiso_map(grouped_data, group, percentile='P50'):
    simulations = grouped_data[group]
    sim_numbers = list(simulations.keys())

    if percentile == 'P50':
        sim_number = sim_numbers[len(sim_numbers) // 2]
    elif percentile == 'P10':
        sim_number = sim_numbers[int(len(sim_numbers) * 0.1)]
    elif percentile == 'P90':
        sim_number = sim_numbers[int(len(sim_numbers) * 0.9)]
    else:
        raise ValueError("Percentile must be 'P10', 'P50', or 'P90'")

    data = simulations[sim_number].T

    # Calcular os valores mínimo e máximo globais
    global_min = float(data.min().values)
    global_max = float(data.max().values)

    fig = make_subplots(rows=1, cols=1, subplot_titles=[f"{group} - {percentile}"])

    # Criar malhas para i e j
    i_mesh, j_mesh = np.meshgrid(np.arange(data.shape[1]), np.arange(data.shape[2]), indexing='ij')

    for t in range(len(data.Date)):
        fig.add_trace(
            go.Heatmap(
                z=data.isel(Date=t).values,
                x=data.xc.values[0],
                y=data.yc.values[:, 0],
                colorscale='Turbo',
                showscale=True,
                visible=False,
                zmin=global_min,
                zmax=global_max,
                hovertemplate='x: %{x:.2f}<br>y: %{y:.2f}<br>HPhiSo: %{z:.2f}<br>i: %{customdata[0]}<br>j: %{customdata[1]}<extra></extra>',
                customdata=np.dstack((i_mesh, j_mesh))
            ),
            row=1,
            col=1
        )

    # Tornar o primeiro frame visível
    fig.data[0].visible = True

    # Criar e adicionar o slider
    steps = []
    for i in range(len(fig.data)):
        step = dict(
            method="update",
            args=[{"visible": [False] * len(fig.data)},
                  {"title": f"{group} - {percentile} - Date: {data.Date.values[i]}"}],
            label=str(data.Date.values[i])
        )
        step["args"][0]["visible"][i] = True
        steps.append(step)

    sliders = [dict(
        active=0,
        currentvalue={"prefix": "Date: "},
        pad={"t": 50},
        steps=steps
    )]

    # Calcular o aspecto para manter a proporção
    x_range = data.xc.values[0].max() - data.xc.values[0].min()
    y_range = data.yc.values[:, 0].max() - data.yc.values[:, 0].min()
    aspect_ratio = y_range / x_range

    fig.update_layout(
        sliders=sliders,
        title=f"{group} - {percentile}",
        xaxis_title='X Coordinate (m)',
        yaxis_title='Y Coordinate (m)',
        xaxis=dict(scaleanchor="y", scaleratio=1),
        yaxis=dict(scaleanchor="x", scaleratio=aspect_ratio),
        height=600,  # Ajuste conforme necessário
        width=800,   # Ajuste conforme necessário
    )

    return fig

@log_execution
def find_new_wells_from_excel(directory_path, initial_positions_path, max_attempts=10, wait_time=5):
    # Extrair o número da tentativa do nome do diretório
    attempt_name = os.path.basename(directory_path.rstrip(os.path.sep))
    print(f"Nome do diretório: {attempt_name}")

    if attempt_name.lower() == "BASE".lower():
        return find_new_wells(directory_path, None)
    else:
        # Usar expressão regular para encontrar o número da tentativa
        match = re.search(r'ATTEMPT_(\d+)', attempt_name)
        if match:
            n_tentativa = int(match.group(1))
            print(f"Número da tentativa extraído: {n_tentativa}")
        else:
            print(f"Não foi possível extrair o número da tentativa do diretório: {directory_path}")
            raise ValueError(f"Formato de nome de diretório inválido: {attempt_name}")

    for attempt in range(max_attempts):
        try:
            # Tentar abrir o arquivo Excel
            print(f"Tentando abrir o arquivo Excel: {initial_positions_path}")
            df = pd.read_excel(initial_positions_path)

            # Converter a coluna 'Tentativa' para string
            df['Tentativa'] = df['Tentativa'].astype(str)

            # Encontrar a linha correspondente à tentativa atual
            row = df[df['Tentativa'] == str(n_tentativa)]

            if row.empty:
                print(f"Tentativa {n_tentativa} não encontrada no arquivo Excel.")
                print(f"Valores únicos na coluna 'Tentativa': {df['Tentativa'].unique()}")
                raise ValueError(f"Tentativa {n_tentativa} não encontrada no arquivo Excel.")

            # Extrair os nomes dos poços
            if 'Locação' not in row.columns:
                print(f"Coluna 'Locação' não encontrada. Colunas disponíveis: {row.columns}")
                raise ValueError("Coluna 'Locação' não encontrada no arquivo Excel.")

            locacao = row['Locação'].values[0]
            if pd.isna(locacao):
                print(f"Valor na coluna 'Locação' é NaN para a tentativa {n_tentativa}")
                raise ValueError(f"Valor na coluna 'Locação' é NaN para a tentativa {n_tentativa}")

            new_wells = [well.strip() for well in str(locacao).split(',') if well.strip()]

            if not new_wells:
                print(f"Nenhum poço encontrado após processar o valor da coluna 'Locação': {locacao}")
                raise ValueError(f"Nenhum poço encontrado para a tentativa {n_tentativa}")

            print(f"Novos poços encontrados: {new_wells}")
            return new_wells

        except PermissionError:
            print(f"O arquivo Excel está aberto. Tentativa {attempt + 1} de {max_attempts}. Aguardando {wait_time} segundos...")
            time.sleep(wait_time)
        except Exception as e:
            print(f"Erro ao ler o arquivo Excel: {str(e)}")
            raise

    raise TimeoutError(f"Não foi possível acessar o arquivo Excel após {max_attempts} tentativas.")

# ======================
# FUNCAO PRINCIPAL
# ======================
@log_execution
def main():
    # Verificar comandos de documentação primeiro
    documentacao()

    parser = argparse.ArgumentParser(description="Generate ensemble report")
    parser.add_argument("directory_path", help="Path to the directory containing the data files")
    parser.add_argument("--initial_positions_path", help="Path to the Excel file containing initial positions")
    args = parser.parse_args()

    directory_path = args.directory_path
    initial_positions_path = args.initial_positions_path

    if os.path.basename(directory_path.rstrip("/")) == "BASE":
        print("The specified directory is BASE. No processing will be performed.")
        return 1
    base_path = os.path.join(directory_path, "..", "BASE")

    print("Starting processing...")
    print("Identifying new wells...")
    if initial_positions_path:
        print("Identifying new wells from initial positions file...")
        print(f"Directory path: {directory_path}")
        print(f"Initial positions file path: {initial_positions_path}")
        try:
            new_wells = find_new_wells_from_excel(directory_path, initial_positions_path)
            print(f"Novos poços identificados: {new_wells}")
        except Exception as e:
            print(f"Erro ao identificar novos poços: {str(e)}")
            import traceback
            traceback.print_exc()
            return 1
    else:
        print("Identifying new wells...")
        new_wells = find_new_wells(directory_path, base_path)
    if not new_wells:
        print("No new wells found.")
        return 1

    # Read the distance file
    print("Reading distances file...")
    all_distances = read_dist_file(directory_path)

    print("Reading data files...")
    dataframes = read_data_files(directory_path)
    base_dataframes = read_data_files(base_path)
    if not dataframes or not base_dataframes:
        print("Error: No data files found in the specified directories.")
        return 1

    print("Calculating field values...")
    voip_field = {
        'values': {},
        'entity': "FIELD",
        'name': "VOIP",
        'unit': "m3",
    }
    voip_field = calculate_values(dataframes, base_dataframes, voip_field, 0)
    dnpepv_field = {
        'values': {},
        'entity': "FIELD-PRO",
        'name': "Npe*",
        'unit': "m3",
    }
    dnpepv_field = calculate_values(dataframes, base_dataframes, dnpepv_field, -1)
    scatter_info_field = calculate_scatter_info(dnpepv_field, voip_field)

    print("Calculating well values...")
    scatter_info_well = {}
    for well in new_wells:
        dnpepv_well = {
            'values': {},
            'entity': well,
            'name': "Npe*",
            'unit': "m3",
        }
        dnpepv_well = calculate_values(dataframes, base_dataframes, dnpepv_well, -1)
        scatter_info_well[well] = calculate_scatter_info(dnpepv_well, dnpepv_field, voip_field)
        print(f"dpercentile({well})={scatter_info_well[well]['dpercentile']}")

    print("Generating HTML reports...")
    generate_html_reports(directory_path, dataframes, base_dataframes, new_wells, scatter_info_field, scatter_info_well, all_distances)

    print("Processing complete.")

if __name__ == "__main__":
    main()

# Implementar o que esta em __status__['em_desenvolvimento'].
