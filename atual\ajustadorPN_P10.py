import pandas as pd
import os
import datetime
from Auxiliares.modules.logging_module import log_execution

# Importe aqui o dicionário de limites
from limitesPN import limites

@log_execution
def ler_excel(caminho_arquivo):
    """Lê o arquivo Excel e retorna um DataFrame"""
    print(f"Lendo arquivo Excel: {caminho_arquivo}")
    return pd.read_excel(caminho_arquivo)

@log_execution
def ler_ordem_projetos(caminho_arquivo):
    """Lê o arquivo de texto com a ordem dos projetos"""
    print(f"Lendo ordem de projetos: {caminho_arquivo}")
    resultado = []
    with open(caminho_arquivo, 'r') as arquivo:
        for linha in arquivo:
            linha_limpa = linha.strip()
            if linha_limpa and not linha_limpa.startswith('#'):
                resultado.append(linha_limpa)
    return resultado

@log_execution
def str_to_date(date_str):
    """Converte string para date object"""
    try:
        return datetime.datetime.strptime(date_str.strip(), "%Y-%m-%d").date()
    except:
        return None

@log_execution
def get_limit_for_date(limite_valor, data_consulta):
    """
    Retorna o limite numérico correspondente à data_consulta,
    dado que limite_valor pode ser:
      - Um inteiro/float fixo
      - Um dicionário com chaves que podem definir intervalos de data
    """
    # Garante que é datetime.date
    if isinstance(data_consulta, pd.Timestamp):
        data_consulta = data_consulta.date()

    # Se o valor for numérico (int ou float), simplesmente retorne
    if isinstance(limite_valor, (int, float)):
        return limite_valor

    # Caso seja um dicionário, precisamos verificar cada faixa
    # Exemplo de chaves possíveis:
    #   ":2029-01-01"   -> até 2029-01-01 (inclusive)
    #   "2029-02-01:"   -> a partir de 2029-02-01 (inclusive)
    #   "2029-01-01:2029-12-01" -> entre 2029-01-01 e 2029-12-01 (inclusive)
    #   "2030-01-01"    -> somente na data 2030-01-01
    #   etc.
    for intervalo_str, valor_max in limite_valor.items():
        # Tentar detectar se é um intervalo ou data única
        if ":" in intervalo_str:
            partes = intervalo_str.split(":")
            # Exemplos de partes:
            #   ["", "2029-01-01"] -> significa :2029-01-01
            #   ["2029-02-01", ""] -> significa 2029-02-01:
            #   ["2029-01-01", "2029-12-01"] -> 2029-01-01:2029-12-01
            start_str, end_str = partes[0].strip(), partes[1].strip()

            start_date = str_to_date(start_str) if start_str else None
            end_date   = str_to_date(end_str)   if end_str else None

            # Verifica se data_consulta está dentro do intervalo
            # Se start_date for None, significa sem limite inferior
            # Se end_date for None, significa sem limite superior
            if (start_date is None or data_consulta >= start_date) and \
               (end_date is None   or data_consulta <= end_date):
                return valor_max

        else:
            # Pode ser uma data exata (ex: "2030-01-01")
            data_limite = str_to_date(intervalo_str)
            if data_limite == data_consulta:
                return valor_max

    # Se não encontrar nenhum intervalo correspondente, pode retornar None
    # ou algum valor default. Aqui retornaremos None, indicando "não definido".
    return None

@log_execution
def apply_priority_sorting(df, sort_by, priority=None):
    """
    Aplica ordenação prioritária ao DataFrame com base nas regras fornecidas.
    Ordena primeiro por sort_by (decrescente), depois por prioridades definidas (ascendente).
    """
    # Criar cópia para evitar warnings de modificação
    df_sorted = df.copy()

    # Se não houver prioridade, ordenar apenas por sort_for
    if not priority:
        return df_sorted.sort_values(sort_by, ascending=False)

    # Processar regras de prioridade
    sort_columns = sort_by.copy()
    ascending = [False] * len(sort_by)

    for col, priority_str in priority.items():
        # Criar dicionário de prioridades para a coluna
        priority_dict = {}
        for pair in priority_str.split(','):
            key, val = pair.strip().split('=')
            priority_dict[key] = int(val)

        # Adicionar coluna temporária com valores de prioridade
        priority_col = f"{col}_priority"
        df_sorted[priority_col] = (
            df_sorted[col]
            .map(priority_dict)
            .fillna(5000)  # Valores não especificados recebem prioridade mais baixa
        )

        # sort_columns.append(priority_col)
        # ascending.append(True)  # Prioridades menores primeiro
        sort_columns.insert(0,priority_col)
        ascending.insert(0,True)

    # Ordenar e remover colunas temporárias
    df_sorted = df_sorted.sort_values(sort_columns, ascending=ascending)
    df_sorted = df_sorted.drop(columns=[c for c in df_sorted.columns if '_priority' in c])

    # if 'BLA' in df['Campo'].values:
    #     df.to_excel(r"L:\res\campos\jubarte\er\er03\PN\2630\C1\deletar\df.xlsx", index=True)
    #     print('df')
    #     df_sorted.to_excel(r"L:\res\campos\jubarte\er\er03\PN\2630\C1\deletar\df_sorted.xlsx", index=True)
    #     print('df_sorted')
    #     print(f'sort_by={sort_by}')
    #     print(f'priority={priority}')
    #     input()

    return df_sorted

@log_execution
def check_limits_for_platform_qw(df_plataforma, plataforma, projeto_in, coluna, priority=None):
    """
    Agrupa df_plataforma por 'Date', soma as colunas numéricas e compara
    com os limites de Qw. Se exceder, gera linhas de ajuste negativas,
    reduzindo primeiro os poços de maior BSW.

    Retorna df_plataforma atualizado (contendo as novas linhas de ajuste).
    """

    if plataforma not in limites:
        print(f"UEP '{plataforma}' não encontrada em limitesPN.py")
        return df_plataforma  # retorna inalterado

    # Garante que a coluna 'Date' é datetime.date
    if not pd.api.types.is_datetime64_any_dtype(df_plataforma['Date']):
        df_plataforma['Date'] = pd.to_datetime(df_plataforma['Date']).dt.date

    # Se a coluna "Classe" não existir, crie e marque tudo como Original
    if "Classe" not in df_plataforma.columns:
        df_plataforma["Classe"] = "Original"

    # Colunas de interesse para verificar o limite (Qw)
    cols_existentes = [col for col in ["Qw Pot(m3/day)"] if col in df_plataforma.columns]
    if not cols_existentes:
        print(f"Nenhuma coluna 'Qw Pot(m3/day)' encontrada para a plataforma {plataforma}.")
        return df_plataforma

    # Agrupa por Date e soma as colunas de interesse
    df_agg = df_plataforma.groupby("Date")[cols_existentes].sum().reset_index()

    # Para cada data, verifica se Qw excede o limite
    for idx, row in df_agg.iterrows():
        data_row = row["Date"]

        limit_Qw = get_limit_for_date(limites[plataforma].get("Qw"), data_row)
        if limit_Qw is None:
            print(f"Não foi encontrado limite para a plataforma '{plataforma}' na data {data_row}")
            continue  # não tem limite definido, pula

        valor_somado_Qw = row.get("Qw Pot(m3/day)", 0.0)

        # Verifica se excede
        exceed = valor_somado_Qw - limit_Qw
        if exceed > 0.01:
            # 1) Filtra as linhas para a data em questão
            mask_data = (df_plataforma["Date"] == data_row)
            subDF = df_plataforma.loc[mask_data].copy()

            # 2) Colunas para agrupamento
            group_cols = [
                "Versao", "Cenario", "Campo", "ZP", "NZP", "UEP",
                "Well", "Date",
            ]
            group_cols = [col for col in group_cols if col in subDF.columns]

            # Colunas para soma
            sum_cols = [
                "Qo Pot(m3/day)", "Qw Pot(m3/day)", "Qg Pot(mil m3/day)",
                "Qgco2 Pot(mil m3/day)", "Qghc Pot(mil m3/day)",
                "Qwi Pot(m3/day)", "Qgi Pot(mil m3/day)", "Qgl Pot(mil m3/day)",
                "Qgco2i Pot(mil m3/day)", "Qghci Pot(mil m3/day)",
            ]
            sum_cols_exist = [c for c in sum_cols if c in subDF.columns]

            # Colunas adicionais para agregação
            agg_dict = {col: 'sum' for col in sum_cols_exist}
            if 'Projeto' in subDF.columns:
                agg_dict['Projeto'] = 'first'
            if 'IUPI' in subDF.columns:
                agg_dict['IUPI'] = 'first'

            # Agrupa e agrega
            subDF_agg = subDF.groupby(group_cols, as_index=False).agg(agg_dict)

            # Define a coluna (Projeto ou IUPI) com projeto_in
            subDF_agg[coluna] = projeto_in

            # Prepara subDF para cálculos
            subDF_agg["Qo_i"] = subDF_agg.get("Qo Pot(m3/day)", 0.0)
            subDF_agg["Qw_i"] = subDF_agg.get("Qw Pot(m3/day)", 0.0)
            subDF_agg["Qg_i"] = subDF_agg.get("Qg Pot(mil m3/day)", 0.0)
            subDF_agg["Qgco2_i"] = subDF_agg.get("Qgco2 Pot(mil m3/day)", 0.0)
            subDF_agg["Qghc_i"] = subDF_agg.get("Qghc Pot(mil m3/day)", 0.0)

            # Calcula BSW e RGO
            @log_execution
            def calc_bsw(row_poco):
                denom = row_poco["Qo_i"] + row_poco["Qw_i"]
                return row_poco["Qw_i"] / denom if denom > 0 else 0.0

            @log_execution
            def calc_rgo(row_poco):
                return row_poco["Qg_i"] / row_poco["Qo_i"] if row_poco["Qo_i"] > 0 else 0.0

            subDF_agg["BSW"] = subDF_agg.apply(calc_bsw, axis=1)
            subDF_agg["RGO"] = subDF_agg.apply(calc_rgo, axis=1)

            # # Ordena decrescentemente por BSW
            # subDF_agg.sort_values(by="BSW", ascending=False, inplace=True)
            subDF_agg = apply_priority_sorting(subDF_agg, ['BSW'], priority)

            adjustment_rows = []
            still_to_cut = exceed

            for irow, row_poco in subDF_agg.iterrows():
                if still_to_cut <= 0.01:
                    break

                Qw_i = row_poco["Qw_i"]
                BSW_i = row_poco["BSW"]
                RGO_i = row_poco["RGO"]
                Qg_i = row_poco["Qg_i"]
                Qgco2_i = row_poco["Qgco2_i"]
                # Qghc_i = row_poco["Qghc_i"]

                if Qw_i <= 0:
                    continue

                reduce_amount = min(Qw_i, still_to_cut)
                Qo_reduce = reduce_amount * (1 - BSW_i) / BSW_i if BSW_i != 0 else 0
                Qg_reduce = Qo_reduce * RGO_i

                # Calcula redução de Qgco2 e Qghc com base no teorCO2
                teorCO2 = Qgco2_i / Qg_i if Qg_i != 0 else 0.0
                Qgco2_reduce = teorCO2 * Qg_reduce
                Qghc_reduce = Qg_reduce - Qgco2_reduce

                # Determina Projeto e IUPI com base em coluna
                projeto_value = projeto_in if coluna == "Projeto" else df_plataforma.loc[df_plataforma['IUPI'] == projeto_in, 'Projeto'].iloc[0]
                iupi_value = projeto_in if coluna == "IUPI" else df_plataforma.loc[df_plataforma['Projeto'] == projeto_in, 'IUPI'].iloc[0]
                # print(projeto_value)
                # print(iupi_value)
                # df_plataforma.to_excel(r"L:\res\campos\jubarte\er\er03\PN\2630\C1\deletar\df_plataforma.xlsx", index=True)
                # print('df_plataforma')
                # input()

                # Linha de ajuste negativo
                new_line = {
                    "Versao": row_poco.get("Versao", ""),
                    "Cenario": row_poco.get("Cenario", ""),
                    "Campo": row_poco.get("Campo", ""),
                    "ZP": row_poco.get("ZP", ""),
                    "NZP": row_poco.get("NZP", ""),
                    "UEP": row_poco.get("UEP", ""),
                    "Projeto": projeto_value,
                    "IUPI": iupi_value,
                    "Well": row_poco.get("Well", ""),
                    "Date": row_poco.get("Date", data_row),
                    "Classe": "Ajuste Qw",
                    "Qo Pot(m3/day)": -Qo_reduce,
                    "Qw Pot(m3/day)": -reduce_amount,
                    "Qg Pot(mil m3/day)": -Qg_reduce,
                    "Qgco2 Pot(mil m3/day)": -Qgco2_reduce,
                    "Qghc Pot(mil m3/day)": -Qghc_reduce,
                    "Qwi Pot(m3/day)": 0.0,
                    "Qgi Pot(mil m3/day)": 0.0,
                    "Qgl Pot(mil m3/day)": 0.0,
                    "Qgco2i Pot(mil m3/day)": 0.0,
                    "Qghci Pot(mil m3/day)": 0.0,
                }
                adjustment_rows.append(new_line)
                still_to_cut -= reduce_amount

            if adjustment_rows:
                # Garante que todas as colunas existem no DataFrame
                for col in sum_cols + ["Versao", "Cenario", "Campo", "ZP", "NZP", "UEP", "Well", "Date", "Classe", "Projeto", "IUPI"]:
                    if col not in df_plataforma.columns:
                        df_plataforma[col] = 0.0  # ou valor padrão apropriado

                df_plataforma = pd.concat(
                    [df_plataforma, pd.DataFrame(adjustment_rows)],
                    ignore_index=True
                )

    # Retorna o df_plataforma atualizado
    return df_plataforma

@log_execution
def check_limits_for_platform_qg(df_plataforma, plataforma, projeto_in, coluna, priority=None):
    """
    Agrupa df_plataforma por 'Date', soma as colunas numéricas e compara
    com os limites de Qg. Se exceder, gera linhas de ajuste negativas,
    reduzindo primeiro os poços de maior RGO e aplicando prioridades definidas.
    """

    if plataforma not in limites:
        print(f"UEP '{plataforma}' não encontrada em limitesPN.py")
        return df_plataforma

    if not pd.api.types.is_datetime64_any_dtype(df_plataforma['Date']):
        df_plataforma['Date'] = pd.to_datetime(df_plataforma['Date']).dt.date

    if "Classe" not in df_plataforma.columns:
        df_plataforma["Classe"] = "Original"

    df_agg = df_plataforma.groupby("Date").agg(
        Qg=("Qg Pot(mil m3/day)", "sum"),
        Qgl=("Qgl Pot(mil m3/day)", "sum")
    ).reset_index()

    df_agg["Qgt"] = df_agg["Qg"] + df_agg["Qgl"]

    for idx, row in df_agg.iterrows():
        data_row = row["Date"]
        limit_Qg = get_limit_for_date(limites[plataforma].get("Qg"), data_row)

        if limit_Qg is None:
            print(f"Sem limite Qg para {plataforma} em {data_row}")
            continue

        valor_Qgt = row.get("Qgt", 0.0)
        exceed = valor_Qgt - limit_Qg

        if exceed > 0.01:
            mask_data = (df_plataforma["Date"] == data_row)
            subDF = df_plataforma.loc[mask_data].copy()

            group_cols = ["Versao", "Cenario", "Campo", "ZP", "NZP", "UEP", "Well", "Date"]
            sum_cols = [
                "Qo Pot(m3/day)", "Qw Pot(m3/day)", "Qg Pot(mil m3/day)",
                "Qgco2 Pot(mil m3/day)", "Qghc Pot(mil m3/day)"
            ]

            agg_dict = {col: 'sum' for col in sum_cols if col in subDF.columns}
            subDF_agg = subDF.groupby(group_cols, as_index=False).agg(agg_dict)
            subDF_agg[coluna] = projeto_in

            subDF_agg["Qo_i"] = subDF_agg["Qo Pot(m3/day)"]
            subDF_agg["Qg_i"] = subDF_agg["Qg Pot(mil m3/day)"]
            subDF_agg["Qgco2_i"] = subDF_agg["Qgco2 Pot(mil m3/day)"]
            subDF_agg["Qghc_i"] = subDF_agg["Qghc Pot(mil m3/day)"]

            @log_execution
            def calc_rgo(row):
                return row["Qg_i"] / row["Qo_i"] if row["Qo_i"] > 0 else 0

            subDF_agg["RGO"] = subDF_agg.apply(calc_rgo, axis=1)

            subDF_agg = apply_priority_sorting(subDF_agg, ['RGO'], priority)

            adjustment_rows = []
            still_to_cut = exceed

            for _, row_poco in subDF_agg.iterrows():
                if still_to_cut <= 0.01:
                    break

                Qg_i = row_poco["Qg_i"]
                teorCO2 = row_poco["Qgco2_i"] / Qg_i if Qg_i > 0 else 0
                reduce_qg = min(Qg_i, still_to_cut)

                Qo_reduce = reduce_qg / row_poco["RGO"] if row_poco["RGO"] > 0 else 0
                Qw_reduce = Qo_reduce * (row_poco["Qw Pot(m3/day)"] / row_poco["Qo Pot(m3/day)"]) if row_poco["Qo Pot(m3/day)"] > 0 else 0
                Qgco2_reduce = reduce_qg * teorCO2
                Qghc_reduce = reduce_qg - Qgco2_reduce

                projeto_value = projeto_in if coluna == "Projeto" else df_plataforma.loc[df_plataforma['IUPI'] == projeto_in, 'Projeto'].iloc[0]
                iupi_value = projeto_in if coluna == "IUPI" else df_plataforma.loc[df_plataforma['Projeto'] == projeto_in, 'IUPI'].iloc[0]

                new_line = {
                    "Versao": row_poco["Versao"],
                    "Cenario": row_poco["Cenario"],
                    "Campo": row_poco["Campo"],
                    "ZP": row_poco["ZP"],
                    "NZP": row_poco["NZP"],
                    "UEP": plataforma,
                    "Projeto": projeto_value,
                    "IUPI": iupi_value,
                    "Well": row_poco["Well"],
                    "Date": data_row,
                    "Classe": "Ajuste Qg",
                    "Qo Pot(m3/day)": -Qo_reduce,
                    "Qw Pot(m3/day)": -Qw_reduce,
                    "Qg Pot(mil m3/day)": -reduce_qg,
                    "Qgco2 Pot(mil m3/day)": -Qgco2_reduce,
                    "Qghc Pot(mil m3/day)": -Qghc_reduce,
                    "Qwi Pot(m3/day)": 0.0,
                    "Qgi Pot(mil m3/day)": 0.0,
                    "Qgl Pot(mil m3/day)": 0.0,
                    "Qgco2i Pot(mil m3/day)": 0.0,
                    "Qghci Pot(mil m3/day)": 0.0,
                }
                adjustment_rows.append(new_line)
                still_to_cut -= reduce_qg

            if adjustment_rows:
                df_plataforma = pd.concat([df_plataforma, pd.DataFrame(adjustment_rows)], ignore_index=True)

    return df_plataforma

@log_execution
def check_limits_for_platform_ql(df_plataforma, plataforma, projeto_in, coluna, priority=None):
    """
    Agrupa df_plataforma por 'Date', soma Qo + Qw e compara com o limite de Ql.
    Se exceder, gera ajustes negativos priorizando poços com maior BSW.
    Mantém proporção de CO2 na redução de Qg.
    """

    if plataforma not in limites:
        print(f"UEP '{plataforma}' não encontrada em limitesPN.py")
        return df_plataforma

    # Converter coluna Date se necessário
    if not pd.api.types.is_datetime64_any_dtype(df_plataforma['Date']):
        df_plataforma['Date'] = pd.to_datetime(df_plataforma['Date']).dt.date

    # Criar coluna Classe se não existir
    if "Classe" not in df_plataforma.columns:
        df_plataforma["Classe"] = "Original"

    # Agrupar e verificar limites
    df_agg = df_plataforma.groupby("Date").agg(
        Qo=("Qo Pot(m3/day)", "sum"),
        Qw=("Qw Pot(m3/day)", "sum")
    ).reset_index()

    df_agg["Ql"] = df_agg["Qo"] + df_agg["Qw"]

    for idx, row in df_agg.iterrows():
        data_row = row["Date"]
        limit_Ql = get_limit_for_date(limites[plataforma].get("Ql"), data_row)

        if limit_Ql is None:
            print(f"Sem limite Ql para {plataforma} em {data_row}")
            continue

        exceed = row["Ql"] - limit_Ql
        if exceed > 0.01:
            # Filtrar e processar dados da data
            mask = df_plataforma["Date"] == data_row
            subDF = df_plataforma.loc[mask].copy()

            # Agrupar e agregar
            group_cols = ["Versao", "Cenario", "Campo", "ZP", "NZP", "UEP", "Well", "Date"]
            agg_dict = {
                "Qo Pot(m3/day)": "sum",
                "Qw Pot(m3/day)": "sum",
                "Qg Pot(mil m3/day)": "sum",
                "Qgco2 Pot(mil m3/day)": "sum",
                "Qghc Pot(mil m3/day)": "sum",
                coluna: "first"
            }

            subDF_agg = subDF.groupby(group_cols, as_index=False).agg(agg_dict)
            subDF_agg[coluna] = projeto_in  # Define projeto/IUPI conforme parametro

            # Calcular variáveis auxiliares
            subDF_agg["Ql_i"] = subDF_agg["Qo Pot(m3/day)"] + subDF_agg["Qw Pot(m3/day)"]
            subDF_agg["BSW"] = subDF_agg["Qw Pot(m3/day)"] / subDF_agg["Ql_i"].replace(0, 1)
            subDF_agg["RGO"] = subDF_agg["Qg Pot(mil m3/day)"] / subDF_agg["Qo Pot(m3/day)"].replace(0, 1)

            # Ordenar por BSW
            # subDF_agg.sort_values("BSW", ascending=False, inplace=True)
            subDF_agg = apply_priority_sorting(subDF_agg, ['BSW'], priority)

            adjustment_rows = []
            still_to_cut = exceed

            for _, row_poco in subDF_agg.iterrows():
                if still_to_cut <= 0.01:
                    break

                # Calcular reduções
                ql_i = row_poco["Ql_i"]
                reduce_ql = min(ql_i, still_to_cut)

                # Redução proporcional de Qo e Qw
                qo_reduce = reduce_ql * (1 - row_poco["BSW"])
                qw_reduce = reduce_ql * row_poco["BSW"]

                # Redução de Qg baseado no RGO e split CO2
                qg_reduce = qo_reduce * row_poco["RGO"]
                teor_co2 = row_poco["Qgco2 Pot(mil m3/day)"] / row_poco["Qg Pot(mil m3/day)"] if row_poco["Qg Pot(mil m3/day)"] > 0 else 0
                qgco2_reduce = qg_reduce * teor_co2
                qghc_reduce = qg_reduce - qgco2_reduce

                # Determinar Projeto/IUPI
                projeto_value = projeto_in if coluna == "Projeto" else df_plataforma.loc[df_plataforma['IUPI'] == projeto_in, 'Projeto'].iloc[0]
                iupi_value = projeto_in if coluna == "IUPI" else df_plataforma.loc[df_plataforma['Projeto'] == projeto_in, 'IUPI'].iloc[0]

                new_line = {
                    "Versao": row_poco["Versao"],
                    "Cenario": row_poco["Cenario"],
                    "Campo": row_poco["Campo"],
                    "ZP": row_poco["ZP"],
                    "NZP": row_poco["NZP"],
                    "UEP": plataforma,
                    "Projeto": projeto_value,
                    "IUPI": iupi_value,
                    "Well": row_poco["Well"],
                    "Date": data_row,
                    "Classe": "Ajuste Ql",
                    "Qo Pot(m3/day)": -qo_reduce,
                    "Qw Pot(m3/day)": -qw_reduce,
                    "Qg Pot(mil m3/day)": -qg_reduce,
                    "Qgco2 Pot(mil m3/day)": -qgco2_reduce,
                    "Qghc Pot(mil m3/day)": -qghc_reduce,
                    # Colunas de injeção zeradas
                    "Qwi Pot(m3/day)": 0.0,
                    "Qgi Pot(mil m3/day)": 0.0,
                    "Qgl Pot(mil m3/day)": 0.0,
                    "Qgco2i Pot(mil m3/day)": 0.0,
                    "Qghci Pot(mil m3/day)": 0.0
                }
                adjustment_rows.append(new_line)
                still_to_cut -= reduce_ql

            if adjustment_rows:
                df_plataforma = pd.concat(
                    [df_plataforma, pd.DataFrame(adjustment_rows)],
                    ignore_index=True
                )

    return df_plataforma

@log_execution
def check_limits_for_platform_qo(df_plataforma, plataforma, projetos_analisados, coluna, priority=None):
    """
    Verifica limite de Qo, ajustando primeiro poços novos do projeto atual.
    Mantém proporção de CO2 na redução de Qg.
    """

    projeto_in = projetos_analisados[-1]
    projetos_anteriores = projetos_analisados[:-1]

    if plataforma not in limites:
        print(f"UEP '{plataforma}' não encontrada em limitesPN.py")
        return df_plataforma

    # Converter coluna Date se necessário
    if not pd.api.types.is_datetime64_any_dtype(df_plataforma['Date']):
        df_plataforma['Date'] = pd.to_datetime(df_plataforma['Date']).dt.date

    # Criar coluna Classe se não existir
    if "Classe" not in df_plataforma.columns:
        df_plataforma["Classe"] = "Original"

    # Agrupar e verificar limites
    df_agg = df_plataforma.groupby("Date")["Qo Pot(m3/day)"].sum().reset_index()

    for idx, row in df_agg.iterrows():
        data_row = row["Date"]
        limit_Qo = get_limit_for_date(limites[plataforma].get("Qo"), data_row)

        if limit_Qo is None:
            print(f"Sem limite Qo para {plataforma} em {data_row}")
            continue

        exceed = row["Qo Pot(m3/day)"] - limit_Qo
        if exceed > 0.01:
            # Filtrar e processar dados da data
            mask = df_plataforma["Date"] == data_row
            subDF = df_plataforma.loc[mask].copy()

            # Identificar poços novos
            pocos_anteriores = set()
            for proj in projetos_anteriores:
                mask_proj = (df_plataforma[coluna] == proj)
                pocos_anteriores.update(df_plataforma.loc[mask_proj, "Well"].unique())

            subDF["Poco_Novo"] = ~subDF["Well"].isin(pocos_anteriores)

            # Agrupar e agregar
            group_cols = ["Versao", "Cenario", "Campo", "ZP", "NZP", "UEP", "Well", "Date"]
            agg_dict = {
                "Qo Pot(m3/day)": "sum",
                "Qw Pot(m3/day)": "sum",
                "Qg Pot(mil m3/day)": "sum",
                "Qgco2 Pot(mil m3/day)": "sum",
                "Qghc Pot(mil m3/day)": "sum",
                coluna: "first",
                "Poco_Novo": "first"
            }

            subDF_agg = subDF.groupby(group_cols, as_index=False).agg(agg_dict)
            subDF_agg[coluna] = projeto_in  # Define projeto/IUPI conforme parametro

            # Calcular variáveis auxiliares
            subDF_agg["RGO"] = subDF_agg["Qg Pot(mil m3/day)"] / subDF_agg["Qo Pot(m3/day)"].replace(0, 1)
            subDF_agg["BSW"] = subDF_agg["Qw Pot(m3/day)"] / (subDF_agg["Qo Pot(m3/day)"] + subDF_agg["Qw Pot(m3/day)"]).replace(0, 1)

            # Ordenar por poços novos primeiro e depois RGO
            # subDF_agg.sort_values(["Poco_Novo", "RGO"], ascending=[False, False], inplace=True)
            subDF_agg = apply_priority_sorting(subDF_agg, ["Poco_Novo", "RGO"], priority)

            adjustment_rows = []
            still_to_cut = exceed

            for _, row_poco in subDF_agg.iterrows():
                if still_to_cut <= 0.01:
                    break

                # Calcular reduções
                qo_reduce = min(row_poco["Qo Pot(m3/day)"], still_to_cut)

                # Redução proporcional de Qw e Qg
                qw_reduce = qo_reduce * row_poco["BSW"] / (1 - row_poco["BSW"]) if row_poco["BSW"] < 1 else 0
                qg_reduce = qo_reduce * row_poco["RGO"]

                # Split CO2
                teor_co2 = row_poco["Qgco2 Pot(mil m3/day)"] / row_poco["Qg Pot(mil m3/day)"] if row_poco["Qg Pot(mil m3/day)"] > 0 else 0
                qgco2_reduce = qg_reduce * teor_co2
                qghc_reduce = qg_reduce - qgco2_reduce

                # Determinar Projeto/IUPI
                projeto_value = projeto_in if coluna == "Projeto" else df_plataforma.loc[df_plataforma['IUPI'] == projeto_in, 'Projeto'].iloc[0]
                iupi_value = projeto_in if coluna == "IUPI" else df_plataforma.loc[df_plataforma['Projeto'] == projeto_in, 'IUPI'].iloc[0]

                new_line = {
                    "Versao": row_poco["Versao"],
                    "Cenario": row_poco["Cenario"],
                    "Campo": row_poco["Campo"],
                    "ZP": row_poco["ZP"],
                    "NZP": row_poco["NZP"],
                    "UEP": plataforma,
                    "Projeto": projeto_value,
                    "IUPI": iupi_value,
                    "Well": row_poco["Well"],
                    "Date": data_row,
                    "Classe": "Ajuste Qo",
                    "Qo Pot(m3/day)": -qo_reduce,
                    "Qw Pot(m3/day)": -qw_reduce,
                    "Qg Pot(mil m3/day)": -qg_reduce,
                    "Qgco2 Pot(mil m3/day)": -qgco2_reduce,
                    "Qghc Pot(mil m3/day)": -qghc_reduce,
                    # Colunas de injeção zeradas
                    "Qwi Pot(m3/day)": 0.0,
                    "Qgi Pot(mil m3/day)": 0.0,
                    "Qgl Pot(mil m3/day)": 0.0,
                    "Qgco2i Pot(mil m3/day)": 0.0,
                    "Qghci Pot(mil m3/day)": 0.0
                }
                adjustment_rows.append(new_line)
                still_to_cut -= qo_reduce

            if adjustment_rows:
                df_plataforma = pd.concat(
                    [df_plataforma, pd.DataFrame(adjustment_rows)],
                    ignore_index=True
                )

    return df_plataforma

@log_execution
def check_limits_for_platform_qwi(df_plataforma, plataforma, projetos_analisados, coluna, priority=None):
    """
    Verifica limite de Qo, ajustando primeiro poços novos do projeto atual.
    Mantém proporção de CO2 na redução de Qg.
    """

    projeto_in = projetos_analisados[-1]
    projetos_anteriores = projetos_analisados[:-1]

    if plataforma not in limites:
        print(f"UEP '{plataforma}' não encontrada em limitesPN.py")
        return df_plataforma

    # Converter coluna Date se necessario
    if not pd.api.types.is_datetime64_any_dtype(df_plataforma['Date']):
        df_plataforma['Date'] = pd.to_datetime(df_plataforma['Date']).dt.date

    # Criar coluna Classe se nao existir
    if "Classe" not in df_plataforma.columns:
        df_plataforma["Classe"] = "Original"

    # Agrupar e verificar limites
    df_agg = df_plataforma.groupby("Date")["Qwi Pot(m3/day)"].sum().reset_index()

    for idx, row in df_agg.iterrows():
        data_row = row["Date"]
        limit_Qwi = get_limit_for_date(limites[plataforma].get("Qwi"), data_row)

        if limit_Qwi is None:
            print(f"Sem limite Qwi para {plataforma} em {data_row}")
            continue

        exceed = row["Qwi Pot(m3/day)"] - limit_Qwi
        if exceed > 0.01:
            # Filtrar e processar dados da data
            mask = df_plataforma["Date"] == data_row
            subDF = df_plataforma.loc[mask].copy()

            # Identificar pocos novos
            pocos_anteriores = set()
            for proj in projetos_anteriores:
                mask_proj = (df_plataforma[coluna] == proj)
                pocos_anteriores.update(df_plataforma.loc[mask_proj, "Well"].unique())

            subDF["Poco_Novo"] = ~subDF["Well"].isin(pocos_anteriores)

            # Agrupar e agregar
            group_cols = ["Versao", "Cenario", "Campo", "ZP", "NZP", "UEP", "Well", "Date"]
            agg_dict = {
                "Qwi Pot(m3/day)": "sum",
                coluna: "first",
                "Poco_Novo": "first"
            }

            subDF_agg = subDF.groupby(group_cols, as_index=False).agg(agg_dict)
            subDF_agg[coluna] = projeto_in  # Define projeto/IUPI conforme parametro

            # subDF_agg.sort_values(["Poco_Novo", "Qwi Pot(m3/day)"], ascending=[False, False], inplace=True)
            subDF_agg = apply_priority_sorting(subDF_agg, ["Poco_Novo", "Qwi Pot(m3/day)"], priority)

            adjustment_rows = []
            still_to_cut = exceed

            for _, row_poco in subDF_agg.iterrows():
                if still_to_cut <= 0.01:
                    break

                # Calcular reducoes
                qwi_reduce = min(row_poco["Qwi Pot(m3/day)"], still_to_cut)

                # Determinar Projeto/IUPI
                projeto_value = projeto_in if coluna == "Projeto" else df_plataforma.loc[df_plataforma['IUPI'] == projeto_in, 'Projeto'].iloc[0]
                iupi_value = projeto_in if coluna == "IUPI" else df_plataforma.loc[df_plataforma['Projeto'] == projeto_in, 'IUPI'].iloc[0]

                new_line = {
                    "Versao": row_poco["Versao"],
                    "Cenario": row_poco["Cenario"],
                    "Campo": row_poco["Campo"],
                    "ZP": row_poco["ZP"],
                    "NZP": row_poco["NZP"],
                    "UEP": plataforma,
                    "Projeto": projeto_value,
                    "IUPI": iupi_value,
                    "Well": row_poco["Well"],
                    "Date": data_row,
                    "Classe": "Ajuste Qwi",
                    "Qo Pot(m3/day)": 0.0,
                    "Qw Pot(m3/day)": 0.0,
                    "Qg Pot(mil m3/day)": 0.0,
                    "Qgco2 Pot(mil m3/day)": 0.0,
                    "Qghc Pot(mil m3/day)": 0.0,
                    "Qwi Pot(m3/day)": -qwi_reduce,
                    "Qgi Pot(mil m3/day)": 0.0,
                    "Qgl Pot(mil m3/day)": 0.0,
                    "Qgco2i Pot(mil m3/day)": 0.0,
                    "Qghci Pot(mil m3/day)": 0.0
                }
                adjustment_rows.append(new_line)
                still_to_cut -= qwi_reduce

            if adjustment_rows:
                df_plataforma = pd.concat(
                    [df_plataforma, pd.DataFrame(adjustment_rows)],
                    ignore_index=True
                )

    return df_plataforma

@log_execution
def analisar_plataforma(df_plataforma, plataforma, projetos_analisados, coluna, priority=None):
    """Analisa os dados de uma plataforma específica"""
    print(f"Processando plataforma: {plataforma}")

    # Faz a checagem de limites de Qg e captura o df atualizado
    df_plataforma_atualizado = check_limits_for_platform_qg(df_plataforma, plataforma, projetos_analisados[-1], coluna, priority)

    # Faz a checagem de limites de Qw e captura o df atualizado
    df_plataforma_atualizado = check_limits_for_platform_qw(df_plataforma_atualizado, plataforma, projetos_analisados[-1], coluna, priority)

    # Faz a checagem de limites de Ql e captura o df atualizado
    df_plataforma_atualizado = check_limits_for_platform_ql(df_plataforma_atualizado, plataforma, projetos_analisados[-1], coluna, priority)

    # Faz a checagem de limites de Qo e captura o df atualizado
    df_plataforma_atualizado = check_limits_for_platform_qo(df_plataforma_atualizado, plataforma, projetos_analisados, coluna, priority)

    # Faz a checagem de limites de Qwi e captura o df atualizado
    df_plataforma_atualizado = check_limits_for_platform_qwi(df_plataforma_atualizado, plataforma, projetos_analisados, coluna, priority)

    return df_plataforma_atualizado


@log_execution

def analisar_projeto_cumulativo(df, projetos_analisados, coluna, priority=None):
    """Analisa os dados de projetos de forma cumulativa"""
    print(f"\nProcessando projeto: {projetos_analisados[-1]}")

    # Verificar e criar a coluna 'Classe' se necessário
    if 'Classe' not in df.columns:
        df['Classe'] = 'Original'

    # Filtra o DataFrame para obter apenas as linhas dos projetos acumulados até agora
    df_filtrado = df[df[coluna].isin(projetos_analisados)]

    # Verificar se a coluna 'UEP' existe no DataFrame
    if 'UEP' in df_filtrado.columns:
        # Obter valores únicos de plataforma
        plataformas = df_filtrado['UEP'].unique()

        # Analisar cada plataforma separadamente
        for plataforma in plataformas:
            # Filtrar dados para esta plataforma
            df_plataforma = df_filtrado[df_filtrado['UEP'] == plataforma]
            # recebe o df com ajustes
            df_plataforma_ajustado = analisar_plataforma(df_plataforma, plataforma, projetos_analisados, coluna, priority)

            # Agora, precisamos "substituir" essa parte em df_filtrado
            # ou concatenar de volta. Exemplo simples:
            # 1) Remove as linhas antigas dessa plataforma
            df_filtrado = df_filtrado[df_filtrado['UEP'] != plataforma]
            # 2) Concatena com as linhas novas
            df_filtrado = pd.concat([df_filtrado, df_plataforma_ajustado], ignore_index=True)
    else:
        print("Coluna 'UEP' não encontrada no DataFrame. Análise por plataforma não será realizada.")

    # 1) Apagar do df principal as linhas desses projetos
    df_sem_projeto_analisado = df[~df[coluna].isin(projetos_analisados)]
    # 2) Concatenar com df_filtrado atualizado
    df_atualizado = pd.concat([df_sem_projeto_analisado, df_filtrado], ignore_index=True)
    return df_atualizado

@log_execution
def planilhaGIR(df, file_path):
    """
    Processa o DataFrame para adequação às especificações de colunas, renomeação, cálculos e ordem.
    Retorna o DataFrame processado.
    """
    # Lista de colunas originais necessárias (incluindo as usadas em cálculos)
    colunas_necessarias = [
        'Versao', 'Cenario', 'Campo', 'NZP', 'UEP', 'IUPI', 'Well', 'Date',
        'Qo Pot(m3/day)', 'Qg Pot(mil m3/day)', 'Qgco2 Pot(mil m3/day)',
        'Qw Pot(m3/day)', 'Qwi Pot(m3/day)', 'Qgco2i Pot(mil m3/day)',
        'Qgl Pot(mil m3/day)'
    ]

    # Remove colunas não citadas
    df = df[colunas_necessarias].copy()

    # Dicionário de renomeação
    renomear_colunas = {
        'Versao': 'Versão',
        'Cenario': 'Cenário',
        'Campo': 'Campo',
        'NZP': 'Nº da ZP',
        'UEP': 'Plataforma',
        'IUPI': 'Projeto',
        'Well': 'Poço',
        'Date': 'Data',
        'Qo Pot(m3/day)': '1 - Qo Pot',
        'Qw Pot(m3/day)': '3 - Qw Pot',
        'Qwi Pot(m3/day)': '4 - Qwi Pot',
        'Qgco2i Pot(mil m3/day)': '5 - Qgi Pot',
        'Qgl Pot(mil m3/day)': '6 - GL Pot'
    }
    df = df.rename(columns=renomear_colunas)

    # Calcula '2 - Qg Pot' (Qg Pot - Qgco2 Pot)
    df['2 - Qg Pot'] = df['Qg Pot(mil m3/day)'].fillna(0) - df['Qgco2 Pot(mil m3/day)'].fillna(0)

    # Remove colunas originais usadas no cálculo
    df = df.drop(columns=['Qg Pot(mil m3/day)', 'Qgco2 Pot(mil m3/day)'])

    # Cria coluna '7 - Auto inj Pot' (vazia por padrão)
    df['7 - Auto inj Pot'] = 0.0

    # Ordem final das colunas
    ordem_colunas = [
        'Versão', 'Cenário', 'Campo', 'Nº da ZP', 'Plataforma', 'Projeto', 'Poço', 'Data',
        '1 - Qo Pot', '2 - Qg Pot', '3 - Qw Pot', '4 - Qwi Pot',
        '5 - Qgi Pot', '6 - GL Pot', '7 - Auto inj Pot'
    ]

    df = df[ordem_colunas]

    df.to_excel(file_path, index=False, sheet_name='Planilha1')
    print(f"Arquivo Excel GIR salvo em: {os.path.abspath(file_path)}")

    return df

@log_execution
def main():
    excel_path = r"L:\res\campos\jubarte\er\er03\PN\2630\C1\P10\PN2630C1_P50baseP10P90_novoGL_CorrCO140_BAZ1.output.202505281204.new.xlsx"
    ordem_projetos_path = r"L:\res\campos\jubarte\er\er02\00_Programas\python\script\atual\ordem_projetos.txt"
    priority = {
        'Campo':'BLA=9999' # 'Campo': 'JUB=0,PRB=1,BLA=9999' -> Com essa entrada OUTROS campo possui prioridade 5000 ou seja BLA é depois dos OUTROS
    }

    # Configurar timestamp para nome do arquivo de log
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M")
    output_excel_path = os.path.splitext(excel_path)[0] + f"_{timestamp}.ajustado.xlsx"
    output_excel_GIR_path = os.path.splitext(excel_path)[0] + f".{timestamp}.ajustado.gir.xlsx"

    # Solicitar caminhos dos arquivos (ou usar caminhos padrão se já definidos)
    excel_path = input("Digite o caminho do arquivo Excel: ") if not excel_path else excel_path
    ordem_projetos_path = input("Digite o caminho do arquivo ordem_projetos.txt: ") if not ordem_projetos_path else ordem_projetos_path

    # Verificar se os arquivos existem
    if not os.path.exists(excel_path):
        print(f"Erro: O arquivo Excel '{excel_path}' não existe.")
        return

    if not os.path.exists(ordem_projetos_path):
        print(f"Erro: O arquivo de ordem de projetos '{ordem_projetos_path}' não existe.")
        return

    # Ler os arquivos
    df = ler_excel(excel_path)
    ordem_projetos = ler_ordem_projetos(ordem_projetos_path)

    # print('df.columns')
    # print(df.columns)
    # input()

    if df is None or not ordem_projetos:
        print("Não foi possível continuar devido a erros na leitura dos arquivos.")
        return

    # coluna = 'Projeto'
    coluna = 'IUPI'

    # Verificar se a coluna 'IUPI' existe no DataFrame
    if coluna not in df.columns:
        print(f"Erro: A coluna '{coluna}' não foi encontrada no arquivo Excel.")
        print(f"Colunas disponiveis: {', '.join(df.columns)}")
        return

    print(f"Total de projetos a processar: {len(ordem_projetos)}")

    projetos_acumulados = []
    for i, projeto in enumerate(ordem_projetos, 1):
        print(f"\nProcessando projeto {i}/{len(ordem_projetos)}: {projeto}")
        projetos_acumulados.append(projeto)
        df = analisar_projeto_cumulativo(df, projetos_acumulados.copy(), coluna, priority)

    print("Análise concluída!")

    # Verificar e preencher valores vazios na coluna 'Classe'
    if 'Classe' in df.columns:
        # Preencher valores vazios ou NaN com "Original"
        df['Classe'] = df['Classe'].fillna("Original")
        # Verificar se há strings vazias e substituí-las
        df.loc[df['Classe'] == '', 'Classe'] = "Original"
        print(f"Preenchidos {len(df[df['Classe'] == 'Original'])} registros com 'Classe' igual a 'Original'")
        print(f"Preenchidos {len(df[df['Classe'] != 'Original'])} registros com 'Classe' diferente de 'Original'")
    else:
        # Se a coluna não existir, criar e preencher tudo com "Original"
        df['Classe'] = "Original"
        print("Coluna 'Classe' não existia e foi criada com valor padrão 'Original'")

    # return

    # Salvar no Excel final
    df.to_excel(output_excel_path, index=False)
    print(f"Arquivo Excel final salvo em: {os.path.abspath(output_excel_path)}")

    planilhaGIR(df, output_excel_GIR_path)

if __name__ == "__main__":
    main()
