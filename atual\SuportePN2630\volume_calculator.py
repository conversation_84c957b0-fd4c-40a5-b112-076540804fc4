#!/usr/bin/env python3
"""
Sistema Otimizado de Cálculo de Volumes para Dados de Petróleo
Versão Python 3.0 - Análise Avançada com Pandas e NumPy

Autor: Sistema de Análise de Produção
Data: 2025
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime
import warnings
from pathlib import Path
import argparse
from typing import Dict, List, Tuple, Optional
import logging

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Suprimir warnings do pandas
warnings.filterwarnings('ignore')

class VolumeCalculator:
    """
    Classe principal para cálculo de volumes de produção de petróleo
    """
    
    def __init__(self):
        """Inicializa o calculador com configurações padrão"""
        self.vazoes_config = {
            'Qo': {'coluna': '1 - Qo Pot', 'nome': '<PERSON>leo'},
            'Qg': {'coluna': '2 - Qg Pot', 'nome': 'Gás'},
            'Qw': {'coluna': '3 - Qw Pot', 'nome': 'Água'},
            'Qwi': {'coluna': '4 - Qwi Pot', 'nome': 'Água Injetada'},
            'Qgi': {'coluna': '5 - Qgi Pot', 'nome': 'Gás Injetado'},
            'GL': {'coluna': '6 - GL Pot', 'nome': 'Gas Lift'},
            'Auto': {'coluna': '7 - Auto inj Pot', 'nome': 'Auto Injeção'}
        }
        
        self.colunas_principais = {
            'poco': 'Poço',
            'data': 'Data',
            'campo': 'Campo',
            'plataforma': 'Plataforma',
            'projeto': 'Projeto'
        }
        
        self.df = None
        self.df_resultado = None
    
    def carregar_excel(self, caminho_arquivo: str, nome_aba: Optional[str] = None) -> pd.DataFrame:
        """
        Carrega arquivo Excel e realiza limpeza inicial dos dados
        
        Args:
            caminho_arquivo: Caminho para o arquivo Excel
            nome_aba: Nome da aba (opcional, usa a primeira se não especificado)
            
        Returns:
            DataFrame com os dados carregados
        """
        try:
            logger.info(f"Carregando arquivo: {caminho_arquivo}")
            
            # Verifica se o arquivo existe
            if not os.path.exists(caminho_arquivo):
                raise FileNotFoundError(f"Arquivo não encontrado: {caminho_arquivo}")
            
            # Carrega o arquivo Excel
            if nome_aba:
                df = pd.read_excel(caminho_arquivo, sheet_name=nome_aba)
            else:
                df = pd.read_excel(caminho_arquivo)
            
            logger.info(f"Dados carregados: {len(df)} registros, {len(df.columns)} colunas")
            
            # Limpeza inicial
            df = self._preparar_dados(df)
            
            self.df = df
            return df
            
        except Exception as e:
            logger.error(f"Erro ao carregar arquivo: {str(e)}")
            raise
    
    def _preparar_dados(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepara e limpa os dados para processamento
        
        Args:
            df: DataFrame original
            
        Returns:
            DataFrame limpo e preparado
        """
        logger.info("Preparando e limpando dados...")
        
        # Cria cópia para não modificar o original
        df_clean = df.copy()
        
        # Converte coluna de data
        if 'Data' in df_clean.columns:
            df_clean['Data'] = pd.to_datetime(df_clean['Data'], errors='coerce')
        
        # Limpa e converte colunas de vazão
        for vazao_key, vazao_info in self.vazoes_config.items():
            col_name = vazao_info['coluna']
            if col_name in df_clean.columns:
                # Remove espaços e converte vírgulas para pontos
                df_clean[col_name] = df_clean[col_name].astype(str).str.replace(' ', '').str.replace(',', '.')
                # Converte para numérico, colocando NaN para valores inválidos
                df_clean[col_name] = pd.to_numeric(df_clean[col_name], errors='coerce')
                # Preenche NaN com 0
                df_clean[col_name] = df_clean[col_name].fillna(0)
        
        # Remove registros com data inválida
        df_clean = df_clean.dropna(subset=['Data'])
        
        # Ordena por Poço e Data
        if 'Poço' in df_clean.columns and 'Data' in df_clean.columns:
            df_clean = df_clean.sort_values(['Poço', 'Data']).reset_index(drop=True)
        
        logger.info(f"Dados limpos: {len(df_clean)} registros válidos")
        return df_clean
    
    def calcular_dias_mes(self, data_series: pd.Series) -> pd.Series:
        """
        Calcula o número de dias no mês para cada data
        
        Args:
            data_series: Série com datas
            
        Returns:
            Série com número de dias do mês
        """
        return data_series.dt.days_in_month
    
    def calcular_volumes_otimizado(self) -> pd.DataFrame:
        """
        Calcula volumes mensais e acumulados para todas as vazões
        Versão otimizada usando operações vetorizadas do pandas
        
        Returns:
            DataFrame com todos os volumes calculados
        """
        if self.df is None:
            raise ValueError("Dados não carregados. Use carregar_excel() primeiro.")
        
        logger.info("Iniciando cálculo de volumes...")
        start_time = datetime.now()
        
        # Cria cópia dos dados
        df_calc = self.df.copy()
        
        # Calcula dias do mês uma vez
        df_calc['dias_mes'] = self.calcular_dias_mes(df_calc['Data'])
        
        # Calcula volumes para todas as vazões simultaneamente
        for vazao_key, vazao_info in self.vazoes_config.items():
            col_vazao = vazao_info['coluna']
            
            if col_vazao in df_calc.columns:
                # Volume mensal = vazão * dias do mês
                col_volume = f'Vol_{vazao_key}'
                df_calc[col_volume] = df_calc[col_vazao] * df_calc['dias_mes']
                
                # Volume acumulado por poço usando groupby + cumsum (muito eficiente)
                col_acumulado = f'Acum_{vazao_key}'
                df_calc[col_acumulado] = df_calc.groupby('Poço')[col_volume].cumsum()
        
        # Remove coluna auxiliar
        df_calc = df_calc.drop('dias_mes', axis=1)
        
        elapsed_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"Cálculo concluído em {elapsed_time:.2f} segundos")
        
        self.df_resultado = df_calc
        return df_calc
    
    def gerar_relatorio_estatistico(self) -> Dict:
        """
        Gera relatório estatístico dos dados processados
        
        Returns:
            Dicionário com estatísticas
        """
        if self.df_resultado is None:
            raise ValueError("Execute calcular_volumes_otimizado() primeiro.")
        
        relatorio = {
            'total_registros': len(self.df_resultado),
            'periodo': {
                'inicio': self.df_resultado['Data'].min(),
                'fim': self.df_resultado['Data'].max()
            },
            'pocos_unicos': self.df_resultado['Poço'].nunique(),
            'campos_unicos': self.df_resultado['Campo'].nunique() if 'Campo' in self.df_resultado.columns else 0,
            'estatisticas_vazoes': {}
        }
        
        # Estatísticas por vazão
        for vazao_key, vazao_info in self.vazoes_config.items():
            col_vazao = vazao_info['coluna']
            col_volume = f'Vol_{vazao_key}'
            col_acumulado = f'Acum_{vazao_key}'
            
            if col_vazao in self.df_resultado.columns:
                relatorio['estatisticas_vazoes'][vazao_key] = {
                    'nome': vazao_info['nome'],
                    'vazao_media': self.df_resultado[col_vazao].mean(),
                    'vazao_max': self.df_resultado[col_vazao].max(),
                    'volume_total': self.df_resultado[col_volume].sum(),
                    'registros_ativos': (self.df_resultado[col_vazao] > 0).sum()
                }
        
        return relatorio
    
    def salvar_resultado(self, caminho_saida: str, incluir_graficos: bool = True):
        """
        Salva resultados em arquivo Excel com múltiplas abas
        
        Args:
            caminho_saida: Caminho do arquivo de saída
            incluir_graficos: Se deve incluir aba com gráficos
        """
        if self.df_resultado is None:
            raise ValueError("Execute calcular_volumes_otimizado() primeiro.")
        
        logger.info(f"Salvando resultados em: {caminho_saida}")
        
        with pd.ExcelWriter(caminho_saida, engine='openpyxl') as writer:
            # Aba principal com todos os dados
            self.df_resultado.to_excel(writer, sheet_name='Dados_Completos', index=False)
            
            # Aba com resumo por poço
            resumo_poco = self._gerar_resumo_por_poco()
            resumo_poco.to_excel(writer, sheet_name='Resumo_por_Poco', index=False)
            
            # Aba com resumo mensal
            resumo_mensal = self._gerar_resumo_mensal()
            resumo_mensal.to_excel(writer, sheet_name='Resumo_Mensal', index=False)
            
            # Aba com relatório estatístico
            relatorio = self.gerar_relatorio_estatistico()
            df_relatorio = pd.DataFrame([relatorio])
            df_relatorio.to_excel(writer, sheet_name='Relatorio', index=False)
        
        logger.info("Arquivo salvo com sucesso!")
    
    def _gerar_resumo_por_poco(self) -> pd.DataFrame:
        """Gera resumo consolidado por poço"""
        if self.df_resultado is None:
            return pd.DataFrame()
        
        # Agrupa por poço e calcula totais
        colunas_volume = [f'Vol_{key}' for key in self.vazoes_config.keys()]
        colunas_existentes = [col for col in colunas_volume if col in self.df_resultado.columns]
        
        resumo = self.df_resultado.groupby('Poço').agg({
            'Data': ['min', 'max', 'count'],
            **{col: 'sum' for col in colunas_existentes}
        }).round(2)
        
        # Achata o MultiIndex das colunas
        resumo.columns = ['_'.join(col).strip() for col in resumo.columns.values]
        resumo = resumo.reset_index()
        
        return resumo
    
    def _gerar_resumo_mensal(self) -> pd.DataFrame:
        """Gera resumo consolidado mensal"""
        if self.df_resultado is None:
            return pd.DataFrame()
        
        # Cria coluna ano-mês
        self.df_resultado['Ano_Mes'] = self.df_resultado['Data'].dt.to_period('M')
        
        colunas_volume = [f'Vol_{key}' for key in self.vazoes_config.keys()]
        colunas_existentes = [col for col in colunas_volume if col in self.df_resultado.columns]
        
        resumo_mensal = self.df_resultado.groupby('Ano_Mes').agg({
            'Poço': 'nunique',
            **{col: 'sum' for col in colunas_existentes}
        }).round(2)
        
        resumo_mensal = resumo_mensal.reset_index()
        resumo_mensal['Ano_Mes'] = resumo_mensal['Ano_Mes'].astype(str)
        
        return resumo_mensal
    
    def exportar_para_csv(self, diretorio_saida: str):
        """
        Exporta dados para múltiplos arquivos CSV
        
        Args:
            diretorio_saida: Diretório onde salvar os arquivos
        """
        if self.df_resultado is None:
            raise ValueError("Execute calcular_volumes_otimizado() primeiro.")
        
        # Cria diretório se não existe
        Path(diretorio_saida).mkdir(parents=True, exist_ok=True)
        
        # Dados completos
        self.df_resultado.to_csv(
            os.path.join(diretorio_saida, 'dados_completos.csv'), 
            index=False, encoding='utf-8-sig'
        )
        
        # Resumos
        resumo_poco = self._gerar_resumo_por_poco()
        resumo_poco.to_csv(
            os.path.join(diretorio_saida, 'resumo_por_poco.csv'), 
            index=False, encoding='utf-8-sig'
        )
        
        resumo_mensal = self._gerar_resumo_mensal()
        resumo_mensal.to_csv(
            os.path.join(diretorio_saida, 'resumo_mensal.csv'), 
            index=False, encoding='utf-8-sig'
        )
        
        logger.info(f"Arquivos CSV salvos em: {diretorio_saida}")

def main():
    """Função principal para execução via linha de comando"""
    parser = argparse.ArgumentParser(description='Calculador de Volumes de Produção de Petróleo')
    parser.add_argument('arquivo_excel', help='Caminho para o arquivo Excel de entrada')
    parser.add_argument('-o', '--output', default='resultado_volumes.xlsx', 
                       help='Nome do arquivo de saída (default: resultado_volumes.xlsx)')
    parser.add_argument('--aba', help='Nome da aba do Excel (opcional)')
    parser.add_argument('--csv', help='Diretório para exportar CSVs (opcional)')
    parser.add_argument('--relatorio', action='store_true', 
                       help='Exibir relatório estatístico no console')
    
    args = parser.parse_args()
    
    try:
        # Inicializa calculador
        calc = VolumeCalculator()
        
        # Carrega dados
        calc.carregar_excel(args.arquivo_excel, args.aba)
        
        # Calcula volumes
        calc.calcular_volumes_otimizado()
        
        # Salva resultado principal
        calc.salvar_resultado(args.output)
        
        # Exporta CSVs se solicitado
        if args.csv:
            calc.exportar_para_csv(args.csv)
        
        # Mostra relatório se solicitado
        if args.relatorio:
            relatorio = calc.gerar_relatorio_estatistico()
            print("\n" + "="*60)
            print("RELATÓRIO ESTATÍSTICO")
            print("="*60)
            print(f"Total de registros: {relatorio['total_registros']:,}")
            print(f"Período: {relatorio['periodo']['inicio']} a {relatorio['periodo']['fim']}")
            print(f"Poços únicos: {relatorio['pocos_unicos']}")
            print(f"Campos únicos: {relatorio['campos_unicos']}")
            print("\nEstatísticas por Vazão:")
            for key, stats in relatorio['estatisticas_vazoes'].items():
                print(f"  {stats['nome']}: {stats['volume_total']:,.2f} volume total")
        
        logger.info("Processamento concluído com sucesso!")
        
    except Exception as e:
        logger.error(f"Erro durante processamento: {str(e)}")
        sys.exit(1)

# Exemplo de uso interativo
def exemplo_uso():
    """
    Exemplo de como usar a classe VolumeCalculator
    """
    # Inicializa o calculador
    calc = VolumeCalculator()
    
    # Carrega arquivo Excel
    # calc.carregar_excel('dados_petroleo.xlsx')
    
    # Calcula volumes
    # resultado = calc.calcular_volumes_otimizado()
    
    # Gera relatório
    # relatorio = calc.gerar_relatorio_estatistico()
    # print(relatorio)
    
    # Salva resultados
    # calc.salvar_resultado('resultado_analise.xlsx')
    
    print("Para usar este script:")
    print("1. Via linha de comando:")
    print("   python volume_calculator.py dados.xlsx -o resultado.xlsx --relatorio")
    print("\n2. Via código Python:")
    print("   from volume_calculator import VolumeCalculator")
    print("   calc = VolumeCalculator()")
    print("   calc.carregar_excel('dados.xlsx')")
    print("   calc.calcular_volumes_otimizado()")
    print("   calc.salvar_resultado('resultado.xlsx')")

if __name__ == "__main__":
    if len(sys.argv) == 1:
        exemplo_uso()
    else:
        main()

# Dependências necessárias (requirements.txt):
"""
pandas>=1.5.0
numpy>=1.20.0
openpyxl>=3.0.0
pathlib
argparse
"""