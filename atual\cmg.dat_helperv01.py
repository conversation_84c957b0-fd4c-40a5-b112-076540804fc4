import re
import sys
import os
from datetime import datetime, timedelta
from Auxiliares.modules.logging_module import log_execution

# --------------------------------------------------------------
# 1) Ler o arquivo principal
# --------------------------------------------------------------
@log_execution
def le_arquivo(file_path):
    """Lê o arquivo principal e retorna uma lista de strings (linhas do arquivo)."""
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as infile:
        return infile.readlines()

# --------------------------------------------------------------
# 2) Ajustar linhas DATE
# --------------------------------------------------------------
@log_execution
def ajusta_DATE(conteudo):
    """
    Ajusta as linhas DATE no formato correto:
      DATE YYYY MM DD
    O dia (DD) pode ter parte decimal; a parte inteira sempre terá 2 dígitos.
    """
    conteudo_ajustado = []
    for line in conteudo:
        indentacao = re.match(r"^\s*", line).group(0)
        match = re.search(r"(DATE)\s+(\d{4})\s+(\d{1,2})\s+([\d\.]+)", line)
        if match:
            date_str, year, month, day = match.groups()
            if "." in day:
                day_int, day_dec = day.split(".")
                formatted_day = f"{int(day_int):02d}.{day_dec}"
            else:
                formatted_day = f"{int(day):02d}"
            formatted_line = f"{indentacao}{date_str} {year} {int(month):02d} {formatted_day}"
            conteudo_ajustado.append(formatted_line)
        else:
            conteudo_ajustado.append(line.rstrip())
    return conteudo_ajustado

# --------------------------------------------------------------
# 3) Carregar o mapeamento de linux para windows (para INCLUDEs)
# --------------------------------------------------------------
@log_execution
def carrega_mapa_linux2windows():
    mapa = {}
    arquivo_mapa = "linux2windows.txt"
    script_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(script_dir, arquivo_mapa)
    if not os.path.exists(file_path):
        print(f"Warning: O arquivo {file_path} não foi encontrado!")
    if not os.path.isfile(file_path):
        return mapa
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith("#"):
                continue
            cols = line.split()
            if len(cols) >= 2:
                linux_path, windows_path = cols[0], cols[1]
                mapa[linux_path] = windows_path
    return mapa

@log_execution
def traduz_caminho_includes(caminho_incluido, mapa_linux2win):
    caminho_limpo = caminho_incluido.strip().strip("'\"")
    for linux_prefix, windows_prefix in mapa_linux2win.items():
        if caminho_limpo.startswith(linux_prefix):
            sufixo = caminho_limpo[len(linux_prefix):]
            caminho_limpo = windows_prefix + sufixo
            break
    return caminho_limpo

# --------------------------------------------------------------
# 4) Carregar recursivamente arquivos INCLUDE
#    – Aqui, se o arquivo incluído contiver linhas que começam com DATE,
#      mantemos apenas a primeira e a última (o restante é ignorado).
# --------------------------------------------------------------
@log_execution
def carregar_conteudo_incluido(base_file_path, mapa_linux2win, linhas, ja_carregado=None):
    if ja_carregado is None:
        ja_carregado = set()
    base_dir = os.path.dirname(base_file_path)
    resultado = []
    re_date = re.compile(r"^\s*DATE\s+", re.IGNORECASE)
    for line in linhas:
        origem_main = True
        match_inc = re.match(r"^\s*INCLUDE\s+'([^']+)'", line, re.IGNORECASE)
        if match_inc:
            caminho_incl = match_inc.group(1)
            caminho_incl = traduz_caminho_includes(caminho_incl, mapa_linux2win)
            if not os.path.isabs(caminho_incl):
                caminho_incl = os.path.join(base_dir, caminho_incl)
            caminho_incl = os.path.normpath(caminho_incl)
            resultado.append((line.rstrip(), origem_main))
            if caminho_incl not in ja_carregado and os.path.isfile(caminho_incl):
                ja_carregado.add(caminho_incl)
                with open(caminho_incl, 'r', encoding='utf-8', errors='ignore') as incf:
                    conteudo_incl = incf.readlines()
                # Filtrar: se existirem linhas que começam com DATE, guardar apenas a 1ª e a última.
                date_lines = [l.rstrip() for l in conteudo_incl if re_date.match(l)]
                non_date_lines = [l.rstrip() for l in conteudo_incl if not re_date.match(l)]
                include_result = []
                if date_lines:
                    include_result.append((date_lines[0], False))
                    if len(date_lines) > 1:
                        include_result.append((date_lines[-1], False))
                for l in non_date_lines:
                    include_result.append((l, False))
                # Se desejar recursão em INCLUDEs aninhados:
                sub_resultado = carregar_conteudo_incluido(caminho_incl, mapa_linux2win,
                                                           [l for l, _ in include_result],
                                                           ja_carregado=ja_carregado)
                # Para este caso, vamos simplesmente acrescentar os resultados do include
                resultado.extend(include_result)
                resultado.extend(sub_resultado)
        else:
            resultado.append((line.rstrip(), origem_main))
    return resultado

# --------------------------------------------------------------
# 5) Funções auxiliares de data/hora
# --------------------------------------------------------------
@log_execution
def parse_date_decimal(year_str, month_str, day_str):
    year = int(year_str)
    month = int(month_str)
    if "." in day_str:
        parts = day_str.split(".")
        day_int = int(parts[0])
        day_frac = float("0." + parts[1])
    else:
        day_int = int(day_str)
        day_frac = 0.0
    dt_base = datetime(year, month, day_int)
    seconds = day_frac * 86400.0
    return dt_base + timedelta(seconds=seconds)

@log_execution
def difference_in_days(dt1, dt2):
    delta = dt2 - dt1
    return delta.total_seconds() / 86400.0

@log_execution
def add_fraction_of_day(dt, fraction_days):
    return dt + timedelta(days=fraction_days)

@log_execution
def format_date_decimal(dt):
    year = dt.year
    month = dt.month
    day = dt.day
    segs = dt.hour * 3600 + dt.minute * 60 + dt.second + dt.microsecond / 1_000_000
    frac_day = segs / 86400.0
    if frac_day > 0:
        frac_str = f"{frac_day:.3f}".lstrip('0')
        if frac_str == ".000":
            return f"DATE {year} {month:02d} {day:02d}"
        else:
            return f"DATE {year} {month:02d} {day:02d}{frac_str}"
    else:
        return f"DATE {year} {month:02d} {day:02d}"

@log_execution
def extrair_limites_include(caminho_incl):
    """
    Abre o arquivo INCLUDE (em UTF-8, ignorando erros) e retorna uma tupla:
      (first_dt, last_dt)
    – first_dt: o objeto datetime correspondente à 1ª linha que inicia com DATE (se houver)
    – last_dt: o objeto datetime correspondente à última linha que inicia com DATE (se houver)
    Se não houver linhas DATE, retorna (None, None).
    """
    try:
        with open(caminho_incl, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
    except Exception as e:
        return None, None

    re_date = re.compile(r"^\s*DATE\s+(\d{4})\s+(\d{1,2})\s+([\d\.]+)", re.IGNORECASE)
    first_dt = None
    last_dt = None
    for line in lines:
        m = re_date.match(line)
        if m:
            year, month, day = m.group(1), m.group(2), m.group(3)
            dt = parse_date_decimal(year, month, day)
            if first_dt is None:
                first_dt = dt
            last_dt = dt
    return first_dt, last_dt

# --------------------------------------------------------------
# Função adiciona_DATE_curto – versão ajustada
# --------------------------------------------------------------
@log_execution
def adiciona_DATE_curto(linhas_ajustadas):
    """
    Processa somente as linhas do arquivo principal (ignorando INCLUDEs para saída)
    para inserir, quando houver conteúdo relevante entre duas linhas DATE,
    uma nova linha igual a (DATA anterior + 0.001) – mas somente se essa data não for
    exatamente igual (dentro de uma tolerância) à próxima DATA.

    Ao encontrar uma linha INCLUDE, lê-se o arquivo para extrair a 1ª e a última
    ocorrência de linhas que iniciam com DATE e, se encontradas, marca o gap como relevante.
    A linha INCLUDE em si não é copiada para o arquivo de saída.
    """
    resultado = []
    prev_date = None       # Última data (objeto datetime) processada do arquivo principal
    gap_relevante = False  # Flag que indica se há conteúdo relevante entre duas datas

    # Se uma linha INCLUDE for encontrada, extraímos seus limites e marcamos o gap
    @log_execution
    def processa_include(line):
        nonlocal gap_relevante
        m = re.match(r"^\s*INCLUDE\s+'([^']+)'", line, re.IGNORECASE)
        if m:
            caminho_incl = m.group(1)
            # Se o caminho não for absoluto, assumimos que é relativo ao arquivo principal
            base_dir = os.path.dirname(adiciona_DATE_curto.MAIN_FILE_PATH)
            if not os.path.isabs(caminho_incl):
                caminho_incl = os.path.join(base_dir, caminho_incl)
            caminho_incl = os.path.normpath(caminho_incl)
            first_dt, last_dt = extrair_limites_include(caminho_incl)
            if first_dt is not None and last_dt is not None:
                # Consideramos o gap relevante se o arquivo INCLUDE possui DATEs.
                gap_relevante = True

    # Função para determinar se uma linha (não INCLUDE) é conteúdo relevante
    @log_execution
    def eh_conteudo_relevante(linha):
        txt = linha.strip()
        if not txt:
            return False
        if txt.upper().startswith("WSRF") or txt.startswith("**"):
            return False
        return True

    re_date_main = re.compile(r"^\s*DATE\s+(\d{4})\s+(\d{1,2})\s+([\d\.]+)\s*$", re.IGNORECASE)

    for line in linhas_ajustadas:
        # Se a linha é um INCLUDE, processa para marcar gap e NÃO a adiciona à saída.
        if line.strip().upper().startswith("INCLUDE"):
            processa_include(line)
            continue

        m_date = re_date_main.match(line)
        if m_date:
            # Linha do arquivo principal que é DATE.
            year, month, day = m_date.group(1), m_date.group(2), m_date.group(3)
            current_date = parse_date_decimal(year, month, day)
            if prev_date is not None:
                diff = difference_in_days(prev_date, current_date)
                if gap_relevante and diff >= 0.001:
                    # Calcula a data esperada: prev_date + 0.001
                    expected_date = add_fraction_of_day(prev_date, 0.001)
                    # Se a data esperada difere da data atual por mais de uma tolerância (por exemplo, 1e-6 dias), insere-a.
                    if abs(difference_in_days(expected_date, current_date)) > 1e-6:
                        resultado.append(format_date_decimal(expected_date))
            resultado.append(line)
            prev_date = current_date
            gap_relevante = False  # reseta a flag após uma linha DATE do principal
        else:
            # Se não é DATE e é do arquivo principal, adiciona à saída e, se for conteúdo relevante, marca o gap.
            resultado.append(line)
            if eh_conteudo_relevante(line):
                gap_relevante = True

    return resultado

# --------------------------------------------------------------
# 7) Salvar o arquivo principal modificado
# --------------------------------------------------------------
@log_execution
def salva_arquivo(file_path, conteudo):
    base, ext = os.path.splitext(file_path)
    if ext.lower() not in {".dat", ".tpl", ".inc"}:
        print("Erro: O arquivo deve ter extensão .dat ou .tpl")
        return
    output_file = f"{base}_formatted{ext}"
    with open(output_file, 'w', encoding='utf-8') as outfile:
        outfile.write("\n".join(conteudo) + "\n")
    print(f"Arquivo processado e salvo como: {output_file}")

# --------------------------------------------------------------
# 8) main
# --------------------------------------------------------------
@log_execution
def main(file_path):
    adiciona_DATE_curto.MAIN_FILE_PATH = file_path
    if not file_path.lower().endswith((".dat", ".tpl")):
        print("Erro: O arquivo deve ter extensão .dat ou .tpl")
        sys.exit(1)
    conteudo = le_arquivo(file_path)
    conteudo_ajustado = ajusta_DATE(conteudo)
    conteudo_final = adiciona_DATE_curto(conteudo_ajustado)
    salva_arquivo(file_path, conteudo_final)

# --------------------------------------------------------------
# Execução via linha de comando
# --------------------------------------------------------------
if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Uso: python script.py <caminho_para_arquivo.dat ou .tpl>")
        sys.exit(1)
    file_path = sys.argv[1]
    main(file_path)
