# Sistema Modular de Análise de Dados v4.0
# Use --help para ver opções disponíveis

# Exemplo de uso programático:

from data_analyzer import AnalysisFactory

# Cria engine para petróleo
engine, config = AnalysisFactory.create_petroleum_engine()        

# Carrega e processa dados
engine.load_data(r'L:\res\campos\jubarte\er\er03\PN\2630\C2\P50\2507282319\COMP_VERSOES2\JUB V2 - 2025.07.30.xlsx')
engine.process_data(config)

# Gera relatórios
engine.generate_reports({
    'excel': r'L:\res\campos\jubarte\er\er03\PN\2630\C2\P50\2507282319\COMP_VERSOES2\resultado.xlsx',
    'csv': r'L:\res\campos\jubarte\er\er03\PN\2630\C2\P50\2507282319\COMP_VERSOES2\csv_output',
    'text': r'L:\res\campos\jubarte\er\er03\PN\2630\C2\P50\2507282319\COMP_VERSOES2\relatorio.txt'
})
