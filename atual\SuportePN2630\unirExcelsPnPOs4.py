import os
import pandas as pd
import tkinter as tk
from tkinter import filedialog
import xlwings as xw

def ler_arquivos_excel(pasta):
    dfs = []
    colunas_padrao = None
    for arquivo in os.listdir(pasta):
        if arquivo.endswith(".xlsx") or arquivo.endswith(".xls"):
            caminho = os.path.join(pasta, arquivo)
            xls = pd.ExcelFile(caminho)
            for nome_sheet in xls.sheet_names:
                df = xls.parse(nome_sheet)
                if colunas_padrao is None:
                    colunas_padrao = list(df.columns)
                else:
                    df = df[[col for col in colunas_padrao if col in df.columns]]
                dfs.append(df)
    resultado = pd.concat(dfs, ignore_index=True)
    return resultado[colunas_padrao] if colunas_padrao else resultado

def salvar_simples(df, destino):
    df.to_excel(destino, index=False, sheet_name="Planilha1")

def salvar_com_grafico(df, destino):
    df.to_excel(destino, index=False, sheet_name="Planilha1")
    with xw.App(visible=False) as app:
        wb = app.books.open(destino)
        sht = wb.sheets["Planilha1"]
        last_row = sht.range("A1").end("down").row
        last_col = sht.range("A1").end("right").column
        chart = sht.charts.add()
        chart.chart_type = "line"
        chart.set_source_data(sht.range((1, 1), (last_row, last_col)))
        chart.name = "GraficoSimples"
        chart.top = sht.range("A1").top + 100
        chart.left = sht.range("A1").left + 200
        wb.save()
        wb.close()

def salvar_com_segmentacao(df, destino):
    df.to_excel(destino, index=False, sheet_name="Planilha1")
    with xw.App(visible=False) as app:
        wb = app.books.open(destino)
        sht = wb.sheets["Planilha1"]
        last_row = sht.range("A1").end("down").row
        last_col = sht.range("A1").end("right").column
        table_range = sht.range((1, 1), (last_row, last_col))
        tabela_nome = "TabelaPlanilha1"
        sht.api.ListObjects.Add(1, sht.range(table_range).api, 0, 1).Name = tabela_nome
        pt_sht = wb.sheets.add("GraficoDinamico")
        pt_cache = wb.api.PivotTableWizard(
            SourceType=3,
            SourceData=f"Planilha1!{table_range.address}",
            TableDestination=pt_sht.range("A3").api,
            TableName="PivotResumo"
        )
        for i, col in enumerate(df.columns):
            if i == 0:
                pt_cache.PivotFields(col).Orientation = 1  # xlRowField
            elif i == 1:
                pt_cache.PivotFields(col).Orientation = 2  # xlColumnField
            else:
                pt_cache.PivotFields(col).Orientation = 4  # xlPageField
        chart = pt_sht.shapes.add_chart().chart
        chart.set_source_data(pt_sht.range("A3").expand())
        chart.chart_type = "column_clustered"
        wb.save()
        wb.close()
        
def criar_colunas_extras(df):
    df["Seq_Proj_PN2630_C2"] = [gerar_formula_excel1(i) for i in range(len(df))]
    return df
    
def gerar_formula_excel1(linha):
    linha_excel = linha + 2  # +2 porque pandas começa no 0 e Excel no 1, mais cabeçalho
    formula = (
        f'=SE(F{linha_excel}="NO.CDAN";01;'
        f'SE(F{linha_excel}="NO.P-57";02;'
        f'SE(F{linha_excel}="NO.P-58";03;'
        f'SE(F{linha_excel}="POCSUB-24-0645";04;'
        f'SE(F{linha_excel}="DP-11-0310";05;'
        f'SE(F{linha_excel}="DPCOM-20-0001";06;'
        f'SE(F{linha_excel}="DPCOMSO-20-0002";07;'
        f'SE(F{linha_excel}="DPCOMSO-21-0002";08;'
        f'SE(F{linha_excel}="DPCOMSO-22-0118";09;'
        f'SE(F{linha_excel}="EVPRO-23-0179";10;'
        f'SE(F{linha_excel}="EVPRO-23-0180";11;'
        f'SE(F{linha_excel}="DPCOMSO-21-0003";12;'
        f'SE(F{linha_excel}="DPCOM-22-0177";13;'
        f'SE(F{linha_excel}="DPCOM-23-0144";14;'
        f'SE(F{linha_excel}="CCUS-24-0230";15;-1'
        f')))))))))))))'
    )
    return formula

def selecionar_pasta_e_gerar(tipo):
    pasta = filedialog.askdirectory(title="Selecione a pasta com arquivos Excel")
    if not pasta:
        return
    destino = filedialog.asksaveasfilename(defaultextension=".xlsx", filetypes=[("Excel files", "*.xlsx")])
    if not destino:
        return
    df = ler_arquivos_excel(pasta)
    #df = criar_colunas_extras(df)
    funcoes = {
        1: salvar_simples,
        2: salvar_com_grafico,
        3: salvar_com_segmentacao
    }
    funcoes[tipo](df, destino)

def criar_interface():
    janela = tk.Tk()
    janela.title("Unir Excel + Gráficos")
    tk.Label(janela, text="Selecione o tipo de união e saída:", font=("Arial", 14)).pack(pady=10)
    tk.Button(janela, text="1 - União Simples", command=lambda: selecionar_pasta_e_gerar(1), width=40).pack(pady=5)
    tk.Button(janela, text="2 - União + Gráfico Simples", command=lambda: selecionar_pasta_e_gerar(2), width=40).pack(pady=5)
    tk.Button(janela, text="3 - União + Gráfico Dinâmico + Segmentações", command=lambda: selecionar_pasta_e_gerar(3), width=40).pack(pady=5)
    janela.mainloop()

if __name__ == "__main__":
    criar_interface()