import pandas as pd
import os
import sys
from Auxiliares.modules.logging_module import log_execution

@log_execution
def ler_arquivo_excel(caminho_arquivo: str, **kwargs) -> pd.DataFrame:
    # (Manter função existente sem alterações)
    if not os.path.exists(caminho_arquivo):
        raise FileNotFoundError(f"Arquivo não encontrado: {caminho_arquivo}")

    if not caminho_arquivo.lower().endswith(('.xlsx', '.xls')):
        raise ValueError("Extensão de arquivo inválida. Deve ser .xlsx ou .xls")

    return pd.read_excel(caminho_arquivo, **kwargs)

@log_execution
def renomear_colunas(df: pd.DataFrame, mapeamento_colunas: dict) -> pd.DataFrame:
    # (Manter função existente sem alterações)
    return df.rename(columns=mapeamento_colunas)

@log_execution
def substituir_nan_por_zero(df: pd.DataFrame) -> pd.DataFrame:
    # (Manter função existente sem alterações)
    df_processed = df.copy()
    num_cols = df_processed.select_dtypes(include='number').columns
    df_processed[num_cols] = df_processed[num_cols].fillna(0)
    str_cols = df_processed.select_dtypes(include='object').columns
    df_processed[str_cols] = df_processed[str_cols].fillna('A_definir')
    return df_processed

@log_execution
def converter_nzp_inteiro(df: pd.DataFrame) -> pd.DataFrame:
    # (Manter função existente sem alterações)
    if 'NZP' in df.columns:
        df['NZP'] = pd.to_numeric(df['NZP'], errors='coerce')
        df['NZP'] = df['NZP'].fillna(0)
        df['NZP'] = df['NZP'].astype(int)
    return df

@log_execution
def processar_po(caminho_entrada: str) -> pd.DataFrame:
    mapeamento_colunas = {
        "Projeto": "IUPI",
        "Nº da ZP": "NZP",
        "Poço": "Well",
        "Plataforma": "UEP",
        "Data": "Date",
        "1 - Qo Pot": "Qo Pot(m3/day)",
        "2 - Qg Pot": "Qg Pot(mil m3/day)",
        "3 - Qw Pot": "Qw Pot(m3/day)",
        "4 - Qwi Pot": "Qwi Pot(m3/day)",
        "5 - Qgi Pot": "Qgi Pot(mil m3/day)",
        "6 - GL Pot": "Qgl Pot(mil m3/day)",
        "Versão": "Versao",
        "Cenário": "Cenario"
    }

    df = ler_arquivo_excel(
        caminho_entrada,
        sheet_name=0,
        header=0,
        na_values=['', 'NA']
    )

    if "7 - Auto inj Pot" in df.columns:
        df = df.drop(columns=["7 - Auto inj Pot"])

    df = renomear_colunas(df, mapeamento_colunas)

    if 'Date' in df.columns:
        df['Date'] = pd.to_datetime(df['Date'], errors='coerce', dayfirst=True)

    df = substituir_nan_por_zero(df)
    df = converter_nzp_inteiro(df)

    # ===================================================
    # NOVAS MODIFICAÇÕES AQUI
    # ===================================================

    # Dicionário de mapeamento Campo+NZP -> ZP
    mapeamento_zp = {
        ('JUB', 15): 'BR100',
        ('JUB', 1): 'RO300',
        ('ANC8', 1): 'MCB/COQ-ESS103A',
        ('ANC9', 1): 'MCB/COQ-ESS103A',
        ('ARGO', 4): 'MCB/COQ-ESS103A',
        ('JUB', 5): 'MCB/COQ-ESS103A',
        ('JUB', 31): 'MCB/COQ-PRB2',
        ('MGG', 8): 'MCB/COQ-PRB2',
        ('PRB', 6): 'MCB/COQ-PRB2',
        ('BLA', 1): 'CO140-ESS122',
        ('JUB', 9): 'CO140-ESS116',
        ('CXR', 1): 'MCB/COQ-ESS172',
        ('JUB', 11): 'MRL700-136',
        ('JUB', 29): 'MRL700-153',
        ('JUB', 10): 'BFR100'
    }

    # Dicionário de mapeamento UEP+IUPI -> Projeto
    mapeamento_projeto = {
        ('P-58', 'DPCOMSO-20-0002'): 'PID1',
        ('P-57', 'NO.P-57'): 'MP',
        ('P-57', 'DPCOMSO-21-0003'): 'PID3',
        ('P-57', 'DP-11-0310'): 'IPB',
        ('P-57', 'DPCOM-23-0144'): 'PID5',
        ('P-57', 'DPCOMSO-21-0002'): 'DC',
        ('P-57', 'EVPRO-23-0179'): 'EVPRO_P-57',
        ('P-57', 'DPCOMSO-20-0002'): 'PID1',
        ('P-57', 'DPCOM-20-0001'): 'PID2',
        ('CDAN', 'NO.CDAN'): 'MP',
        ('MQT', 'NO.P-58'): 'MP',
        ('P-58', 'NO.P-58'): 'MP',
        ('CDAN', 'EVPRO-23-0180'): 'EVPRO_P-58',
        ('MQT', 'EVPRO-23-0180'): 'EVPRO_P-58',
        ('P-58', 'EVPRO-23-0180'): 'EVPRO_P-58',
        ('CDAN', 'DPCOMSO-22-0118'): 'PID4',
        ('MQT', 'DPCOMSO-22-0118'): 'PID4',
        ('P-58', 'DPCOMSO-22-0118'): 'PID4',
        ('CDAN', 'DPCOM-22-0177'): 'ICSPB',
        ('ICSPB', 'DPCOM-22-0177'): 'ICSPB',
        ('MQT', 'DPCOM-22-0177'): 'ICSPB',
        ('P-58', 'DPCOM-22-0177'): 'ICSPB',
        ('CDAN', 'DP-11-0310'): 'IPB',
        ('MQT', 'DP-11-0310'): 'IPB',
        ('P-58', 'DP-11-0310'): 'IPB',
        ('CDAN', 'DPCOMSO-20-0002'): 'PID1',
        ('MQT', 'DPCOMSO-20-0002'): 'PID1',
        ('CDAN', 'DPCOM-20-0001'): 'PID2',
        ('MQT', 'DPCOM-20-0001'): 'PID2',
        ('P-58', 'DPCOM-20-0001'): 'PID2',
        ('P-58', 'DPCOMSO-21-0002'): 'DC'
    }

    # Dicionário de teor de CO2 por Campo+ZP
    mapeamento_tco2 = {
        ('JUB', 'BR100'): 0,
        ('JUB', 'RO300'): 0,
        ('ANC8', 'MCB/COQ-ESS103A'): 0.04,
        ('ANC9', 'MCB/COQ-ESS103A'): 0.04,
        ('ARGO', 'MCB/COQ-ESS103A'): 0.04,
        ('JUB', 'MCB/COQ-ESS103A'): 0.04,
        ('JUB', 'MCB/COQ-PRB2'): 0.04,
        ('MGG', 'MCB/COQ-PRB2'): 0.04,
        ('PRB', 'MCB/COQ-PRB2'): 0.04,
        ('BLA', 'CO140-ESS122'): 0,
        ('JUB', 'CO140-ESS116'): 0,
        ('CXR', 'MCB/COQ-ESS172'): 0.016,
        ('JUB', 'MRL700-136'): 0,
        ('JUB', 'MRL700-153'): 0,
        ('JUB', 'BFR100'): 0
    }

    # 1. Adicionar coluna ZP
    df['ZP'] = df.apply(
        lambda row: mapeamento_zp.get((row['Campo'], row['NZP']), None),
        axis=1
    )

    # 2. Adicionar coluna Projeto
    df['Projeto'] = df.apply(
        lambda row: mapeamento_projeto.get((row['UEP'], row['IUPI']), None),
        axis=1
    )

    # 3. Criar colunas de injeção com valor zero
    df['Qgco2i Pot(mil m3/day)'] = 0
    df['Qghci Pot(mil m3/day)'] = 0

    # 4. Calcular colunas relacionadas ao CO2
    # Primeiro adicionar coluna temporária TCO2
    df['TCO2'] = df.apply(
        lambda row: mapeamento_tco2.get((row['Campo'], row['ZP']), 0),
        axis=1
    )

    # Calcular as novas colunas
    df['Qgco2 Pot(mil m3/day)'] = df['Qg Pot(mil m3/day)'] * df['TCO2']
    df['Qghc Pot(mil m3/day)'] = df['Qg Pot(mil m3/day)'] - df['Qgco2 Pot(mil m3/day)']

    # Remover coluna temporária TCO2
    df = df.drop(columns=['TCO2'])

    # ===================================================
    # FIM DAS NOVAS MODIFICAÇÕES
    # ===================================================

    return df

@log_execution
def salvar_para_csv(df: pd.DataFrame, caminho_saida: str, **kwargs) -> None:
    # (Manter função existente sem alterações)
    df.to_csv(caminho_saida, **kwargs)

@log_execution
def salvar_para_excel(df: pd.DataFrame, caminho_saida: str, **kwargs) -> None:
    """
    Salva um DataFrame em arquivo Excel (.xlsx)

    Parâmetros:
        df: DataFrame a ser salvo
        caminho_saida: Caminho completo do arquivo de saída
        kwargs: Argumentos extras para pd.DataFrame.to_excel()
    """
    # Garante que o diretório de destino existe
    diretorio = os.path.dirname(caminho_saida)
    if diretorio and not os.path.exists(diretorio):
        os.makedirs(diretorio)

    df.to_excel(caminho_saida, **kwargs)

@log_execution
def main():
    fim_po = "2027-12-01"

    diretorio_base = os.path.dirname(__file__)

    # 1. Arquivo principal
    caminho_entrada = os.path.join(diretorio_base, "Auxiliares", "PO", "2025_08_14_10_04_24_PO08.2025_P50_GIR.xlsx")

    # 2. Arquivo de mapeamento
    caminho_mapeamento = os.path.join(diretorio_base, "Auxiliares", "PO", "mapeamento_poços_selecaoPO.xlsx")

    # 3. Arquivo PN2630C2 (usando caminho absoluto)
    caminho_pn2630 = r"L:\res\campos\jubarte\er\er03\PN\2630\V2_Final\P50\2508270842\PN2630V2_Final.xlsx"

    # Verificar se arquivos existem
    if not os.path.exists(caminho_entrada):
        print(f"Erro: Arquivo principal não encontrado em {caminho_entrada}")
        sys.exit(1)

    if not os.path.exists(caminho_mapeamento):
        print(f"Erro: Arquivo de mapeamento não encontrado em {caminho_mapeamento}")
        sys.exit(1)

    if not os.path.exists(caminho_pn2630):
        print(f"Erro: Arquivo PN2630C2 não encontrado em {caminho_pn2630}")
        sys.exit(1)

    try:
        # Processar arquivo principal
        print(f"\nProcessando arquivo principal: {os.path.basename(caminho_entrada)}")
        df_po = processar_po(caminho_entrada)

        # Ler arquivo de mapeamento
        print(f"\nLendo arquivo de mapeamento: {os.path.basename(caminho_mapeamento)}")
        df_mapeamento = ler_arquivo_excel(caminho_mapeamento)

        # Ler arquivo PN2630C2
        print(f"\nLendo arquivo PN2630C2: {os.path.basename(caminho_pn2630)}")
        df_sim = ler_arquivo_excel(caminho_pn2630)

        # Exibir informações básicas sobre os DataFrames
        print("\n" + "="*50)
        print("Dados do arquivo principal processado:")
        print(f"- Dimensões: {df_po.shape[0]} linhas x {df_po.shape[1]} colunas")
        print(f"- Colunas: {list(df_po.columns)}")
        print(f"- Amostra de dados:\n{df_po.head(3)}")

        print("\n" + "="*50)
        print("Dados do arquivo de mapeamento:")
        print(f"- Dimensões: {df_mapeamento.shape[0]} linhas x {df_mapeamento.shape[1]} colunas")
        print(f"- Colunas: {list(df_mapeamento.columns)}")
        print(f"- Amostra de dados:\n{df_mapeamento.head(3)}")

        print("\n" + "="*50)
        print("Dados do arquivo PN2630C2:")
        print(f"- Dimensões: {df_sim.shape[0]} linhas x {df_sim.shape[1]} colunas")
        print(f"- Colunas: {list(df_sim.columns)}")
        print(f"- Amostra de dados:\n{df_sim.head(3)}")
        print("="*50 + "\n")


        # =================================================================
        # NOVO PROCESSAMENTO: Integração entre df_po, df_mapeamento e df_sim
        # =================================================================
        fim_po_date = pd.to_datetime(fim_po)

        # 1. Filtrar df_mapeamento para manter apenas 'SIM' na coluna CopiaPO
        df_mapeamento_filtrado = df_mapeamento[df_mapeamento['CopiaPO'] == 'SIM']

        # 2. Extrair combinações únicas de (UEP, Campo, IUPI, NZP, Well)
        poços_substituir = df_mapeamento_filtrado[
            # ['UEP', 'Campo', 'IUPI', 'NZP', 'Well']
            ['UEP', 'IUPI','Well']
        ].drop_duplicates()

        # Converter para lista de tuplas para comparação eficiente
        tuplas_substituir = [tuple(x) for x in poços_substituir.to_numpy()]

        # 3. Remover registros antigos do df_sim
        ## Criar chave composta no df_sim
        df_sim['chave'] = list(zip(
            df_sim['UEP'],
            # df_sim['Campo'],
            df_sim['IUPI'],
            # df_sim['NZP'],
            df_sim['Well']
        ))

        ## Filtrar linhas a remover: chave na lista + data <= fim_po
        mask_remover = (
            df_sim['chave'].isin(tuplas_substituir) &
            (pd.to_datetime(df_sim['Date']) <= fim_po_date
        ))
        df_sim_restante = df_sim[~mask_remover].copy()

        # 4. Coletar dados atualizados do df_po
        ## Criar chave composta no df_po
        df_po['chave'] = list(zip(
            df_po['UEP'],
            # df_po['Campo'],
            df_po['IUPI'],
            # df_po['NZP'],
            df_po['Well']
        ))

        ## Filtrar dados relevantes: chave na lista + data <= fim_po
        mask_coletar = (
            df_po['chave'].isin(tuplas_substituir) &
            (pd.to_datetime(df_po['Date']) <= fim_po_date
        ))
        df_po_coletado = df_po[mask_coletar].copy()

        # 5. Combinar dados
        ## Ajustar colunas do df_po_coletado para match com df_sim
        colunas_comuns = list(df_sim.columns.difference(['chave']))  # Colunas originais
        df_po_coletado_ajustado = df_po_coletado[colunas_comuns]

        ## Concatenar dados restantes + novos dados do PO
        df_pn = pd.concat(
            [df_sim_restante[colunas_comuns], df_po_coletado_ajustado],
            ignore_index=True
        )

        # =================================================================
        # RESULTADO FINAL: df_pn pronto para uso
        # =================================================================
        print("\nProcesso de integração concluído com sucesso!")
        print(f" - Registros removidos do PN2630C2: {mask_remover.sum()}")
        print(f" - Registros adicionados do PO: {len(df_po_coletado)}")
        print(f" - Dimensões do novo DataFrame (df_pn): {df_pn.shape}")

        # =================================================================
        # REORDENAR COLUNAS NA ORDEM ESPECIFICADA
        # =================================================================
        ordem_colunas = [
            'Versao', 'Cenario', 'Campo', 'ZP', 'NZP', 'UEP', 'Projeto', 'IUPI', 'Well', 'Date',
            'Qo Pot(m3/day)', 'Qw Pot(m3/day)', 'Qwi Pot(m3/day)', 'Qg Pot(mil m3/day)',
            'Qgi Pot(mil m3/day)', 'Qgl Pot(mil m3/day)', 'Qgco2 Pot(mil m3/day)',
            'Qgco2i Pot(mil m3/day)', 'Qghc Pot(mil m3/day)', 'Qghci Pot(mil m3/day)'
        ]

        # Verificar se todas as colunas existem no DataFrame
        colunas_faltantes = [col for col in ordem_colunas if col not in df_pn.columns]

        if colunas_faltantes:
            print(f"Aviso: As seguintes colunas não foram encontradas no DataFrame e serão criadas com valor zero:")
            for col in colunas_faltantes:
                print(f" - {col}")
                df_pn[col] = 0

        # Reordenar o DataFrame
        df_pn = df_pn[ordem_colunas]

        # =================================================================
        # GERAR ARQUIVO FINAL - SALVAR EM EXCEL
        # =================================================================
        # Definir caminho de saída (mesmo diretório do arquivo PN2630C2)
        caminho_saida_pn = caminho_pn2630.replace('.xlsx', '_POnovo.xlsx')

        # Salvar DataFrame integrado
        salvar_para_excel(
            df_pn,
            caminho_saida_pn,
            index=False,         # Não incluir índice
            sheet_name='Dados',   # Nome da planilha
            engine='openpyxl'     # Engine recomendada para .xlsx
        )

        print("\n" + "="*50)
        print(f"Arquivo final salvo com sucesso em:")
        print(caminho_saida_pn)
        print(f"Total de registros: {len(df_pn)}")
        print("="*50)

    except Exception as e:
        print(f"\nErro durante o processamento: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()