#!/bin/bash
# setup_optimization.sh
# Script de instalação para máxima performance

echo "=== SETUP PARA MÁXIMA PERFORMANCE ==="
echo "Instalando dependências otimizadas..."

# Cria ambiente virtual
py -m venv venv_optimized
# source venv_optimized/bin/activate  # Linux/Mac
venv_optimized\Scripts\activate  # Windows

# Atualiza pip
pip install --upgrade pip setuptools wheel

# Dependências básicas obrigatórias
pip install pandas>=2.0.0
pip install numpy>=1.

pip install pandas>=2.0.0 numpy>=1.24.0 polars>=0.20.0 openpyxl>=3.1.0 numba>=0.58.0 psutil>=5.9.0 cupy-cuda12x>=12.0.0  # Para GPU NVIDIA (opcional)