import os
import getpass
from datetime import datetime
import csv
import pandas as pd
# import plotly.express as px
import sys
import argparse
import functools
import inspect
import json
from pathlib import Path

# ======================
# METADADOS DO SISTEMA
# ======================
__version__ = "1.2"
__release_date__ = "2025-08-07"
__author__ = "Iury Zottele Medeiros"
__contact__ = "<EMAIL>"

# ======================
# STATUS DE DESENVOLVIMENTO
# ======================
__status__ = {
    'implementado': [
        "Registro automatico de execucao de scripts com o decorador @log_execution",
        "Classificacao das execucoes como 'Principal' ou 'Subchamada'",
        "Geracao de relatorio HTML com graficos de uso",
        "Estatisticas separadas por tipo de execucao (Principal/Subchamada)",
        "Controle para evitar registro duplicado na mesma execucao",
        "Criacao automatica do diretorio de logs",
        "Graficos de uso por usuario",
        "Filtro por data funcional no relatorio",
        "Ha scripts que nao possuem funcoes, que sao apenas definicao de variaveis. Preciso criar uma funcao que seja chamada nesse script para que o registro dele seja feito.",
        "No relatorio precisamos criar uma secao que mostre a estatistica por script",
        "Na secao por script tambem devemos mostrar os 10 scripts .py menos usados e mais usados do diretorio raiz para o periodo em analise, mostrando o numero de vezes que ele foi usado",
        "Evitar o uso de caracteres especiais",
    ],
    # Preciso desenvolver isso que esta em __status__['em_desenvolvimento']
    'em_desenvolvimento': [
    ],
    'planejado': [
    ]
}

# ======================
# INSTRUCOES DE USO
# ======================
__usage__ = r"""
COMO USAR:
    # Como modulo em outros scripts:
    from Auxiliares.modules.logging_module import log_execution

    @log_execution
    def main():
        # ... codigo do script ...

    # Para gerar o relatorio:
    python -m Auxiliares.modules.logging_module --report

OPCOES:
    --version   Mostra versao e sai
    --docs      Exibe documentacao completa
    --status    Mostra status de implementacao
    --report    Gera o relatorio de uso

EXEMPLOS:
  # Gerar relatorio de uso:
  python -m Auxiliares.modules.logging_module --report
"""

# ======================
# FUNCOES DE EXIBICAO
# ======================
def show_version():
    print(f"\nLOGGING MODULE v{__version__}")
    print(f"Data de lancamento: {__release_date__}")
    print(f"Autor: {__author__} | Contato: {__contact__}")

def show_usage():
    print(__usage__)

def show_status():
    print("\nSTATUS DE DESENVOLVIMENTO:")
    print("\nIMPLEMENTADO:")
    for item in __status__["implementado"]:
        print(f"  * {item}")

    print("\nEM DESENVOLVIMENTO:")
    for item in __status__["em_desenvolvimento"]:
        print(f"  * {item}")

    print("\nPLANEJADO:")
    for item in __status__["planejado"]:
        print(f"  * {item}")

def show_full_docs():
    show_version()
    print("\n" + "="*50)
    show_usage()
    print("\n" + "="*50)
    show_status()

def documentacao():
    # Controle de argumentos
    if "--version" in sys.argv:
        show_version()
        sys.exit(0)

    if "--docs" in sys.argv:
        show_full_docs()
        sys.exit(0)

    if "--status" in sys.argv:
        show_status()
        sys.exit(0)

    if "--help" in sys.argv or "-h" in sys.argv:
        show_usage()
        sys.exit(0)

# ======================
# CONFIGURACAO DE CAMINHOS
# ======================
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
LOG_DIR = os.path.join(BASE_DIR, 'logs')
os.makedirs(LOG_DIR, exist_ok=True)

LOG_FILE = os.path.join(LOG_DIR, 'script_usage_log.csv')
REPORT_FILE = os.path.join(LOG_DIR, 'usage_report.html')

# Conjunto para controlar scripts ja registrados
_registered_scripts = set()

# ======================
# Funcoes desenvolvidas
# ======================
def register_standalone_script(function_name="standalone_execution"):
    """Registra execucao de scripts que nao possuem funcoes principais"""
    try:
        # Obtem o frame do script chamador
        frame = inspect.currentframe().f_back
        script_path = frame.f_globals['__file__']
        script_name = os.path.basename(script_path)

        # Nao registra o próprio módulo
        if script_name == os.path.basename(__file__):
            return

        # Determina tipo de execucao
        is_main = (frame.f_globals['__name__'] == "__main__")
        tipo_execucao = "Principal" if is_main else "Subchamada"

        # Cria chave única
        execution_key = f"{script_name}::{function_name}"

        # Registra apenas uma vez por execucao
        if execution_key not in _registered_scripts:
            _register_execution(script_name, tipo_execucao, function_name)
            _registered_scripts.add(execution_key)
    except Exception as e:
        print(f"Erro no registro de script: {e}")

def log_execution_custom(nome_funcao=None):
    """Decorador que permite nome personalizado para a funcao"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            module = sys.modules[func.__module__]
            script_path = inspect.getfile(module)
            script_name = os.path.basename(script_path)
            
            # Use nome personalizado ou nome real da funcao
            function_name = nome_funcao or func.__name__
            
            is_main = (func.__module__ == "__main__")
            tipo_execucao = "Principal" if is_main else "Subchamada"
            
            execution_key = f"{script_name}::{function_name}"
            
            if execution_key not in _registered_scripts:
                _register_execution(script_name, tipo_execucao, function_name)
                _registered_scripts.add(execution_key)
            
            return func(*args, **kwargs)
        return wrapper
    return decorator
# Exemplo de uso:
# @log_execution_custom("processamento_avancado")
# def processar():
#     pass

def log_execution(func):
    """Decorador para registrar a execucao do script e funcao"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Obtem o módulo da funcao
        module = sys.modules[func.__module__]

        # Obtem o caminho do arquivo do módulo
        script_path = inspect.getfile(module)
        script_name = os.path.basename(script_path)

        # Obtem o nome da funcao
        function_name = func.__name__

        # Determina se e execucao principal ou subchamada
        is_main = (func.__module__ == "__main__")
        tipo_execucao = "Principal" if is_main else "Subchamada"

        # Cria chave única para script + funcao
        execution_key = f"{script_name}::{function_name}"

        # Registra apenas uma vez por execucao (por script + funcao)
        if execution_key not in _registered_scripts:
            _register_execution(script_name, tipo_execucao, function_name)
            _registered_scripts.add(execution_key)

        return func(*args, **kwargs)
    return wrapper

def register_manual_execution(func_name):
    """Permite registrar execucao com nome de funcao personalizado"""
    frame = inspect.currentframe().f_back
    script_path = frame.f_globals['__file__']
    script_name = os.path.basename(script_path)
    
    is_main = (frame.f_globals['__name__'] == "__main__")
    tipo_execucao = "Principal" if is_main else "Subchamada"
    
    execution_key = f"{script_name}::{func_name}"
    
    if execution_key not in _registered_scripts:
        _register_execution(script_name, tipo_execucao, func_name)
        _registered_scripts.add(execution_key)

def _register_execution(script_name, tipo_execucao, function_name=None):
    """Registra a execucao atual com tipo e funcao"""
    if script_name == os.path.basename(__file__):
        return

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    user = getpass.getuser()

    try:
        with open(LOG_FILE, 'a', newline='') as f:
            writer = csv.writer(f)
            # Verificar se o arquivo esta vazio para escrever o cabecalho
            if os.path.getsize(LOG_FILE) == 0:
                writer.writerow(['Script', 'Usuario', 'Timestamp', 'Tipo', 'Funcao'])
            writer.writerow([
                script_name, 
                user, 
                timestamp, 
                tipo_execucao, 
                function_name or 'N/A'
            ])
    except Exception as e:
        print(f"[ERRO] Falha ao registrar execucao: {e}")

def _fix_log_file():
    """Corrige o arquivo de log removendo cabecalhos duplicados e preenchendo valores faltantes"""
    if not os.path.exists(LOG_FILE):
        return
    
    try:
        # Ler todo o conteúdo do arquivo
        with open(LOG_FILE, 'r') as f:
            lines = f.readlines()
        
        # Encontrar a primeira linha de cabecalho
        header_line = None
        data_lines = []
        
        for line in lines:
            if line.startswith('Script,Usuario,Timestamp,Tipo'):
                if header_line is None:
                    header_line = line
                continue
            data_lines.append(line)
        
        # Se nao encontrou cabecalho, criar um novo
        if header_line is None:
            header_line = 'Script,Usuario,Timestamp,Tipo,Funcao\n'
        
        # Garantir que o cabecalho tenha a coluna Funcao
        if 'Funcao' not in header_line:
            header_line = header_line.strip() + ',Funcao\n'
        
        # Processar linhas de dados
        cleaned_lines = []
        for line in data_lines:
            parts = line.strip().split(',')
            # Adicionar 'N/A' para linhas com 4 colunas
            if len(parts) == 4:
                parts.append('N/A')
            cleaned_lines.append(','.join(parts) + '\n')
        
        # Reescrever o arquivo
        with open(LOG_FILE, 'w') as f:
            f.write(header_line)
            f.writelines(cleaned_lines)
            
        print("[INFO] Arquivo de log corrigido com sucesso")
        
    except Exception as e:
        print(f"[ERRO] Falha ao corrigir arquivo de log: {e}")

def generate_report():
    """Gera relatorio HTML com graficos de utilizacao organizado em abas"""
    if not os.path.exists(LOG_FILE):
        print("[INFO] Nenhum dado de log disponivel.")
        return

    try:
        # Primeiro, corrigir o arquivo de log
        _fix_log_file()
        
        # Tentar ler o arquivo CSV
        try:
            df = pd.read_csv(LOG_FILE)
        except pd.errors.ParserError:
            print("[AVISO] Erro ao ler arquivo CSV. Tentando metodo alternativo...")
            # Metodo alternativo para ler arquivos corrompidos
            with open(LOG_FILE, 'r') as f:
                lines = f.readlines()
            
            # Processar manualmente as linhas
            data = []
            for line in lines:
                if line.startswith('Script,Usuario,Timestamp,Tipo'):
                    continue  # Pular cabecalhos
                parts = line.strip().split(',')
                if len(parts) >= 4:
                    # Garantir que temos pelo menos 5 colunas
                    if len(parts) == 4:
                        parts.append('N/A')
                    data.append(parts)
            
            df = pd.DataFrame(data, columns=['Script', 'Usuario', 'Timestamp', 'Tipo', 'Funcao'])
        
        # Verifica estrutura basica
        if df.empty:
            print("[INFO] Nenhum dado disponivel para analise.")
            return

        required_columns = ['Timestamp', 'Tipo', 'Usuario', 'Script']
        if not all(col in df.columns for col in required_columns):
            print("[ERRO] Estrutura de dados invalida no arquivo de log")
            return

        # Processa colunas temporais
        df['Timestamp'] = pd.to_datetime(df['Timestamp'], errors='coerce')
        # Remover linhas com Timestamp invalido
        df = df.dropna(subset=['Timestamp'])
        df['Data'] = df['Timestamp'].dt.date
        df['Mes'] = df['Timestamp'].dt.strftime('%Y-%m')  # Adiciona coluna de mes
        df['Hora'] = df['Timestamp'].dt.hour              # Adiciona coluna de hora

        # Ordena por data
        df = df.sort_values('Timestamp')

        # Calcula datas minima e maxima para o filtro
        min_date = df['Data'].min()
        max_date = df['Data'].max()

        # Prepara dados para JavaScript
        df['DataStr'] = df['Data'].astype(str)
        logs_json = df.to_json(orient='records', date_format='iso')

        # ====================================================================
        # ESTATISTICAS POR SCRIPT + FUNCAO
        # ====================================================================
        # Agrupa por script e funcao
        script_funcao_stats = df.groupby(['Script', 'Funcao']).agg(
            Total=('Script', 'size'),
            Principal=('Tipo', lambda x: (x == 'Principal').sum()),
            Subchamada=('Tipo', lambda x: (x == 'Subchamada').sum()),
            Ultima_Execucao=('Timestamp', 'max')
        ).reset_index()

        # Ordena por total para obter as combinacoes mais e menos usadas
        script_funcao_stats = script_funcao_stats.sort_values('Total', ascending=False)

        # Top 10 combinacoes mais usadas
        top_10_script_funcao = script_funcao_stats.head(10)

        # Top 10 combinacoes menos usadas (apenas as que tem execucoes)
        bottom_10_script_funcao = script_funcao_stats[script_funcao_stats['Total'] > 0].tail(10)
        # ====================================================================

        # Funcoes mais executadas
        funcoes_mais_usadas = df[df['Funcao'] != 'N/A']['Funcao'].value_counts().head(10)
        
        # Estatisticas por script + funcao
        script_funcao_stats = df.groupby(['Script', 'Funcao']).agg(
            Total=('Script', 'size'),
            Principal=('Tipo', lambda x: (x == 'Principal').sum()),
            Subchamada=('Tipo', lambda x: (x == 'Subchamada').sum()),
            Ultima_Execucao=('Timestamp', 'max')
        ).reset_index()

        # Calcula estatisticas gerais
        total_execucoes = len(df)
        total_principal = len(df[df['Tipo'] == 'Principal'])
        total_subchamada = len(df[df['Tipo'] == 'Subchamada'])

        # Calcula estatisticas por usuario
        execucoes_por_usuario = df['Usuario'].value_counts().reset_index()
        execucoes_por_usuario.columns = ['Usuario', 'Execucoes']
        usuarios_unicos = len(execucoes_por_usuario)

        # Calcula ultimas execucoes
        ultimas_execucoes = df.groupby(['Script', 'Tipo'])['Timestamp'].max().reset_index()
        ultimas_execucoes.columns = ['Script', 'Tipo', 'Ultima Execucao']
        ultimas_execucoes['Ultima Execucao'] = ultimas_execucoes['Ultima Execucao'].dt.strftime('%Y-%m-%d %H:%M:%S')

        # ====================================================================
        # ESTATISTICAS POR SCRIPT
        # ====================================================================
        # 1. Encontra todos os scripts .py no diretório raiz
        all_scripts = []
        for root, dirs, files in os.walk(BASE_DIR):
            # Ignorar diretórios ocultos e venv
            dirs[:] = [d for d in dirs if not d.startswith('.') and not d.startswith('_') and d != 'venv']
            for file in files:
                if file.endswith('.py'):
                    # Usar apenas o nome do arquivo para compatibilidade
                    all_scripts.append(file)

        # Converter para set para remover duplicatas
        all_scripts = set(all_scripts)

        # 2. Cria DataFrame com todos os scripts
        all_scripts_df = pd.DataFrame({'Script': list(all_scripts)})

        # 3. Agrupa dados do log
        log_stats = df.groupby('Script').agg(
            Total=('Script', 'size'),
            Principal=('Tipo', lambda x: (x == 'Principal').sum()),
            Subchamada=('Tipo', lambda x: (x == 'Subchamada').sum()),
            Ultima_Execucao=('Timestamp', 'max')
        ).reset_index()

        # 4. Combina com todos os scripts (left join)
        script_stats = pd.merge(all_scripts_df, log_stats, on='Script', how='left')

        # 5. Preenche valores faltantes (scripts sem execucao)
        script_stats.fillna({
            'Total': 0,
            'Principal': 0,
            'Subchamada': 0,
            'Ultima_Execucao': pd.NaT
        }, inplace=True)

        # 6. Ordena por total (ascendente) e depois por script
        script_stats = script_stats.sort_values(['Total', 'Script'], ascending=[True, True])

        # 7. Formata data de última execucao
        script_stats['Ultima_Execucao'] = script_stats['Ultima_Execucao'].apply(
            lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if not pd.isna(x) else 'Nunca'
        )

        # 8. Identifica os 10 scripts menos usados
        least_used_scripts = script_stats.head(10).copy()
        least_used_scripts = least_used_scripts[['Script', 'Total', 'Principal', 'Subchamada', 'Ultima_Execucao']]

        # 9. Identifica os 10 scripts mais usados
        most_used_scripts = script_stats.sort_values('Total', ascending=False).head(10)
        most_used_scripts = most_used_scripts[['Script', 'Total', 'Principal', 'Subchamada', 'Ultima_Execucao']]
        # ====================================================================

        # Timestamp de geracao do relatório
        generation_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Layout do relatorio
        html_content = f"""
        <html>
            <head>
                <title>Relatorio de Uso de Scripts</title>
                <script src="https://cdn.plot.ly/plotly-2.32.0.min.js"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .container {{ max-width: 1400px; margin: 0 auto; }}
                    .header {{ text-align: center; padding: 20px; background: #f8f9fa; border-bottom: 1px solid #dee2e6; }}
                    .chart {{ margin: 25px 0; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,.05); }}
                    .grid {{ display: grid; grid-template-columns: repeat(2, 1fr); gap: 25px; }}
                    .full-width {{ grid-column: span 2; }}
                    .table-container {{ margin: 30px 0; overflow-x: auto; }}
                    table {{ width: 100%; border-collapse: collapse; }}
                    th, td {{ padding: 12px 15px; text-align: left; border-bottom: 1px solid #dee2e6; }}
                    th {{ background-color: #f8f9fa; font-weight: 600; }}
                    .info-card {{ background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                    .stats-grid {{ display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; text-align: center; }}
                    .stat-card {{ background: #e9ecef; padding: 15px; border-radius: 8px; }}
                    .filtro-data {{ background: #e9ecef; padding: 15px; border-radius: 8px; margin-bottom: 20px; }}
                    .filtro-data h3 {{ margin-top: 0; }}
                    .date-inputs {{ display: flex; gap: 10px; align-items: center; flex-wrap: wrap; }}
                    .date-inputs > div {{ display: flex; align-items: center; gap: 5px; }}
                    .date-inputs label {{ font-weight: bold; white-space: nowrap; }}
                    .date-inputs input {{ padding: 5px; border: 1px solid #ccc; border-radius: 4px; }}
                    .date-inputs button {{ padding: 6px 12px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; }}
                    .date-inputs button:hover {{ background: #45a049; }}
                    .script-stats {{
                        background: #f0f8ff;
                        padding: 20px;
                        border-radius: 8px;
                        margin: 30px 0;
                        border: 1px solid #d1e7ff;
                    }}
                    .script-funcao-stats {{
                        background: #fff0f6;
                        padding: 20px;
                        border-radius: 8px;
                        margin: 30px 0;
                        border: 1px solid #ffd6e7;
                    }}
                    .script-stats h2 {{
                        border-bottom: 2px solid #a8d1ff;
                        padding-bottom: 10px;
                        margin-top: 0;
                    }}
                    .script-funcao-stats h2 {{
                        border-bottom: 2px solid #ff85c0;
                        padding-bottom: 10px;
                        margin-top: 0;
                    }}
                    .script-chart {{
                        height: 500px;
                    }}
                    .script-grid {{
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 20px;
                    }}
                    .script-funcao-grid {{
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 20px;
                    }}
                    
                    /* Estilos para o sistema de abas */
                    .tabs {{
                        display: flex;
                        flex-wrap: wrap;
                        margin-bottom: 20px;
                        border-bottom: 1px solid #dee2e6;
                    }}
                    .tab {{
                        padding: 10px 20px;
                        cursor: pointer;
                        background-color: #f8f9fa;
                        border: 1px solid transparent;
                        border-top-left-radius: 5px;
                        border-top-right-radius: 5px;
                        margin-right: 5px;
                        margin-bottom: -1px;
                    }}
                    .tab:hover {{
                        background-color: #e9ecef;
                    }}
                    .tab.active {{
                        background-color: white;
                        border-color: #dee2e6 #dee2e6 white;
                        font-weight: bold;
                    }}
                    .tab-content {{
                        display: none;
                    }}
                    .tab-content.active {{
                        display: block;
                    }}
                </style>
                <script>
                    // Dados completos para filtro
                    const logsData = {logs_json};
                    const allScripts = {json.dumps(list(all_scripts))};

                    // Funcao para converter data para formato comparavel
                    function parseDate(dateStr) {{
                        return new Date(dateStr);
                    }}

                    // Funcao para alternar entre abas
                    function openTab(evt, tabName) {{
                        // Esconder todos os conteúdos de abas
                        const tabContents = document.getElementsByClassName("tab-content");
                        for (let i = 0; i < tabContents.length; i++) {{
                            tabContents[i].classList.remove("active");
                        }}
                        
                        // Desativar todas as abas
                        const tabs = document.getElementsByClassName("tab");
                        for (let i = 0; i < tabs.length; i++) {{
                            tabs[i].classList.remove("active");
                        }}
                        
                        // Ativar a aba atual
                        document.getElementById(tabName).classList.add("active");
                        evt.currentTarget.classList.add("active");
                        
                        // Redimensionar graficos após mudar de aba
                        setTimeout(() => {{
                            const plots = document.querySelectorAll('.js-plotly-plot');
                            plots.forEach(plot => {{
                                Plotly.Plots.resize(plot);
                            }});
                        }}, 100);
                    }}

                    function aplicarFiltro() {{
                        const startDateInput = document.getElementById('start-date').value;
                        const endDateInput = document.getElementById('end-date').value;

                        console.log('Filtro aplicado - Data inicial:', startDateInput, 'Data final:', endDateInput);

                        // Criar objetos Date com horarios especificos
                        const startDate = new Date(startDateInput + 'T00:00:00'); // Inicio do dia
                        const endDate = new Date(endDateInput + 'T23:59:59.999'); // Final do dia

                        console.log('Periodo de filtro:', startDate, 'ate', endDate);

                        // Filtrar dados (incluindo as datas de inicio e fim)
                        const filteredData = logsData.filter(log => {{
                            const logDate = new Date(log.Timestamp);
                            const isInRange = logDate >= startDate && logDate <= endDate;

                            // Log de debug para os primeiros registros
                            if (logsData.indexOf(log) < 3) {{
                                console.log(`Log ${{logsData.indexOf(log)}}: ${{log.Timestamp}} -> ${{logDate}} -> Incluido: ${{isInRange}}`);
                            }}

                            return isInRange;
                        }});

                        console.log('Registros filtrados:', filteredData.length, 'de', logsData.length, 'total');

                        // Atualizar todos os graficos
                        atualizarGraficos(filteredData);
                    }}

                    function resetarFiltro() {{
                        // Restaurar datas min/max
                        document.getElementById('start-date').value = '{min_date}';
                        document.getElementById('end-date').value = '{max_date}';

                        // Atualizar com todos os dados
                        atualizarGraficos(logsData);
                    }}

                    function atualizarGraficos(data) {{
                        // Atualizar estatisticas
                        document.getElementById('total-execucoes').textContent = data.length;
                        document.getElementById('total-principal').textContent =
                            data.filter(d => d.Tipo === 'Principal').length;
                        document.getElementById('total-subchamada').textContent =
                            data.filter(d => d.Tipo === 'Subchamada').length;
                        document.getElementById('usuarios-unicos').textContent =
                            new Set(data.map(d => d.Usuario)).size;

                        // Atualizar graficos
                        atualizarGrafico('grafico-geral-dia', data, 'Data', 'Tipo', 'Execucoes por Dia (Geral)', 'histogram', 'group');
                        atualizarGrafico('grafico-geral-mes', data, 'Mes', 'Tipo', 'Execucoes por Mes (Geral)', 'histogram');
                        atualizarGraficoPizza('grafico-geral-pizza', data, 'Tipo', 'Distribuicao por Tipo (Geral)');
                        atualizarGrafico('grafico-geral-hora', data, 'Hora', 'Tipo', 'Execucoes por Hora (Geral)', 'histogram');
                        atualizarGraficoLinha('grafico-evolucao-tipo', data, 'Data', 'Tipo', 'Evolucao de Execucoes por Tipo');

                        // Graficos de usuario
                        atualizarGraficoBarras('grafico-usuario-barras', data, 'Usuario', 'Execucoes por Usuario');
                        atualizarGraficoPizza('grafico-usuario-pizza', data, 'Usuario', 'Distribuicao por Usuario');
                        atualizarGraficoLinha('grafico-evolucao-usuario', data, 'Data', 'Usuario', 'Evolucao de Execucoes por Usuario');

                        // Graficos de execucoes principais
                        const principalData = data.filter(d => d.Tipo === 'Principal');
                        atualizarGrafico('grafico-principal-dia', principalData, 'Data', 'Script', 'Execucoes por Dia (Principal)', 'histogram', 'group');
                        atualizarGrafico('grafico-principal-mes', principalData, 'Mes', 'Script', 'Execucoes por Mes (Principal)', 'histogram');

                        // Graficos de subchamadas
                        const subchamadaData = data.filter(d => d.Tipo === 'Subchamada');
                        atualizarGrafico('grafico-subchamada-dia', subchamadaData, 'Data', 'Script', 'Execucoes por Dia (Subchamada)', 'histogram', 'group');
                        atualizarGrafico('grafico-subchamada-mes', subchamadaData, 'Mes', 'Script', 'Execucoes por Mes (Subchamada)', 'histogram');

                        // Atualizar estatisticas de scripts
                        updateScriptStats(data);
                        
                        // Atualizar estatisticas de script + funcao
                        updateScriptFuncaoStats(data);
                    }}

                    // Funcao generica para atualizar histogramas
                    function atualizarGrafico(elementId, data, xAxis, color, title, type='histogram', barmode='') {{
                        const traces = [];
                        const categories = [...new Set(data.map(d => d[color]))];

                        categories.forEach(category => {{
                            const categoryData = data.filter(d => d[color] === category);
                            const xValues = categoryData.map(d => d[xAxis]);

                            traces.push({{
                                x: xValues,
                                type: type,
                                name: category,
                                marker: {{ color: getColor(category) }}
                            }});
                        }});

                        const layout = {{
                            title: title,
                            barmode: barmode,
                            xaxis: {{ title: xAxis }},
                            yaxis: {{ title: 'Quantidade' }}
                        }};

                        Plotly.react(elementId, traces, layout);
                    }}

                    // Funcao para graficos de pizza
                    function atualizarGraficoPizza(elementId, data, category, title) {{
                        const counts = {{}};
                        data.forEach(d => {{
                            counts[d[category]] = (counts[d[category]] || 0) + 1;
                        }});

                        const labels = Object.keys(counts);
                        const values = Object.values(counts);

                        const trace = {{
                            labels: labels,
                            values: values,
                            type: 'pie',
                            hole: 0.4,
                            name: title
                        }};

                        const layout = {{
                            title: title
                        }};

                        Plotly.react(elementId, [trace], layout);
                    }}

                    // Funcao para graficos de linha
                    function atualizarGraficoLinha(elementId, data, xAxis, color, title) {{
                        const traces = [];
                        const categories = [...new Set(data.map(d => d[color]))];

                        categories.forEach(category => {{
                            const categoryData = data.filter(d => d[color] === category);
                            const counts = {{}};

                            categoryData.forEach(d => {{
                                counts[d[xAxis]] = (counts[d[xAxis]] || 0) + 1;
                            }});

                            const xValues = Object.keys(counts).sort();
                            const yValues = xValues.map(x => counts[x] || 0);

                            traces.push({{
                                x: xValues,
                                y: yValues,
                                type: 'scatter',
                                mode: 'lines+markers',
                                name: category,
                                line: {{ shape: 'linear' }}
                            }});
                        }});

                        const layout = {{
                            title: title,
                            xaxis: {{ title: xAxis }},
                            yaxis: {{ title: 'Quantidade' }}
                        }};

                        Plotly.react(elementId, traces, layout);
                    }}

                    // Funcao para graficos de barras horizontales
                    function atualizarGraficoBarras(elementId, data, category, title) {{
                        const counts = {{}};
                        data.forEach(d => {{
                            counts[d[category]] = (counts[d[category]] || 0) + 1;
                        }});

                        const labels = Object.keys(counts);
                        const values = Object.values(counts);

                        const trace = {{
                            x: values,
                            y: labels,
                            type: 'bar',
                            orientation: 'h',
                            marker: {{ color: '#1f77b4' }}
                        }};

                        const layout = {{
                            title: title,
                            xaxis: {{ title: 'Execucoes' }},
                            yaxis: {{ title: category, automargin: true }}
                        }};

                        Plotly.react(elementId, [trace], layout);
                    }}

                    // Funcao auxiliar para cores
                    function getColor(category) {{
                        const colors = [
                            '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                            '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
                        ];
                        return colors[Math.abs(category.split('').reduce((a,b)=>{{a=((a<<5)-a)+b.charCodeAt(0);return a&a}},0)) % colors.length];
                    }}

                    // ====================================================
                    // FUNCÕES PARA ESTATISTICAS DE SCRIPTS
                    // ====================================================

                    // Funcao para atualizar estatisticas de scripts
                    function updateScriptStats(data) {{
                        console.log('Iniciando updateScriptStats com', data.length, 'registros');

                        // Verifica se as tabelas existem antes de continuar
                        const mostUsedTable = document.getElementById('most-used-table');
                        const leastUsedTable = document.getElementById('least-used-table');

                        if (!mostUsedTable || !leastUsedTable) {{
                            console.error('Tabelas nao encontradas no DOM');
                            console.log('most-used-table exists:', !!mostUsedTable);
                            console.log('least-used-table exists:', !!leastUsedTable);
                            return;
                        }}

                        // Agrupa dados por script
                        const scriptCounts = {{}};

                        // Inicializa com todos os scripts conhecidos
                        allScripts.forEach(script => {{
                            scriptCounts[script] = {{
                                Total: 0,
                                Principal: 0,
                                Subchamada: 0,
                                Ultima_Execucao: null
                            }};
                        }});

                        // Processa dados de log
                        data.forEach(log => {{
                            if (!scriptCounts[log.Script]) {{
                                scriptCounts[log.Script] = {{
                                    Total: 0,
                                    Principal: 0,
                                    Subchamada: 0,
                                    Ultima_Execucao: null
                                }};
                            }}

                            scriptCounts[log.Script].Total++;

                            if (log.Tipo === 'Principal') {{
                                scriptCounts[log.Script].Principal++;
                            }} else {{
                                scriptCounts[log.Script].Subchamada++;
                            }}

                            const logDate = new Date(log.Timestamp);
                            if (!scriptCounts[log.Script].Ultima_Execucao ||
                                logDate > scriptCounts[log.Script].Ultima_Execucao) {{
                                scriptCounts[log.Script].Ultima_Execucao = logDate;
                            }}
                        }});

                        // Converte para array
                        let scriptsArray = Object.keys(scriptCounts).map(script => ({{
                            Script: script,
                            Total: scriptCounts[script].Total,
                            Principal: scriptCounts[script].Principal,
                            Subchamada: scriptCounts[script].Subchamada,
                            Ultima_Execucao: scriptCounts[script].Ultima_Execucao ?
                                scriptCounts[script].Ultima_Execucao.toISOString() : null
                        }}));

                        console.log('Scripts processados:', scriptsArray.length);

                        // Ordena por total (ascendente) e depois por script
                        const leastUsed = [...scriptsArray]
                            .sort((a, b) => {{
                                if (a.Total !== b.Total) {{
                                    return a.Total - b.Total;
                                }}
                                return a.Script.localeCompare(b.Script);
                            }})
                            .slice(0, 10);

                        // Ordena por total (decrescente) para os mais usados
                        const mostUsed = [...scriptsArray]
                            .sort((a, b) => b.Total - a.Total)
                            .slice(0, 10);

                        console.log('Menos usados:', leastUsed.length);
                        console.log('Mais usados:', mostUsed.length);

                        // Atualiza tabelas
                        updateScriptTable('least-used-table', leastUsed);
                        updateScriptTable('most-used-table', mostUsed);

                        // Atualiza grafico de pizza
                        updateScriptPieChart(scriptsArray);
                    }}

                    // Atualiza a tabela de scripts
                    function updateScriptTable(tableId, data) {{
                        console.log('Atualizando tabela:', tableId, 'com', data.length, 'registros');

                        const table = document.getElementById(tableId);
                        if (!table) {{
                            console.error('Tabela nao encontrada:', tableId);
                            return;
                        }}

                        const tbody = table.getElementsByTagName('tbody')[0];
                        if (!tbody) {{
                            console.error('tbody nao encontrado na tabela:', tableId);
                            return;
                        }}

                        tbody.innerHTML = '';

                        data.forEach((script, index) => {{
                            console.log(`Adicionando linha ${{index}}:`, script.Script, script.Total);

                            const row = document.createElement('tr');

                            const scriptCell = document.createElement('td');
                            scriptCell.textContent = script.Script;
                            row.appendChild(scriptCell);

                            const totalCell = document.createElement('td');
                            totalCell.textContent = script.Total;
                            row.appendChild(totalCell);

                            const principalCell = document.createElement('td');
                            principalCell.textContent = script.Principal;
                            row.appendChild(principalCell);

                            const subchamadaCell = document.createElement('td');
                            subchamadaCell.textContent = script.Subchamada;
                            row.appendChild(subchamadaCell);

                            const lastCell = document.createElement('td');
                            lastCell.textContent = script.Ultima_Execucao ?
                                new Date(script.Ultima_Execucao).toLocaleString() : 'Nunca';
                            row.appendChild(lastCell);

                            tbody.appendChild(row);
                        }});

                        console.log('Tabela atualizada com sucesso:', tableId);
                    }}

                    // Atualiza o grafico de pizza de distribuicao de scripts
                    function updateScriptPieChart(scriptsArray) {{
                        console.log('Criando grafico de pizza com', scriptsArray.length, 'scripts');

                        // Filtra apenas scripts que foram usados
                        const usedScripts = scriptsArray.filter(script => script.Total > 0);
                        console.log('Scripts usados:', usedScripts.length);

                        if (usedScripts.length === 0) {{
                            // Se nao ha scripts usados, mostra uma mensagem
                            const layout = {{
                                title: 'Distribuicao de Uso de Scripts',
                                height: 500,
                                annotations: [{{
                                    text: 'Nenhum script foi executado no periodo selecionado',
                                    showarrow: false,
                                    x: 0.5,
                                    y: 0.5,
                                    font: {{ size: 16 }}
                                }}],
                                xaxis: {{ visible: false }},
                                yaxis: {{ visible: false }}
                            }};
                            Plotly.react('script-distribution-pie', [], layout);
                            return;
                        }}

                        // Ordena por uso (decrescente)
                        const sortedScripts = usedScripts.sort((a, b) => b.Total - a.Total);

                        // Prepara dados para o grafico de pizza
                        const topScripts = sortedScripts.slice(0, 10);
                        const outrosScripts = sortedScripts.slice(10);
                        const outrosTotal = outrosScripts.reduce((sum, script) => sum + script.Total, 0);

                        let labels = topScripts.map(s => s.Script);
                        let values = topScripts.map(s => s.Total);

                        if (outrosTotal > 0) {{
                            labels.push('Outros');
                            values.push(outrosTotal);
                        }}

                        console.log('Labels:', labels);
                        console.log('Values:', values);

                        const trace = {{
                            labels: labels,
                            values: values,
                            type: 'pie',
                            hole: 0.4,
                            textinfo: 'label+percent',
                            textposition: 'auto',
                            hovertemplate: '<b>%{{label}}</b><br>Execucoes: %{{value}}<br>Percentual: %{{percent}}<extra></extra>',
                            marker: {{
                                colors: [
                                    '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                                    '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf',
                                    '#aec7e8'
                                ],
                                line: {{
                                    color: '#ffffff',
                                    width: 2
                                }}
                            }}
                        }};

                        const layout = {{
                            title: {{
                                text: 'Distribuicao de Uso de Scripts',
                                font: {{ size: 16 }}
                            }},
                            height: 500,
                            margin: {{ t: 60, b: 50, l: 50, r: 120 }},
                            showlegend: true,
                            legend: {{
                                orientation: 'v',
                                x: 1.02,
                                y: 0.5,
                                font: {{ size: 12 }}
                            }}
                        }};

                        console.log('Renderizando grafico...');

                        // USAR newPlot em vez de react para garantir renderizacao limpa
                        Plotly.newPlot('script-distribution-pie', [trace], layout, {{responsive: true}})
                            .then(() => {{
                                console.log('Grafico de pizza renderizado com sucesso');
                                // Forca un resize após renderizacao
                                setTimeout(() => {{
                                    Plotly.Plots.resize('script-distribution-pie');
                                }}, 100);
                            }})
                            .catch(err => {{
                                console.error('Erro ao renderizar grafico:', err);
                                // Em caso de erro, mostra mensagem
                                const errorLayout = {{
                                    title: 'Erro ao carregar grafico',
                                    height: 500,
                                    annotations: [{{
                                        text: 'Erro ao carregar o grafico de distribuicao',
                                        showarrow: false,
                                        x: 0.5,
                                        y: 0.5,
                                        font: {{ size: 16, color: 'red' }}
                                    }}],
                                    xaxis: {{ visible: false }},
                                    yaxis: {{ visible: false }}
                                }};
                                Plotly.newPlot('script-distribution-pie', [], errorLayout);
                            }});
                    }}

                    // ====================================================
                    // FUNCÕES PARA ESTATISTICAS DE SCRIPT + FUNCAO
                    // ====================================================

                    // Funcao para atualizar estatisticas de script + funcao
                    function updateScriptFuncaoStats(data) {{
                        console.log('Iniciando updateScriptFuncaoStats com', data.length, 'registros');

                        // Agrupa dados por script e funcao
                        const scriptFuncaoCounts = {{}};

                        data.forEach(log => {{
                            const key = log.Script + '::' + log.Funcao;
                            if (!scriptFuncaoCounts[key]) {{
                                scriptFuncaoCounts[key] = {{
                                    Script: log.Script,
                                    Funcao: log.Funcao,
                                    Total: 0,
                                    Principal: 0,
                                    Subchamada: 0,
                                    Ultima_Execucao: null
                                }};
                            }}

                            scriptFuncaoCounts[key].Total++;

                            if (log.Tipo === 'Principal') {{
                                scriptFuncaoCounts[key].Principal++;
                            }} else {{
                                scriptFuncaoCounts[key].Subchamada++;
                            }}

                            const logDate = new Date(log.Timestamp);
                            if (!scriptFuncaoCounts[key].Ultima_Execucao || logDate > scriptFuncaoCounts[key].Ultima_Execucao) {{
                                scriptFuncaoCounts[key].Ultima_Execucao = logDate;
                            }}
                        }});

                        // Converte para array
                        let scriptFuncaoArray = Object.values(scriptFuncaoCounts);

                        console.log('Combinacoes script-funcao processadas:', scriptFuncaoArray.length);

                        // Ordena por total (decrescente)
                        scriptFuncaoArray.sort((a, b) => b.Total - a.Total);

                        // Top 10 combinacoes mais usadas
                        const top10 = scriptFuncaoArray.slice(0, 10);

                        // Top 10 combinacoes menos usadas (apenas as que tem execucoes)
                        const bottom10 = scriptFuncaoArray
                            .filter(item => item.Total > 0)
                            .sort((a, b) => a.Total - b.Total)
                            .slice(0, 10);

                        console.log('Combinacoes mais usadas:', top10.length);
                        console.log('Combinacoes menos usadas:', bottom10.length);

                        // Atualiza tabelas
                        updateScriptFuncaoTable('top-script-funcao-table', top10);
                        updateScriptFuncaoTable('bottom-script-funcao-table', bottom10);

                        // Atualiza grafico de pizza
                        updateScriptFuncaoPieChart(scriptFuncaoArray);
                    }}

                    // Atualiza a tabela de script + funcao
                    function updateScriptFuncaoTable(tableId, data) {{
                        console.log('Atualizando tabela:', tableId, 'com', data.length, 'registros');

                        const table = document.getElementById(tableId);
                        if (!table) {{
                            console.error('Tabela nao encontrada:', tableId);
                            return;
                        }}

                        const tbody = table.getElementsByTagName('tbody')[0];
                        if (!tbody) {{
                            console.error('tbody nao encontrado na tabela:', tableId);
                            return;
                        }}

                        tbody.innerHTML = '';

                        data.forEach((item, index) => {{
                            console.log(`Adicionando linha ${{index}}:`, item.Script, item.Funcao, item.Total);

                            const row = document.createElement('tr');

                            const scriptCell = document.createElement('td');
                            scriptCell.textContent = item.Script;
                            row.appendChild(scriptCell);

                            const funcaoCell = document.createElement('td');
                            funcaoCell.textContent = item.Funcao;
                            row.appendChild(funcaoCell);

                            const totalCell = document.createElement('td');
                            totalCell.textContent = item.Total;
                            row.appendChild(totalCell);

                            const principalCell = document.createElement('td');
                            principalCell.textContent = item.Principal;
                            row.appendChild(principalCell);

                            const subchamadaCell = document.createElement('td');
                            subchamadaCell.textContent = item.Subchamada;
                            row.appendChild(subchamadaCell);

                            const lastCell = document.createElement('td');
                            lastCell.textContent = item.Ultima_Execucao ?
                                new Date(item.Ultima_Execucao).toLocaleString() : 'Nunca';
                            row.appendChild(lastCell);

                            tbody.appendChild(row);
                        }});

                        console.log('Tabela atualizada com sucesso:', tableId);
                    }}

                    // Atualiza o grafico de pizza de distribuicao de script + funcao
                    function updateScriptFuncaoPieChart(data) {{
                        console.log('Criando grafico de pizza com', data.length, 'combinacoes script-funcao');

                        // Filtra apenas combinacoes que foram usadas
                        const usedData = data.filter(item => item.Total > 0);
                        console.log('Combinacoes usadas:', usedData.length);

                        if (usedData.length === 0) {{
                            // Se nao ha combinacoes usadas, mostra uma mensagem
                            const layout = {{
                                title: 'Distribuicao de Uso por Script e Funcao',
                                height: 500,
                                annotations: [{{
                                    text: 'Nenhuma combinacao script-funcao foi executada no periodo',
                                    showarrow: false,
                                    x: 0.5,
                                    y: 0.5,
                                    font: {{ size: 16 }}
                                }}],
                                xaxis: {{ visible: false }},
                                yaxis: {{ visible: false }}
                            }};
                            Plotly.react('script-funcao-distribution-pie', [], layout);
                            return;
                        }}

                        // Ordena por uso (decrescente)
                        const sortedData = usedData.sort((a, b) => b.Total - a.Total);

                        // Prepara dados para o grafico de pizza
                        const top10 = sortedData.slice(0, 10);
                        const outros = sortedData.slice(10);
                        const outrosTotal = outros.reduce((sum, item) => sum + item.Total, 0);

                        let labels = top10.map(item => item.Script + '::' + item.Funcao);
                        let values = top10.map(item => item.Total);

                        if (outrosTotal > 0) {{
                            labels.push('Outros');
                            values.push(outrosTotal);
                        }}

                        console.log('Labels:', labels);
                        console.log('Values:', values);

                        const trace = {{
                            labels: labels,
                            values: values,
                            type: 'pie',
                            hole: 0.4,
                            textinfo: 'label+percent',
                            textposition: 'auto',
                            hovertemplate: '<b>%{{label}}</b><br>Execucoes: %{{value}}<br>Percentual: %{{percent}}<extra></extra>',
                            marker: {{
                                colors: [
                                    '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                                    '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf',
                                    '#aec7e8'
                                ],
                                line: {{
                                    color: '#ffffff',
                                    width: 2
                                }}
                            }}
                        }};

                        const layout = {{
                            title: {{
                                text: 'Distribuicao de Uso por Script and Funcao',
                                font: {{ size: 16 }}
                            }},
                            height: 500,
                            margin: {{ t: 60, b: 50, l: 50, r: 120 }},
                            showlegend: true,
                            legend: {{
                                orientation: 'v',
                                x: 1.02,
                                y: 0.5,
                                font: {{ size: 10 }}
                            }}
                        }};

                        console.log('Renderizando grafico...');

                        Plotly.newPlot('script-funcao-distribution-pie', [trace], layout, {{responsive: true}})
                            .then(() => {{
                                console.log('Grafico de pizza renderizado com sucesso');
                                setTimeout(() => {{
                                    Plotly.Plots.resize('script-funcao-distribution-pie');
                                }}, 100);
                            }})
                            .catch(err => {{
                                console.error('Erro ao renderizar grafico:', err);
                                const errorLayout = {{
                                    title: 'Erro ao carregar grafico',
                                    height: 500,
                                    annotations: [{{
                                        text: 'Erro ao carregar o grafico de distribuicao',
                                        showarrow: false,
                                        x: 0.5,
                                        y: 0.5,
                                        font: {{ size: 16, color: 'red' }}
                                    }}],
                                    xaxis: {{ visible: false }},
                                    yaxis: {{ visible: false }}
                                }};
                                Plotly.newPlot('script-funcao-distribution-pie', [], errorLayout);
                            }});
                    }}

                    // ====================================================
                    // INICIALIZACAO
                    // ====================================================

                    // Inicializar ao carregar a pagina
                    document.addEventListener('DOMContentLoaded', function() {{
                        // Configurar datas iniciais
                        document.getElementById('start-date').value = '{min_date}';
                        document.getElementById('end-date').value = '{max_date}';

                        // Inicializar graficos com dados completos
                        aplicarFiltro();
                    }});
                </script>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Relatorio de Uso de Scripts</h1>
                        <div class="info-card">
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <h3>Total Execucoes</h3>
                                    <p style="font-size: 24px; font-weight: bold;" id="total-execucoes">{total_execucoes}</p>
                                </div>
                                <div class="stat-card">
                                    <h3>Execucoes Principais</h3>
                                    <p style="font-size: 24px; font-weight: bold;" id="total-principal">{total_principal}</p>
                                </div>
                                <div class="stat-card">
                                    <h3>Subchamadas</h3>
                                    <p style="font-size: 24px; font-weight: bold;" id="total-subchamada">{total_subchamada}</p>
                                </div>
                                <div class="stat-card">
                                    <h3>Usuarios Unicos</h3>
                                    <p style="font-size: 24px; font-weight: bold;" id="usuarios-unicos">{usuarios_unicos}</p>
                                </div>
                            </div>
                            <p>Periodo: {min_date} - {max_date} |
                               Ultima atualizacao: {generation_timestamp}</p>
                        </div>
                    </div>

                    <div class="filtro-data">
                        <h3>Filtro por Data</h3>
                        <div class="date-inputs">
                            <div>
                                <label for="start-date">Data Inicial:</label>
                                <input type="date" id="start-date">
                            </div>
                            <div>
                                <label for="end-date">Data Final:</label>
                                <input type="date" id="end-date">
                            </div>
                            <button onclick="aplicarFiltro()">Aplicar Filtro</button>
                            <button onclick="resetarFiltro()">Resetar</button>
                        </div>
                    </div>

                    <!-- Sistema de abas -->
                    <div class="tabs">
                        <div class="tab active" onclick="openTab(event, 'tab-ultimas-execucoes')">Ultimas Execucoes</div>
                        <div class="tab" onclick="openTab(event, 'tab-scripts')">Scripts</div>
                        <div class="tab" onclick="openTab(event, 'tab-script-funcao')">Script + Funcao</div>
                        <div class="tab" onclick="openTab(event, 'tab-geral')">Estatisticas Gerais</div>
                        <div class="tab" onclick="openTab(event, 'tab-usuario')">Estatisticas por Usuario</div>
                        <div class="tab" onclick="openTab(event, 'tab-principal')">Execucoes Principais</div>
                        <div class="tab" onclick="openTab(event, 'tab-subchamada')">Subchamadas</div>
                    </div>

                    <!-- Conteúdo das abas -->
                    <div id="tab-ultimas-execucoes" class="tab-content active">
                        <div class="table-container">
                            <h2>Ultimas Execucoes</h2>
                            {ultimas_execucoes.to_html(index=False, classes='table table-striped')}
                        </div>
                    </div>

                    <div id="tab-scripts" class="tab-content">
                        <div class="script-stats">
                            <h2>Estatisticas por Script</h2>

                            <div class="script-grid">
                                <div>
                                    <h3>Top 10 Scripts Mais Utilizados</h3>
                                    <div class="table-container">
                                        <table class="table table-striped" id="most-used-table">
                                            <thead>
                                                <tr>
                                                    <th>Script</th>
                                                    <th>Total</th>
                                                    <th>Principal</th>
                                                    <th>Subchamada</th>
                                                    <th>Ultima Execucao</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div>
                                    <h3>Top 10 Scripts Menos Utilizados</h3>
                                    <div class="table-container">
                                        <table class="table table-striped" id="least-used-table">
                                            <thead>
                                                <tr>
                                                    <th>Script</th>
                                                    <th>Total</th>
                                                    <th>Principal</th>
                                                    <th>Subchamada</th>
                                                    <th>Ultima Execucao</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="chart full-width" id="script-distribution-pie"></div>
                        </div>
                    </div>

                    <div id="tab-script-funcao" class="tab-content">
                        <div class="script-funcao-stats">
                            <h2>Estatisticas por Script e Funcao</h2>

                            <div class="script-funcao-grid">
                                <div>
                                    <h3>Top 10 Combinacoes (Script + Funcao) Mais Utilizadas</h3>
                                    <div class="table-container">
                                        <table class="table table-striped" id="top-script-funcao-table">
                                            <thead>
                                                <tr>
                                                    <th>Script</th>
                                                    <th>Funcao</th>
                                                    <th>Total</th>
                                                    <th>Principal</th>
                                                    <th>Subchamada</th>
                                                    <th>Ultima Execucao</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div>
                                    <h3>Top 10 Combinacoes (Script + Funcao) Menos Utilizadas</h3>
                                    <div class="table-container">
                                        <table class="table table-striped" id="bottom-script-funcao-table">
                                            <thead>
                                                <tr>
                                                    <th>Script</th>
                                                    <th>Funcao</th>
                                                    <th>Total</th>
                                                    <th>Principal</th>
                                                    <th>Subchamada</th>
                                                    <th>Ultima Execucao</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="chart full-width" id="script-funcao-distribution-pie"></div>
                        </div>
                    </div>

                    <div id="tab-geral" class="tab-content">
                        <h2>Estatisticas Gerais</h2>
                        <div class="grid">
                            <div class="chart" id="grafico-geral-dia"></div>
                            <div class="chart" id="grafico-geral-pizza"></div>
                            <div class="chart" id="grafico-geral-mes"></div>
                            <div class"chart" id="grafico-geral-hora"></div>
                            <div class="chart full-width" id="grafico-evolucao-tipo"></div>
                        </div>
                    </div>

                    <div id="tab-usuario" class="tab-content">
                        <h2>Estatisticas por Usuario</h2>
                        <div class="grid">
                            <div class="chart" id="grafico-usuario-barras"></div>
                            <div class="chart" id="grafico-usuario-pizza"></div>
                            <div class="chart full-width" id="grafico-evolucao-usuario"></div>
                        </div>
                    </div>

                    <div id="tab-principal" class="tab-content">
                        <h2>Execucoes Principais</h2>
                        <div class="grid">
                            <div class="chart" id="grafico-principal-dia"></div>
                            <div class="chart" id="grafico-principal-mes"></div>
                        </div>
                    </div>

                    <div id="tab-subchamada" class="tab-content">
                        <h2>Subchamadas</h2>
                        <div class="grid">
                            <div class="chart" id="grafico-subchamada-dia"></div>
                            <div class="chart" id="grafico-subchamada-mes"></div>
                        </div>
                    </div>
                </div>
            </body>
        </html>
        """

        with open(REPORT_FILE, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"[OK] Relatorio gerado: file://{os.path.abspath(REPORT_FILE)}")

    except Exception as e:
        print(f"[ERRO] Erro ao gerar relatorio: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Ponto de entrada para execucao direta"""
    # Verificar se ha argumentos de documentacao
    documentacao()

    # Configurar parser para argumentos
    parser = argparse.ArgumentParser(description='Sistema de Logging de Scripts')
    parser.add_argument('--report', action='store_true', help='Gerar relatorio de uso')
    args = parser.parse_args()

    if args.report:
        generate_report()
    else:
        print("Modo de uso:")
        print("  python -m Auxiliares.modules.logging_module --report")
        print("\nPara usar em outros scripts:")
        print("  from Auxiliares.modules.logging_module import log_execution")
        print("  @log_execution")

if __name__ == "__main__":
    main()