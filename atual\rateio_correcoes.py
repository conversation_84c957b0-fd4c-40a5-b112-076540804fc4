import pandas as pd
import os
import datetime
from Auxiliares.modules.logging_module import log_execution

# Importe aqui o dicionário de limites
from limitesPN import limites

@log_execution
def ler_excel(caminho_arquivo):
    """Lê o arquivo Excel e retorna um DataFrame"""
    print(f"Lendo arquivo Excel: {caminho_arquivo}")
    return pd.read_excel(caminho_arquivo)

@log_execution
def ler_ordem_projetos(caminho_arquivo):
    """Lê o arquivo de texto com a ordem dos projetos"""
    print(f"Lendo ordem de projetos: {caminho_arquivo}")
    resultado = []
    with open(caminho_arquivo, 'r') as arquivo:
        for linha in arquivo:
            linha_limpa = linha.strip()
            if linha_limpa and not linha_limpa.startswith('#'):
                resultado.append(linha_limpa)
    return resultado

@log_execution
def str_to_date(date_str):
    """Converte string para date object"""
    try:
        return datetime.datetime.strptime(date_str.strip(), "%Y-%m-%d").date()
    except:
        return None

@log_execution
def get_limit_for_date(limite_valor, data_consulta):
    """
    Retorna o limite numérico correspondente à data_consulta,
    dado que limite_valor pode ser:
      - Um inteiro/float fixo
      - Um dicionário com chaves que podem definir intervalos de data
    """
    # Garante que é datetime.date
    if isinstance(data_consulta, pd.Timestamp):
        data_consulta = data_consulta.date()

    # Se o valor for numérico (int ou float), simplesmente retorne
    if isinstance(limite_valor, (int, float)):
        return limite_valor

    # Caso seja um dicionário, precisamos verificar cada faixa
    for intervalo_str, valor_max in limite_valor.items():
        # Tentar detectar se é um intervalo ou data única
        if ":" in intervalo_str:
            partes = intervalo_str.split(":")
            start_str, end_str = partes[0].strip(), partes[1].strip()

            start_date = str_to_date(start_str) if start_str else None
            end_date   = str_to_date(end_str)   if end_str else None

            # Verifica se data_consulta está dentro do intervalo
            if (start_date is None or data_consulta >= start_date) and \
               (end_date is None   or data_consulta <= end_date):
                return valor_max

        else:
            # Pode ser uma data exata (ex: "2030-01-01")
            data_limite = str_to_date(intervalo_str)
            if data_limite == data_consulta:
                return valor_max

    return None

@log_execution
def apply_priority_sorting(df, sort_by, priority=None):
    """
    Aplica ordenação prioritária ao DataFrame com base nas regras fornecidas.
    Ordena primeiro por sort_by (decrescente), depois por prioridades definidas (ascendente).
    """
    df_sorted = df.copy()

    if not priority:
        return df_sorted.sort_values(sort_by, ascending=False)

    sort_columns = sort_by.copy()
    ascending = [False] * len(sort_by)

    for col, priority_str in priority.items():
        priority_dict = {}
        for pair in priority_str.split(','):
            key, val = pair.strip().split('=')
            priority_dict[key] = int(val)

        priority_col = f"{col}_priority"
        df_sorted[priority_col] = (
            df_sorted[col]
            .map(priority_dict)
            .fillna(5000)
        )

        sort_columns.insert(0, priority_col)
        ascending.insert(0, True)

    df_sorted = df_sorted.sort_values(sort_columns, ascending=ascending)
    df_sorted = df_sorted.drop(columns=[c for c in df_sorted.columns if '_priority' in c])

    return df_sorted

@log_execution
def calcular_rateio_campo_zp(subDF, data_row, variavel_coluna):
    """
    Calcula o rateio para Campo e ZP baseado na proporção da variável especificada.
    Retorna um dicionário com as proporções para cada combinação Campo/ZP.
    """
    # Filtrar dados para a data específica
    mask_data = subDF["Date"] == data_row
    dados_data = subDF.loc[mask_data].copy()

    # Verificar quantos campos únicos existem nesta data
    campos_unicos = dados_data["Campo"].nunique()

    if campos_unicos > 1:
        # Múltiplos campos - calcular rateio por Campo
        agrupamento = dados_data.groupby("Campo")[variavel_coluna].sum()
        total = agrupamento.sum()

        if total > 0:
            proporcoes = (agrupamento / total).to_dict()
            return {'tipo': 'Campo', 'proporcoes': proporcoes}
        else:
            # Se total é zero, dividir igualmente
            proporcoes = {campo: 1.0/campos_unicos for campo in agrupamento.index}
            return {'tipo': 'Campo', 'proporcoes': proporcoes}

    else:
        # Apenas um campo - verificar ZP
        zps_unicos = dados_data["ZP"].nunique()

        if zps_unicos > 1:
            # Múltiplas ZPs - calcular rateio por ZP
            agrupamento = dados_data.groupby("ZP")[variavel_coluna].sum()
            total = agrupamento.sum()

            if total > 0:
                proporcoes = (agrupamento / total).to_dict()
                return {'tipo': 'ZP', 'proporcoes': proporcoes}
            else:
                # Se total é zero, dividir igualmente
                proporcoes = {zp: 1.0/zps_unicos for zp in agrupamento.index}
                return {'tipo': 'ZP', 'proporcoes': proporcoes}
        else:
            # Apenas uma ZP - sem rateio necessário
            return {'tipo': 'None', 'proporcoes': {}}

@log_execution
def criar_linhas_ajuste_rateadas(row_poco, data_row, reduce_amount, rateio_info, classe_ajuste,
                                projeto_value, iupi_value, plataforma, teorCO2=0.0):
    """
    Cria linhas de ajuste baseadas no rateio calculado.
    """
    adjustment_rows = []

    if rateio_info['tipo'] == 'None':
        # Sem rateio - criar apenas uma linha
        new_line = criar_linha_ajuste_base(row_poco, data_row, reduce_amount, classe_ajuste,
                                         projeto_value, iupi_value, plataforma, teorCO2)
        adjustment_rows.append(new_line)

    else:
        # Com rateio - criar uma linha para cada proporção
        for chave, proporcao in rateio_info['proporcoes'].items():
            reduce_rateado = reduce_amount * proporcao

            # Criar linha base
            new_line = criar_linha_ajuste_base(row_poco, data_row, reduce_rateado, classe_ajuste,
                                             projeto_value, iupi_value, plataforma, teorCO2)

            # Ajustar Campo ou ZP conforme o tipo de rateio
            if rateio_info['tipo'] == 'Campo':
                new_line["Campo"] = chave
            elif rateio_info['tipo'] == 'ZP':
                new_line["ZP"] = chave

            adjustment_rows.append(new_line)

    return adjustment_rows

@log_execution
def criar_linha_ajuste_base(row_poco, data_row, reduce_amount, classe_ajuste,
                           projeto_value, iupi_value, plataforma, teorCO2=0.0):
    """
    Cria a estrutura base de uma linha de ajuste.
    """
    if classe_ajuste == "Ajuste Qw":
        BSW_i = row_poco.get("BSW", 0.0)
        RGO_i = row_poco.get("RGO", 0.0)

        Qo_reduce = reduce_amount * (1 - BSW_i) / BSW_i if BSW_i != 0 else 0
        Qg_reduce = Qo_reduce * RGO_i
        Qgco2_reduce = teorCO2 * Qg_reduce
        Qghc_reduce = Qg_reduce - Qgco2_reduce

        return {
            "Versao": row_poco.get("Versao", ""),
            "Cenario": row_poco.get("Cenario", ""),
            "Campo": row_poco.get("Campo", ""),
            "ZP": row_poco.get("ZP", ""),
            "NZP": row_poco.get("NZP", ""),
            "UEP": plataforma,
            "Projeto": projeto_value,
            "IUPI": iupi_value,
            "Well": row_poco.get("Well", ""),
            "Date": data_row,
            "Classe": classe_ajuste,
            "Qo Pot(m3/day)": -Qo_reduce,
            "Qw Pot(m3/day)": -reduce_amount,
            "Qg Pot(mil m3/day)": -Qg_reduce,
            "Qgco2 Pot(mil m3/day)": -Qgco2_reduce,
            "Qghc Pot(mil m3/day)": -Qghc_reduce,
            "Qwi Pot(m3/day)": 0.0,
            "Qgi Pot(mil m3/day)": 0.0,
            "Qgl Pot(mil m3/day)": 0.0,
            "Qgco2i Pot(mil m3/day)": 0.0,
            "Qghci Pot(mil m3/day)": 0.0,
        }

    elif classe_ajuste == "Ajuste Qg":
        RGO_i = row_poco.get("RGO", 0.0)

        Qo_reduce = reduce_amount / RGO_i if RGO_i > 0 else 0
        Qw_reduce = Qo_reduce * (row_poco.get("Qw Pot(m3/day)", 0) / row_poco.get("Qo Pot(m3/day)", 1)) if row_poco.get("Qo Pot(m3/day)", 0) > 0 else 0
        Qgco2_reduce = reduce_amount * teorCO2
        Qghc_reduce = reduce_amount - Qgco2_reduce

        return {
            "Versao": row_poco.get("Versao", ""),
            "Cenario": row_poco.get("Cenario", ""),
            "Campo": row_poco.get("Campo", ""),
            "ZP": row_poco.get("ZP", ""),
            "NZP": row_poco.get("NZP", ""),
            "UEP": plataforma,
            "Projeto": projeto_value,
            "IUPI": iupi_value,
            "Well": row_poco.get("Well", ""),
            "Date": data_row,
            "Classe": classe_ajuste,
            "Qo Pot(m3/day)": -Qo_reduce,
            "Qw Pot(m3/day)": -Qw_reduce,
            "Qg Pot(mil m3/day)": -reduce_amount,
            "Qgco2 Pot(mil m3/day)": -Qgco2_reduce,
            "Qghc Pot(mil m3/day)": -Qghc_reduce,
            "Qwi Pot(m3/day)": 0.0,
            "Qgi Pot(mil m3/day)": 0.0,
            "Qgl Pot(mil m3/day)": 0.0,
            "Qgco2i Pot(mil m3/day)": 0.0,
            "Qghci Pot(mil m3/day)": 0.0,
        }

    elif classe_ajuste == "Ajuste Ql":
        BSW_i = row_poco.get("BSW", 0.0)
        RGO_i = row_poco.get("RGO", 0.0)

        qo_reduce = reduce_amount * (1 - BSW_i)
        qw_reduce = reduce_amount * BSW_i
        qg_reduce = qo_reduce * RGO_i
        qgco2_reduce = qg_reduce * teorCO2
        qghc_reduce = qg_reduce - qgco2_reduce

        return {
            "Versao": row_poco.get("Versao", ""),
            "Cenario": row_poco.get("Cenario", ""),
            "Campo": row_poco.get("Campo", ""),
            "ZP": row_poco.get("ZP", ""),
            "NZP": row_poco.get("NZP", ""),
            "UEP": plataforma,
            "Projeto": projeto_value,
            "IUPI": iupi_value,
            "Well": row_poco.get("Well", ""),
            "Date": data_row,
            "Classe": classe_ajuste,
            "Qo Pot(m3/day)": -qo_reduce,
            "Qw Pot(m3/day)": -qw_reduce,
            "Qg Pot(mil m3/day)": -qg_reduce,
            "Qgco2 Pot(mil m3/day)": -qgco2_reduce,
            "Qghc Pot(mil m3/day)": -qghc_reduce,
            "Qwi Pot(m3/day)": 0.0,
            "Qgi Pot(mil m3/day)": 0.0,
            "Qgl Pot(mil m3/day)": 0.0,
            "Qgco2i Pot(mil m3/day)": 0.0,
            "Qghci Pot(mil m3/day)": 0.0
        }

    elif classe_ajuste == "Ajuste Qo":
        BSW_i = row_poco.get("BSW", 0.0)
        RGO_i = row_poco.get("RGO", 0.0)

        qw_reduce = reduce_amount * BSW_i / (1 - BSW_i) if BSW_i < 1 else 0
        qg_reduce = reduce_amount * RGO_i
        qgco2_reduce = qg_reduce * teorCO2
        qghc_reduce = qg_reduce - qgco2_reduce

        return {
            "Versao": row_poco.get("Versao", ""),
            "Cenario": row_poco.get("Cenario", ""),
            "Campo": row_poco.get("Campo", ""),
            "ZP": row_poco.get("ZP", ""),
            "NZP": row_poco.get("NZP", ""),
            "UEP": plataforma,
            "Projeto": projeto_value,
            "IUPI": iupi_value,
            "Well": row_poco.get("Well", ""),
            "Date": data_row,
            "Classe": classe_ajuste,
            "Qo Pot(m3/day)": -reduce_amount,
            "Qw Pot(m3/day)": -qw_reduce,
            "Qg Pot(mil m3/day)": -qg_reduce,
            "Qgco2 Pot(mil m3/day)": -qgco2_reduce,
            "Qghc Pot(mil m3/day)": -qghc_reduce,
            "Qwi Pot(m3/day)": 0.0,
            "Qgi Pot(mil m3/day)": 0.0,
            "Qgl Pot(mil m3/day)": 0.0,
            "Qgco2i Pot(mil m3/day)": 0.0,
            "Qghci Pot(mil m3/day)": 0.0
        }

    elif classe_ajuste == "Ajuste Qwi":
        return {
            "Versao": row_poco.get("Versao", ""),
            "Cenario": row_poco.get("Cenario", ""),
            "Campo": row_poco.get("Campo", ""),
            "ZP": row_poco.get("ZP", ""),
            "NZP": row_poco.get("NZP", ""),
            "UEP": plataforma,
            "Projeto": projeto_value,
            "IUPI": iupi_value,
            "Well": row_poco.get("Well", ""),
            "Date": data_row,
            "Classe": classe_ajuste,
            "Qo Pot(m3/day)": 0.0,
            "Qw Pot(m3/day)": 0.0,
            "Qg Pot(mil m3/day)": 0.0,
            "Qgco2 Pot(mil m3/day)": 0.0,
            "Qghc Pot(mil m3/day)": 0.0,
            "Qwi Pot(m3/day)": -reduce_amount,
            "Qgi Pot(mil m3/day)": 0.0,
            "Qgl Pot(mil m3/day)": 0.0,
            "Qgco2i Pot(mil m3/day)": 0.0,
            "Qghci Pot(mil m3/day)": 0.0
        }

@log_execution
def check_limits_for_platform_qw(df_plataforma, plataforma, projeto_in, coluna, priority=None):
    """
    Agrupa df_plataforma por 'Date', soma as colunas numéricas e compara
    com os limites de Qw. Se exceder, gera linhas de ajuste negativas,
    reduzindo primeiro os poços de maior BSW e aplicando rateio por Campo/ZP.
    """

    if plataforma not in limites:
        print(f"UEP '{plataforma}' não encontrada em limitesPN.py")
        return df_plataforma

    # Garante que a coluna 'Date' é datetime.date
    if not pd.api.types.is_datetime64_any_dtype(df_plataforma['Date']):
        df_plataforma['Date'] = pd.to_datetime(df_plataforma['Date']).dt.date

    # Se a coluna "Classe" não existir, crie e marque tudo como Original
    if "Classe" not in df_plataforma.columns:
        df_plataforma["Classe"] = "Original"

    # Colunas de interesse para verificar o limite (Qw)
    cols_existentes = [col for col in ["Qw Pot(m3/day)"] if col in df_plataforma.columns]
    if not cols_existentes:
        print(f"Nenhuma coluna 'Qw Pot(m3/day)' encontrada para a plataforma {plataforma}.")
        return df_plataforma

    # Agrupa por Date e soma as colunas de interesse
    df_agg = df_plataforma.groupby("Date")[cols_existentes].sum().reset_index()

    # Para cada data, verifica se Qw excede o limite
    for idx, row in df_agg.iterrows():
        data_row = row["Date"]

        limit_Qw = get_limit_for_date(limites[plataforma].get("Qw"), data_row)
        if limit_Qw is None:
            print(f"Não foi encontrado limite para a plataforma '{plataforma}' na data {data_row}")
            continue

        valor_somado_Qw = row.get("Qw Pot(m3/day)", 0.0)
        exceed = valor_somado_Qw - limit_Qw

        if exceed > 0.01:
            # Calcular rateio para esta data
            rateio_info = calcular_rateio_campo_zp(df_plataforma, data_row, "Qw Pot(m3/day)")

            # 1) Filtra as linhas para a data em questão
            mask_data = (df_plataforma["Date"] == data_row)
            subDF = df_plataforma.loc[mask_data].copy()

            # 2) Colunas para agrupamento
            group_cols = [
                "Versao", "Cenario", "Campo", "ZP", "NZP", "UEP",
                "Well", "Date",
            ]
            group_cols = [col for col in group_cols if col in subDF.columns]

            # Colunas para soma
            sum_cols = [
                "Qo Pot(m3/day)", "Qw Pot(m3/day)", "Qg Pot(mil m3/day)",
                "Qgco2 Pot(mil m3/day)", "Qghc Pot(mil m3/day)",
                "Qwi Pot(m3/day)", "Qgi Pot(mil m3/day)", "Qgl Pot(mil m3/day)",
                "Qgco2i Pot(mil m3/day)", "Qghci Pot(mil m3/day)",
            ]
            sum_cols_exist = [c for c in sum_cols if c in subDF.columns]

            # Colunas adicionais para agregação
            agg_dict = {col: 'sum' for col in sum_cols_exist}
            if 'Projeto' in subDF.columns:
                agg_dict['Projeto'] = 'first'
            if 'IUPI' in subDF.columns:
                agg_dict['IUPI'] = 'first'

            # Agrupa e agrega
            subDF_agg = subDF.groupby(group_cols, as_index=False).agg(agg_dict)
            subDF_agg[coluna] = projeto_in

            # Prepara subDF para cálculos
            subDF_agg["Qo_i"] = subDF_agg.get("Qo Pot(m3/day)", 0.0)
            subDF_agg["Qw_i"] = subDF_agg.get("Qw Pot(m3/day)", 0.0)
            subDF_agg["Qg_i"] = subDF_agg.get("Qg Pot(mil m3/day)", 0.0)
            subDF_agg["Qgco2_i"] = subDF_agg.get("Qgco2 Pot(mil m3/day)", 0.0)

            # Calcula BSW e RGO
            @log_execution
            def calc_bsw(row_poco):
                denom = row_poco["Qo_i"] + row_poco["Qw_i"]
                return row_poco["Qw_i"] / denom if denom > 0 else 0.0

            @log_execution
            def calc_rgo(row_poco):
                return row_poco["Qg_i"] / row_poco["Qo_i"] if row_poco["Qo_i"] > 0 else 0.0

            subDF_agg["BSW"] = subDF_agg.apply(calc_bsw, axis=1)
            subDF_agg["RGO"] = subDF_agg.apply(calc_rgo, axis=1)

            # Ordena por BSW
            subDF_agg = apply_priority_sorting(subDF_agg, ['BSW'], priority)

            adjustment_rows = []
            still_to_cut = exceed

            for irow, row_poco in subDF_agg.iterrows():
                if still_to_cut <= 0.01:
                    break

                Qw_i = row_poco["Qw_i"]
                if Qw_i <= 0:
                    continue

                reduce_amount = min(Qw_i, still_to_cut)

                # Determina Projeto e IUPI com base em coluna
                try:
                    if coluna == "Projeto":
                        projeto_value = projeto_in
                        iupi_value = df_plataforma.loc[df_plataforma['Projeto'] == projeto_in, 'IUPI'].iloc[0]
                    else:
                        iupi_value = projeto_in
                        projeto_value = df_plataforma.loc[df_plataforma['IUPI'] == projeto_in, 'Projeto'].iloc[0]
                except IndexError:
                    projeto_value = projeto_in
                    iupi_value = projeto_in

                # Calcula teorCO2
                Qg_i = row_poco["Qg_i"]
                Qgco2_i = row_poco["Qgco2_i"]
                teorCO2 = Qgco2_i / Qg_i if Qg_i != 0 else 0.0

                # Criar linhas de ajuste com rateio
                new_lines = criar_linhas_ajuste_rateadas(
                    row_poco, data_row, reduce_amount, rateio_info, "Ajuste Qw",
                    projeto_value, iupi_value, plataforma, teorCO2
                )

                adjustment_rows.extend(new_lines)
                still_to_cut -= reduce_amount

            if adjustment_rows:
                # Garante que todas as colunas existem no DataFrame
                for col in sum_cols + ["Versao", "Cenario", "Campo", "ZP", "NZP", "UEP", "Well", "Date", "Classe", "Projeto", "IUPI"]:
                    if col not in df_plataforma.columns:
                        df_plataforma[col] = 0.0

                df_plataforma = pd.concat(
                    [df_plataforma, pd.DataFrame(adjustment_rows)],
                    ignore_index=True
                )

    return df_plataforma

@log_execution
def check_limits_for_platform_qg(df_plataforma, plataforma, projeto_in, coluna, priority=None):
    """
    Agrupa df_plataforma por 'Date', soma as colunas numéricas e compara
    com os limites de Qg. Se exceder, gera linhas de ajuste negativas,
    reduzindo primeiro os poços de maior RGO e aplicando rateio por Campo/ZP.
    """

    if plataforma not in limites:
        print(f"UEP '{plataforma}' não encontrada em limitesPN.py")
        return df_plataforma

    if not pd.api.types.is_datetime64_any_dtype(df_plataforma['Date']):
        df_plataforma['Date'] = pd.to_datetime(df_plataforma['Date']).dt.date

    if "Classe" not in df_plataforma.columns:
        df_plataforma["Classe"] = "Original"

    df_agg = df_plataforma.groupby("Date").agg(
        Qg=("Qg Pot(mil m3/day)", "sum"),
        Qgl=("Qgl Pot(mil m3/day)", "sum")
    ).reset_index()

    df_agg["Qgt"] = df_agg["Qg"] + df_agg["Qgl"]

    for idx, row in df_agg.iterrows():
        data_row = row["Date"]
        limit_Qg = get_limit_for_date(limites[plataforma].get("Qg"), data_row)

        if limit_Qg is None:
            print(f"Sem limite Qg para {plataforma} em {data_row}")
            continue

        valor_Qgt = row.get("Qgt", 0.0)
        exceed = valor_Qgt - limit_Qg

        if exceed > 0.01:
            # Calcular rateio para esta data
            rateio_info = calcular_rateio_campo_zp(df_plataforma, data_row, "Qg Pot(mil m3/day)")

            mask_data = (df_plataforma["Date"] == data_row)
            subDF = df_plataforma.loc[mask_data].copy()

            group_cols = ["Versao", "Cenario", "Campo", "ZP", "NZP", "UEP", "Well", "Date"]
            sum_cols = [
                "Qo Pot(m3/day)", "Qw Pot(m3/day)", "Qg Pot(mil m3/day)",
                "Qgco2 Pot(mil m3/day)", "Qghc Pot(mil m3/day)"
            ]

            agg_dict = {col: 'sum' for col in sum_cols if col in subDF.columns}
            subDF_agg = subDF.groupby(group_cols, as_index=False).agg(agg_dict)
            subDF_agg[coluna] = projeto_in

            subDF_agg["Qo_i"] = subDF_agg["Qo Pot(m3/day)"]
            subDF_agg["Qg_i"] = subDF_agg["Qg Pot(mil m3/day)"]
            subDF_agg["Qgco2_i"] = subDF_agg["Qgco2 Pot(mil m3/day)"]

            @log_execution
            def calc_rgo(row):
                return row["Qg_i"] / row["Qo_i"] if row["Qo_i"] > 0 else 0

            subDF_agg["RGO"] = subDF_agg.apply(calc_rgo, axis=1)
            subDF_agg = apply_priority_sorting(subDF_agg, ['RGO'], priority)

            adjustment_rows = []
            still_to_cut = exceed

