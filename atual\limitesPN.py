from Auxiliares.modules.logging_module import register_standalone_script
register_standalone_script()

limites = {
    "P-57": {
        "Ql": 52148,
        "Qo": 28620,
        "Qg": 2000,
        "Qw": 43000,
        "Qwi": 57600,
    },
    "P-58": {
        "Ql": 47700,
        "Qo": 28620,
        "Qg": 5400,
        "Qw": {":2029-05-01": 28690,
               "2029-06-01:": 43000,
        },
        # 'Qw': 28690,
        # 'Qw': 43000,
        "Qwi": {":2030-05-01": 45360,
               "2030-06-01:": 57600,
        },
        # "Qwi": 45360,
        # "Qwi": 57600,
        "Qgi": 0,
    },
    "CDAN": {
        "Ql": 15899,
        "Qo": 15900,
        "Qg": 3500,
        "Qw": 14446,
        "Qwi": 17489,
    },
    # "IPB": {
    #     "Ql": 38200,
    #     "Qo": 15900,
    #     "Qg": 5000,
    #     "Qw": 28700,
    #     "Qwi": 52500,
    # },
    "MQT": {
        "Ql": 38200,
        "Qo": 15900,
        "Qg": 5000,
        "Qw": 28700,
        "Qwi": 52500,
    },
    "ICSPB": {
        "Ql": 28618,
        "Qo": 15899,
        "Qg": 5000,
        "Qw": 22258,
        "Qwi": 25438,
    },
}