# python ranking.py <caminho_para_runFolder>
# python ranking.py <caminho_para_runFolder> --initial_positions_path <caminho_para_arquivo_excel>

import os
import sys
import colorsys
import pandas as pd
from glob import glob
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from yattag import Doc, indent
import openpyxl
from openpyxl.styles import Font, Alignment
import argparse
from ensemble_report import find_new_wells, find_new_wells_from_excel
from Auxiliares.modules.logging_module import log_execution

@log_execution
def find_first_wcoord_file(directory):
    wcoord_pattern = os.path.join(directory, "*.wcoord")
    wcoord_files = glob(wcoord_pattern)
    return wcoord_files[0] if wcoord_files else None

@log_execution
def read_wcoord_files(run_folder):
    attempt_pattern = os.path.join(run_folder, "ATTEMPT_*")
    attempt_dirs = glob(attempt_pattern)
    total_attempts = len(attempt_dirs)
    print(f"Encontrados {total_attempts} diretórios ATTEMPT_XXXX")
    dfs = []
    for i, attempt_dir in enumerate(attempt_dirs, 1):
        wcoord_path = find_first_wcoord_file(attempt_dir)
        if wcoord_path and os.path.exists(wcoord_path):
            df = pd.read_csv(wcoord_path)
            dfs.append(df)
            print(f"Processado {i}/{total_attempts}: {os.path.basename(attempt_dir)}")
        else:
            print(f"Arquivo .wcoord não encontrado em {i}/{total_attempts}: {os.path.basename(attempt_dir)}")
    if not dfs:
        print("Nenhum arquivo .wcoord foi encontrado.")
        return None
    combined_df = pd.concat(dfs, ignore_index=True)
    unique_df = combined_df.drop_duplicates(subset="Poço")
    return unique_df

@log_execution
def generate_colors(n):
    HSV_tuples = [(x * 1.0 / n, 0.5, 0.5) for x in range(n)]
    RGB_tuples = map(lambda x: colorsys.hsv_to_rgb(*x), HSV_tuples)
    return ['rgb' + str(tuple(int(255*x) for x in rgb)) for rgb in RGB_tuples]

@log_execution
def add_well_scatter(fig, well_coords, new_wells_results, row, col):
    # Adiciona todos os poços em cinza
    fig.add_trace(
        go.Scatter(
            x=well_coords['x'],
            y=well_coords['y'],
            mode='markers',
            marker=dict(color='darkgrey', size=8),
            text=well_coords['Poço'],
            name='Todos os Poços',
            showlegend=False
        ),
        row=row, col=col
    )

    # Gera cores dinamicamente baseado no número de Attempts
    colors = generate_colors(len(new_wells_results))

    # Adiciona os poços do Attempt individualmente
    for i, (attempt, new_wells) in enumerate(new_wells_results.items()):
        color = colors[i % len(colors)]  # Seleciona uma cor da paleta

        for poco in new_wells:
            well = well_coords[well_coords['Poço'] == poco]
            if not well.empty:
                fig.add_trace(
                    go.Scatter(
                        x=[well['x'].values[0]],
                        y=[well['y'].values[0]],
                        mode='markers',
                        marker=dict(color=color, size=8),
                        text=poco,
                        name=poco,
                        legendgroup=attempt,
                        showlegend=True
                    ),
                    row=row, col=col
                )

        # Adiciona uma entrada de legenda para o grupo (attempt)
        fig.add_trace(
            go.Scatter(
                x=[None],
                y=[None],
                mode='markers',
                marker=dict(color=color, size=8),
                name=attempt,
                legendgroup=attempt,
                showlegend=True
            ),
            row=row, col=col
        )

@log_execution
def add_bars(fig, df, column, row, color):
    for i, (index, row_data) in enumerate(df.iterrows()):
        fig.add_trace(go.Bar(
            x=[row_data['Attempt']],
            y=[row_data[column]],
            name=row_data['Attempt'],
            legendgroup=row_data['Attempt'],
            showlegend=False,  # Não mostra na legenda, pois já está no scatter
            marker_color=color),
            row=row, col=1
        )

@log_execution
def generate_links_html(result):
    doc, tag, text = Doc().tagtext()

    with tag('div', style='margin-top: 20px; padding: 10px; background-color: #f0f0f0;'):
        with tag('h3'):
            text('Links para Relatórios:')
        with tag('ul', style='list-style-type: none; padding: 0;'):
            for _, row in result.iterrows():
                report_path = row['Report'].replace("L:\\", "\\\\dfs.petrobras.biz\\cientifico\\")
                with tag('li', style='margin-bottom: 5px;'):
                    with tag('a', href=report_path, target='_blank'):
                        text(row['Attempt'])

    return indent(doc.getvalue())

@log_execution
def read_ensemble_reports(run_folder):
    attempt_pattern = os.path.join(run_folder, "ATTEMPT_*")
    attempt_dirs = glob(attempt_pattern)
    total_attempts = len(attempt_dirs)
    print(f"Encontrados {total_attempts} diretórios ATTEMPT_XXXX")
    dfs = []
    for i, attempt_dir in enumerate(attempt_dirs, 1):
        csv_path = os.path.join(attempt_dir, "ensemble_report.csv")
        if os.path.exists(csv_path):
            # Lê o CSV sem definir um index
            df = pd.read_csv(csv_path)

            # Adiciona o nome do diretório como uma nova coluna
            attempt_name = os.path.basename(attempt_dir)
            df['Attempt'] = attempt_name

            dfs.append(df)
            print(f"Processado {i}/{total_attempts}: {attempt_name}")
        else:
            print(f"Arquivo não encontrado em {i}/{total_attempts}: {os.path.basename(attempt_dir)}")

    if not dfs:
        print("Nenhum arquivo ensemble_report.csv encontrado.")
        return None

    print(f"\nCombinando {len(dfs)} arquivos CSV...")
    result = pd.concat(dfs, axis=0)
    return result

@log_execution
def generate_table_html(result_pivoted, run_folder):
    doc, tag, text = Doc().tagtext()

    with tag('table', id='dataTable', klass='display'):
        with tag('thead'):
            with tag('tr'):
                with tag('th'):
                    text('Attempt')
                for column in result_pivoted.columns.levels[0]:
                    for entity in result_pivoted.columns.levels[1]:
                        with tag('th'):
                            text(f"{column}.{entity.rstrip('-PRO')}")
        with tag('tbody'):
            for index, row in result_pivoted.iterrows():
                with tag('tr'):
                    with tag('td'):
                        report_path = os.path.join(run_folder, index, "ensemble_report.html")
                        with tag('a', href=report_path, target='_blank'):
                            text(index)
                    for column in result_pivoted.columns.levels[0]:
                        for entity in result_pivoted.columns.levels[1]:
                            with tag('td'):
                                value = row[column][entity]
                                text(f"{value:.2f}" if pd.notnull(value) else "N/A")

    return doc.getvalue()

@log_execution
def save_to_excel(result_pivoted, run_folder):
    excel_path = os.path.join(run_folder, "ranking.xlsx")
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Ranking"

    # Escrever cabeçalhos
    ws.cell(row=1, column=1, value="Attempt").font = Font(bold=True)
    col = 2
    for column in result_pivoted.columns.levels[0]:
        for entity in result_pivoted.columns.levels[1]:
            header = f"{column}.{entity.rstrip('-PRO')}"
            ws.cell(row=1, column=col, value=header).font = Font(bold=True)
            col += 1

    # Escrever dados
    for row, (index, data) in enumerate(result_pivoted.iterrows(), start=2):
        ws.cell(row=row, column=1, value=index)
        col = 2
        for column in result_pivoted.columns.levels[0]:
            for entity in result_pivoted.columns.levels[1]:
                value = data[column][entity]
                if pd.notnull(value):
                    ws.cell(row=row, column=col, value=value).number_format = '0.00'
                else:
                    ws.cell(row=row, column=col, value="N/A")
                col += 1

    # Ajustar largura das colunas
    for column in ws.columns:
        max_length = 0
        column = [cell for cell in column]
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(cell.value)
            except:
                pass
        adjusted_width = (max_length + 2)
        ws.column_dimensions[column[0].column_letter].width = adjusted_width

    # Centralizar todas as células
    for row in ws.iter_rows():
        for cell in row:
            cell.alignment = Alignment(horizontal='center', vertical='center')

    wb.save(excel_path)
    print(f"Arquivo Excel salvo em: {excel_path}")

@log_execution
def main(run_folder, initial_positions_path=None):
    well_coords = read_wcoord_files(run_folder)
    if well_coords is None:
        print("Não foi possível criar o DataFrame de coordenadas dos poços.")
        return

    result = read_ensemble_reports(run_folder)
    if result is None:
        return

    # Pivotando o DataFrame
    result_pivoted = result.pivot_table(
        values=['DNpe*', 'DNpe', 'DNp', 'DGp'],
        index='Attempt',
        columns='Entity',
        aggfunc='first'
    )
    print(result_pivoted)
    # input("Pressione Enter para continuar...")

    save_to_excel(result_pivoted, run_folder)

    # Selecionando apenas os dados de FIELD-PRO
    result_field_pro = result_pivoted.xs('FIELD-PRO', axis=1, level=1)
    print(result_field_pro)
    # input("Pressione Enter para continuar...")

    # Se você quiser resetar o índice para ter 'Attempt' como uma coluna
    result_field_pro = result_field_pro.reset_index()
    print(result_field_pro)
    # input("Pressione Enter para finalizar...")

    # Chamando find_new_wells ou find_new_wells_from_excel para cada Attempt
    base_folder = os.path.join(run_folder, "BASE")
    new_wells_results = {}

    for attempt in result_field_pro['Attempt']:
        attempt_folder = os.path.join(run_folder, attempt)
        if initial_positions_path:
            new_wells = find_new_wells_from_excel(attempt_folder, initial_positions_path)
        else:
            new_wells = find_new_wells(attempt_folder, base_folder)
        new_wells_results[attempt] = new_wells

    # Imprimindo os resultados
    for attempt, wells in new_wells_results.items():
        print(f"Novos poços em {attempt}:")
        print(wells)
        print()

    # input("Pressione Enter para finalizar...")

    # result = result_field_pro[result['Entity'] == 'FIELD-PRO']
    result_sorted_dnpe_star = result_field_pro.sort_values('DNpe*', ascending=False)
    result_sorted_dnpe = result_field_pro.sort_values('DNpe', ascending=False)
    result_sorted_dnp = result_field_pro.sort_values('DNp', ascending=False)
    result_sorted_dgp = result_field_pro.sort_values('DGp', ascending=False)

    csv_output_path = os.path.join(run_folder, "ranking.csv")
    # result.to_csv(csv_output_path, index=False)
    result_field_pro.to_csv(csv_output_path, index=False)
    print(f"Ranking salvo em: {csv_output_path}")

    fig = make_subplots(rows=5, cols=1,
                        specs=[
                            [{"type": "xy"}],
                            [{"type": "bar"}],
                            [{"type": "bar"}],
                            [{"type": "bar"}],
                            [{"type": "bar"}]
                        ],
                        vertical_spacing=0.05,
                        subplot_titles=(
                            "Mapa de Poços",
                            f"DNpe* P50 ({len(result)} Attempts)",
                            f"DNp P50 + DGp P50 ({len(result)} Attempts)",
                            f"DNp P50 ({len(result)} Attempts)",
                            f"DGp P50 ({len(result)} Attempts)"
                        )
    )

    add_well_scatter(fig, well_coords, new_wells_results, 1, 1)

    add_bars(fig, result_sorted_dnpe_star, 'DNpe*', 2, 'blue')

    for i, (index, row) in enumerate(result_sorted_dnpe.iterrows()):
        fig.add_trace(go.Bar(
            x=[row['Attempt']],
            y=[row['DNp']],
            name=row['Attempt'],
            legendgroup=row['Attempt'],
            showlegend=False,
            marker_color='green'),
            row=3, col=1
        )
        fig.add_trace(go.Bar(
            x=[row['Attempt']],
            y=[row['DGp']],
            name=row['Attempt'],
            legendgroup=row['Attempt'],
            showlegend=False,
            marker_color='red'),
            row=3, col=1
        )

    add_bars(fig, result_sorted_dnp, 'DNp', 4, 'green')
    add_bars(fig, result_sorted_dgp, 'DGp', 5, 'red')

    # Configurar o layout do gráfico de dispersão para ter a mesma escala em x e y
    fig.update_xaxes(title_text="X", row=1, col=1, scaleanchor="y", scaleratio=1)
    fig.update_yaxes(title_text="Y", row=1, col=1)

    # Calcular o centro do mapa
    x_center = (well_coords['x'].max() + well_coords['x'].min()) / 2
    y_center = (well_coords['y'].max() + well_coords['y'].min()) / 2

    # Calcular a extensão do mapa
    x_range = well_coords['x'].max() - well_coords['x'].min()
    y_range = well_coords['y'].max() - well_coords['y'].min()
    max_range = max(x_range, y_range)

    # Definir os limites do mapa
    fig.update_xaxes(range=[x_center - max_range/2, x_center + max_range/2], row=1, col=1)
    fig.update_yaxes(range=[y_center - max_range/2, y_center + max_range/2], row=1, col=1)

    fig.update_layout(
        height=3000,
        showlegend=True,
        legend=dict(
            orientation="v",
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.02,
            itemsizing='constant'
        ),
        barmode='stack',
        margin=dict(t=100, b=50, l=50, r=50),
    )

    # Garantir que o gráfico de dispersão seja quadrado
    fig.update_layout(yaxis2=dict(scaleanchor="x2", scaleratio=1))

    fig.update_xaxes(categoryorder='array', categoryarray=result_sorted_dnpe_star['Attempt'].tolist(), row=2, col=1)
    fig.update_xaxes(categoryorder='array', categoryarray=result_sorted_dnpe['Attempt'].tolist(), row=3, col=1)
    fig.update_xaxes(categoryorder='array', categoryarray=result_sorted_dnp['Attempt'].tolist(), row=4, col=1)
    fig.update_xaxes(categoryorder='array', categoryarray=result_sorted_dgp['Attempt'].tolist(), row=5, col=1)

    html_output_path = os.path.join(run_folder, "ranking.html")
    plot_html = fig.to_html(full_html=False, include_plotlyjs='cdn')
    # links_html = generate_links_html(result)

    doc, tag, text = Doc().tagtext()

    doc.asis('<!DOCTYPE html>')
    with tag('html'):
        with tag('head'):
            with tag('title'):
                text('Ranking de Poços')
            doc.asis('<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.25/css/jquery.dataTables.css">')
            doc.asis('<script type="text/javascript" charset="utf8" src="https://code.jquery.com/jquery-3.6.0.min.js"></script>')
            doc.asis('<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.js"></script>')
        with tag('body'):
            doc.asis(plot_html)
            doc.asis(generate_table_html(result_pivoted, run_folder))
            with tag('script'):
                doc.asis('''
                    $(document).ready( function () {
                        $('#dataTable').DataTable({
                            scrollX: true,
                            scrollY: '500px',
                            scrollCollapse: true,
                            paging: true,
                            fixedColumns: {
                                left: 1
                            }
                        });
                    } );
                ''')
            # Adicionar link para download do Excel
            with tag('div', style='margin-top: 20px; text-align: center;'):
                with tag('a', href='ranking.xlsx', download='ranking.xlsx'):
                    text('Download Ranking Excel')

    html_output_path = os.path.join(run_folder, "ranking.html")
    with open(html_output_path, 'w', encoding='utf-8') as f:
        f.write(indent(doc.getvalue()))

    print(f"Arquivo HTML interativo com gráficos, tabela e links salvo em: {html_output_path}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Generate ranking report")
    parser.add_argument("run_folder", help="Path to the run folder containing ATTEMPT_XXXX directories")
    parser.add_argument("--initial_positions_path", help="Path to the Excel file containing initial positions")
    args = parser.parse_args()

    main(args.run_folder, args.initial_positions_path)
