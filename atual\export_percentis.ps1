#####################################################################################################################################
# Entrada
#####################################################################################################################################

# excel de saida
$out_directory = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\04_PN2630_SelectV1\Sim\Docs"
$out_path = "$out_directory\pocos_087_percentis.xlsx"

# Definir diretorios de entrada
$dir_proj = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\04_PN2630_SelectV1\Sim\run\ATTEMPT_0008"
$dir_base = "L:\res\campos\jubarte\er\er03\JUB_PRB_MCBCOQ\2025\04_PN2630_SelectV1\Sim\run\ATTEMPT_0001"
# $dir_base = "None"

# Realizacoes
$realizations = "087"

# Definir quais figuras serao apresentadas
$plots = @(
    # "Npe*,m3,-1"
    # "Npe,m3,-1"
    "Npe*,m3,2060-01-01"
    "Npe,m3,2060-01-1"
    "Qo,m3/day,2025-01-01"
    "BSW,fraction,2025-01-01"
    "Qo,m3/day,2025-02-01"
    "BSW,fraction,2025-02-01"
    "Qo,m3/day,0"
)

#####################################################################################################################################
# Execucao
#####################################################################################################################################

# Definir diretorio do script dinamicamente
$script_dir = $PSScriptRoot # Diretorio do repositorio

$plots_array = @()
foreach ($plot in $plots) {
    $plots_array += $plot
}

# Definir variaveis
$python = "L:\res\campos\jubarte\er\er02\00_Programas\python\windows\python-3.12.6.amd64\python.exe"
$script = "$script_dir\ensemble.py"
$command = "rank_realizations"

# Comando a ser executado
Write-Output "$python $script $command $dir_proj $dir_base --realizations $realizations --export_path $out_path $plots_array"

# Executando
& $python $script $command $dir_proj $dir_base --realizations $realizations --export_path $out_path $plots_array

#####################################################################################################################################
# Salvando o script atual no diretorio de saida
#####################################################################################################################################

if ($out_path -ne "") {
    # Extrair o caminho completo sem a extensao do arquivo
    $file_name_without_extension = [System.IO.Path]::GetFileNameWithoutExtension($out_path)
    $out_directory = Split-Path -Path $out_path -Parent
    $path_without_extension = Join-Path -Path $out_directory -ChildPath $file_name_without_extension

    # Caminho do novo script a ser salvo
    $script_output_path = "$path_without_extension.ps1"

    # Obter o conteudo do script atual
    $script_content = Get-Content -Path $MyInvocation.MyCommand.Path

    # Substituir a variavel $script_dir pelo diretorio original
    $script_content = $script_content -replace ' = \$PSScriptRoot # Diretorio do repositorio', " = `"$script_dir`" # Diretorio do repositorio"

    # Salvar o conteudo no diretorio de saida
    $script_content | Out-File -FilePath $script_output_path -Encoding UTF8

    # Exibir mensagem de confirmacao
    Write-Output "O script foi salvo em: $script_output_path"

    Write-Output "Segue o comando para executar novamente:"
    Write-Output "powershell.exe -ExecutionPolicy Bypass -File $script_output_path"
}

# Aguarda uma tecla ao final
Write-Host "`nPressione qualquer tecla para finalizar..." -ForegroundColor Yellow
Read-Host