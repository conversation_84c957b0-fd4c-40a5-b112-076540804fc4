#####################################################################################################################################
# PathUtils.ps1 - Funcoes para manipulacao de caminhos
#####################################################################################################################################

function GetAbsolutePath {
    <#
    .SYNOPSIS
    Converte um caminho relativo em absoluto

    .PARAMETER Path
    Caminho a ser convertido

    .PARAMETER BaseDir
    Diretorio base para caminhos relativos
    #>
    param (
        [string]$Path,
        [string]$BaseDir = $PSScriptRoot
    )

    if (-not [System.IO.Path]::IsPathRooted($Path)) {
        return Join-Path -Path $BaseDir -ChildPath $Path
    } else {
        return $Path
    }
}

function ConvertTo-AbsolutePaths {
    <#
    .SYNOPSIS
    Converte todos os caminhos de projetos para absolutos

    .PARAMETER Projects
    Hashtable com os projetos

    .PARAMETER ScriptRoot
    Diretorio base do script
    #>
    param (
        [hashtable]$Projects,
        [string]$ScriptRoot
    )

    $updatedProjects = $Projects.Clone()

    foreach ($projectName in $updatedProjects.Keys) {
        $updatedProjects[$projectName]["proj"] = GetAbsolutePath -Path $updatedProjects[$projectName]["proj"] -BaseDir $ScriptRoot

        if ($updatedProjects[$projectName]["base"] -ne "None") {
            $updatedProjects[$projectName]["base"] = GetAbsolutePath -Path $updatedProjects[$projectName]["base"] -BaseDir $ScriptRoot
        }
    }

    return $updatedProjects
}

function Test-ProjectPaths {
    <#
    .SYNOPSIS
    Verifica se todos os caminhos dos projetos existem

    .PARAMETER Projects
    Hashtable com os projetos
    #>
    param (
        [hashtable]$Projects
    )

    Write-Host "Verificando existencia dos caminhos em cada projeto..." -ForegroundColor Cyan

    $allPathsValid = $true

    foreach ($projectName in $Projects.Keys) {
        Write-Host ""
        Write-Host "--> Projeto: $projectName" -ForegroundColor Green

        $projPath = $Projects[$projectName].proj
        $basePath = $Projects[$projectName].base

        # Verificando se existe o caminho proj
        if (Test-Path $projPath) {
            Write-Host "    OK - proj: $projPath existe." -ForegroundColor DarkGreen
        } else {
            Write-Host "    ERRO - proj: $projPath NAO existe!" -ForegroundColor Red
            $allPathsValid = $false
        }

        # Verificando se existe o caminho base
        if ($basePath -and $basePath -ne "None") {
            if (Test-Path $basePath) {
                Write-Host "    OK - base: $basePath existe." -ForegroundColor DarkGreen
            } else {
                Write-Host "    ERRO - base: $basePath NAO existe!" -ForegroundColor Red
                $allPathsValid = $false
            }
        } else {
            Write-Host "    Observacao - base esta vazio ou 'None'. Nada a verificar." -ForegroundColor Yellow
        }
    }

    Write-Host ""
    Write-Host "Verificacao concluida." -ForegroundColor Cyan
    Write-Host ""

    if (-not $allPathsValid) {
        throw "Alguns caminhos de projeto nao foram encontrados. Verifique os caminhos e tente novamente."
    }
}

function Get-ExportPath {
    <#
    .SYNOPSIS
    Determina o diretorio de exportacao

    .PARAMETER ScriptRoot
    Diretorio do script
    #>
    param (
        [string]$ScriptRoot
    )

    if (-not $ScriptRoot) {
        Write-Host "O script nao esta sendo executado a partir de um arquivo salvo. Definindo um caminho padrao para exportacao..." -ForegroundColor Yellow
        return "C:\Temp"
    } else {
        return $ScriptRoot
    }
}