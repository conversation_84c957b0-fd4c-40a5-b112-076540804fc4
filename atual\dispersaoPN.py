import datetime
import os
import pandas as pd
import numpy as np
from tqdm import tqdm
from ajustadorPN import ler_ordem_projetos
from Auxiliares.modules.logging_module import log_execution

@log_execution
def processar_dados_planilha(arquivo_entrada, arquivo_saida):
    """
    Processa um arquivo Excel aplicando multiplicadores conforme regras de dispersão
    e adiciona novos registros com cenário modificado.

    Args:
        arquivo_entrada (str): Caminho do arquivo Excel de entrada
        arquivo_saida (str): Caminho do arquivo Excel de saída
    """
    # Carregar dados
    print("\nCarregando arquivo...")
    df = carregar_dados(arquivo_entrada)

    # Obter configurações
    config = obter_configuracoes()
    identifiers = config['identifiers']
    value_columns = config['value_columns']
    dispersao = config['dispersao']
    reference = config.get('reference', {})
    output_config = config.get('output', {})

    # Filtrar dados de referência
    print("Filtrando dados base...")
    df_referencia = filtrar_referencia(df, reference)

    # Processar dados de referência
    if not df_referencia.empty:
        # Processar regras
        print("Extraindo regras...")
        rules = extrair_regras(dispersao, identifiers, value_columns)
        default_multipliers = obter_multipliers_padrao(dispersao, value_columns)

        # Aplicar regras com progresso
        print("Aplicando multiplicadores:")
        df_processado = aplicar_regras(df_referencia, rules, default_multipliers,
                                     identifiers, value_columns)

        # Ajustar processado com base nas tendências do referencia
        # print("Ajustando processado baseado nas tendências do referencia...")
        # df_ref = df_referencia.copy()
        # df_proc = df_processado.copy()
        # df_proc_ajustado = ajustar_proc_base_ref(df_ref, df_proc, config, identifiers, value_columns)
        # df_processado = df_proc_ajustado

        # Aplicar configurações de output
        print("Configurando output...")
        df_processado = aplicar_output(df_processado, output_config)

        # Concatenar dados
        print("Gerando dataset final...")
        df_final = pd.concat([df, df_processado], ignore_index=True)
    else:
        df_final = df
    # Salvar resultado
    print("\nSalvando resultado...")
    salvar_resultado(df_final, arquivo_saida, df_processado)
    # salvar_resultado(df_final, arquivo_saida)

@log_execution
def ajustar_proc_base_ref(df_ref, df_proc, config, identifiers, value_columns):
    """
    Ajusta o dataframe processado garantindo a consistência nas reduções de Qo
    com base na referência, seguindo a ordem cumulativa dos projetos.
    Filtra hierarquicamente por Campo, ZP, UEP e Well.
    """
    # Carregar ordem dos projetos
    ordem_projetos_path = config.get('ordem_projetos_path')
    ordem_projetos = ler_ordem_projetos(ordem_projetos_path)

    # Configurações para debug
    data_alvo = pd.to_datetime('2025-10-01')
    poco_alvo = '6-JUB-48A-ESS'
    iupi_alvo = "NO.P-58"

    for i in tqdm(range(1, len(ordem_projetos)+1), desc="Processando projetos"):
        try:
            iupis_atuais = ordem_projetos[:i]
            iupi_atual = iupis_atuais[-1]
            projeto_atual = df_ref.loc[df_ref['IUPI'] == iupi_atual, 'Projeto'].iloc[0]

            # Para cada iteração do projeto, precisamos recarregar todos os dataframes filtrados
            # Filtrar dados para projetos acumulados
            df_ref_acumulado = df_ref[df_ref['IUPI'].isin(iupis_atuais)].copy()
            df_proc_acumulado = df_proc[df_proc['IUPI'].isin(iupis_atuais)].copy()

            # Obter todos os campos únicos
            campos = df_ref_acumulado['Campo'].unique()

            for campo in tqdm(campos, desc=f"Campos para IUPI {iupi_atual}", leave=False):
                # Recarregar filtro por Campo após cada modificação no df_proc
                df_ref_campo = df_ref_acumulado[df_ref_acumulado['Campo'] == campo]
                df_proc_campo = df_proc_acumulado[df_proc_acumulado['Campo'] == campo]

                # Obter todas as ZPs únicas para este Campo
                zps = df_ref_campo['ZP'].unique()

                for zp in tqdm(zps, desc=f"ZPs em {campo}", leave=False):
                    # Recarregar filtro por ZP após cada modificação no df_proc
                    df_ref_zp = df_ref_campo[df_ref_campo['ZP'] == zp]
                    df_proc_zp = df_proc_campo[df_proc_campo['ZP'] == zp]

                    # Obter todas as UEPs únicas para esta ZP
                    ueps = df_ref_zp['UEP'].unique()

                    for uep in tqdm(ueps, desc=f"UEPs em {zp}", leave=False):
                        # Recarregar filtro por UEP após cada modificação no df_proc
                        df_ref_uep = df_ref_zp[df_ref_zp['UEP'] == uep]
                        df_proc_uep = df_proc_zp[df_proc_zp['UEP'] == uep]

                        # Obter todos os poços únicos para esta UEP
                        wells = df_ref_uep['Well'].unique()

                        for well in tqdm(wells, desc=f"Poços em {uep}", leave=False):
                            # Precisamos processar cada poço potencialmente várias vezes
                            # até que não haja mais ajustes necessários
                            ajustes_feitos = True
                            iteracao_poco = 0
                            max_iteracoes = 1000000  # Limite para evitar loops infinitos

                            while ajustes_feitos and iteracao_poco < max_iteracoes:
                                iteracao_poco += 1
                                ajustes_feitos = False

                                # Recarregar filtro por Well após cada modificação no df_proc
                                df_ref_well = df_ref_uep[df_ref_uep['Well'] == well]
                                df_proc_well = df_proc_uep[df_proc_uep['Well'] == well]

                                # Somar valores por data (mantendo as colunas de identificação)
                                df_ref_by_date = df_ref_well.groupby('Date')[value_columns].sum().reset_index()
                                df_proc_by_date = df_proc_well.groupby('Date')[value_columns].sum().reset_index()

                                # Ordenar por data
                                df_ref_by_date = df_ref_by_date.sort_values('Date')
                                df_proc_by_date = df_proc_by_date.sort_values('Date')

                                if len(df_ref_by_date) < 2 or len(df_proc_by_date) < 2:
                                    break  # Não há datas suficientes para comparar

                                # Verificar reduções sequenciais
                                for idx in range(1, len(df_ref_by_date)):
                                    ref_atual = df_ref_by_date.iloc[idx]
                                    ref_anterior = df_ref_by_date.iloc[idx-1]

                                    # Encontrar entradas correspondentes no processado
                                    proc_atual_mask = df_proc_by_date['Date'] == ref_atual['Date']
                                    proc_anterior_mask = df_proc_by_date['Date'] == ref_anterior['Date']

                                    if not any(proc_atual_mask) or not any(proc_anterior_mask):
                                        continue

                                    proc_atual = df_proc_by_date[proc_atual_mask].iloc[0]
                                    proc_anterior = df_proc_by_date[proc_anterior_mask].iloc[0]

                                    # Debug para data e poço específicos
                                    debug_ativo = (pd.to_datetime(ref_atual['Date']) == data_alvo and
                                                 well == poco_alvo and
                                                 iupi_atual == iupi_alvo)

                                    # if debug_ativo:
                                    #     print("\n===== DEBUG INFO =====")
                                    #     print(f"Data: {ref_atual['Date']}, Poço: {well}")
                                    #     print(f"UEP: {uep}, Campo: {campo}, ZP: {zp}")
                                    #     print(f"Projeto: {projeto_atual}, IUPI: {iupi_atual}")
                                    #     print(f"Iteração do poço: {iteracao_poco}")
                                    #     print("\nproc_atual:")
                                    #     print(proc_atual)
                                    #     print("\nproc_anterior:")
                                    #     print(proc_anterior)
                                    #     print("\nref_atual:")
                                    #     print(ref_atual)
                                    #     print("\nref_anterior:")
                                    #     print(ref_anterior)
                                    #     print("=====================\n")
                                    #     input("Pressione Enter para continuar...")

                                    # Verificar necessidade de ajuste
                                    if (ref_atual['Qo Pot(m3/day)'] < ref_anterior['Qo Pot(m3/day)'] * 100.1 / 100 and
                                        proc_atual['Qo Pot(m3/day)'] > proc_anterior['Qo Pot(m3/day)']):

                                        # Calcular diferenças
                                        dqo = proc_atual['Qo Pot(m3/day)'] - proc_anterior['Qo Pot(m3/day)']

                                        # Calcular parâmetros auxiliares
                                        with np.errstate(divide='ignore', invalid='ignore'):
                                            rgo = proc_atual['Qg Pot(mil m3/day)'] / proc_atual['Qo Pot(m3/day)']
                                            bsw = proc_atual['Qw Pot(m3/day)'] / (proc_atual['Qo Pot(m3/day)'] + proc_atual['Qw Pot(m3/day)'])
                                            tco2 = proc_atual['Qgco2 Pot(mil m3/day)'] / proc_atual['Qg Pot(mil m3/day)']

                                        # Substituir valores inválidos
                                        rgo = rgo if not np.isinf(rgo) and not np.isnan(rgo) else 0
                                        bsw = bsw if not np.isinf(bsw) and not np.isnan(bsw) else 0
                                        tco2 = tco2 if not np.isinf(tco2) and not np.isnan(tco2) else 0

                                        # Calcular demais ajustes
                                        dqw = dqo * bsw / (1 - bsw) if (1 - bsw) != 0 else 0
                                        dqg = dqo * rgo
                                        dqgco2 = dqg * tco2
                                        dqghc = dqg - dqgco2

                                        # Obter um registro representativo para os campos de identificação
                                        ref_row = df_ref_well[df_ref_well['Date'] == ref_atual['Date']].iloc[0]
                                        proc_row = df_proc_well[df_proc_well['Date'] == ref_atual['Date']].iloc[0]

                                        # Criar nova linha de ajuste
                                        new_line = {}

                                        # Adicionar todos os campos de identificação do registro original
                                        for id_col in identifiers:
                                            if id_col in proc_row.index:
                                                new_line[id_col] = proc_row[id_col]
                                            elif id_col == 'Campo':
                                                new_line[id_col] = campo
                                            elif id_col == 'ZP':
                                                new_line[id_col] = zp
                                            elif id_col == 'UEP':
                                                new_line[id_col] = uep
                                            elif id_col == 'Well':
                                                new_line[id_col] = well
                                            elif id_col == 'Projeto':
                                                new_line[id_col] = projeto_atual
                                            elif id_col == 'IUPI':
                                                new_line[id_col] = iupi_atual
                                            elif id_col == 'Date':
                                                new_line[id_col] = ref_atual['Date']
                                            else:
                                                # Para outras colunas, tente pegar da referência ou defina como vazio
                                                new_line[id_col] = ref_row.get(id_col, "") if id_col in ref_row.index else ""

                                        # Adicionar valores calculados
                                        new_line.update({
                                            "Qo Pot(m3/day)": -dqo,
                                            "Qw Pot(m3/day)": -dqw,
                                            "Qg Pot(mil m3/day)": -dqg,
                                            "Qgco2 Pot(mil m3/day)": -dqgco2,
                                            "Qghc Pot(mil m3/day)": -dqghc,
                                            "Qwi Pot(m3/day)": 0.0,
                                            "Qgi Pot(mil m3/day)": 0.0,
                                            "Qgl Pot(mil m3/day)": 0.0,
                                            "Qgco2i Pot(mil m3/day)": 0.0,
                                            "Qghci Pot(mil m3/day)": 0.0,
                                        })

                                        # if debug_ativo:
                                        #     print('new_line a ser adicionada:')
                                        #     print(new_line)
                                        #     input("Pressione Enter para adicionar a linha...")

                                        # Adicionar ao dataframe
                                        df_proc = pd.concat([df_proc, pd.DataFrame([new_line])], ignore_index=True)

                                        # Indicar que fizemos um ajuste e precisamos reprocessar
                                        ajustes_feitos = True

                                        # Como fizemos um ajuste, saímos do loop atual e recomeçamos
                                        # com os dados atualizados
                                        break

                                # Se não foram feitos ajustes nesta iteração, não precisamos continuar
                                if not ajustes_feitos:
                                    break

                                if debug_ativo and ajustes_feitos:
                                    print(f"Ajuste feito! Reprocessando poço {well} (iteração {iteracao_poco})")

        except Exception as e:
            print(f"Erro ao processar projeto {i} ({iupi_atual if 'iupi_atual' in locals() else 'desconhecido'}): {str(e)}")
            continue

    return df_proc

@log_execution
def filtrar_referencia(df, reference):
    """Filtra o DataFrame com base na configuração de referência"""
    if not reference:
        return pd.DataFrame()

    mask = (df[list(reference.keys())] == pd.Series(reference)).all(axis=1)
    return df[mask].copy()

@log_execution
def aplicar_output(df_processado, output_config):
    """Aplica as configurações de output ao DataFrame processado"""
    if output_config:
        for col, value in output_config.items():
            df_processado[col] = value
    return df_processado

@log_execution
def carregar_dados(arquivo):
    """Carrega o arquivo Excel para um DataFrame"""
    return pd.read_excel(arquivo)

@log_execution
def obter_configuracoes():
    """Retorna as configurações do processamento"""
    output = {
        'ordem_projetos_path': r"L:\res\campos\jubarte\er\er02\00_Programas\python\script\atual\ordem_projetos.txt",
        'reference': {"Cenario": "P50"},
        'output': {"Cenario": "P10"},
        'identifiers': [
            "Versao", "Cenario", "Campo", "ZP", "NZP", "UEP",
            "Projeto", "IUPI", "Well", "Date"
        ],
        'value_columns': [
            "Qo Pot(m3/day)", "Qw Pot(m3/day)", "Qwi Pot(m3/day)",
            "Qg Pot(mil m3/day)", "Qgi Pot(mil m3/day)", "Qgl Pot(mil m3/day)",
            "Qgco2 Pot(mil m3/day)", "Qgco2i Pot(mil m3/day)",
            "Qghc Pot(mil m3/day)", "Qghci Pot(mil m3/day)"
        ],
        "dispersao": {
            'transition_start_date': "2025-06-01",
            'transition_end_date': "2026-06-01",
            "default": {
                "default": 1.2,
                "Qo Pot(m3/day)": 1.2,
                "Qw Pot(m3/day)": 1.2 * 0.96,
                "Qwi Pot(m3/day)": 1.2,
                "Qg Pot(mil m3/day)": 1.2,
                "Qgi Pot(mil m3/day)": 1.2,
                "Qgl Pot(mil m3/day)": 1,
                "Qgco2 Pot(mil m3/day)": 1.2,
                "Qgco2i Pot(mil m3/day)": 1.2,
                "Qghc Pot(mil m3/day)": 1.2,
                "Qghci Pot(mil m3/day)": 1.2,
            },
            "ZP": {
                "MCB/COQ-ESS103A": {
                    "Projeto": {
                        "MP": {
                            "Qo Pot(m3/day)": 1.048,
                            "Qw Pot(m3/day)": 1.048 * 0.96,
                            "Qwi Pot(m3/day)": 1.048,
                            "Qg Pot(mil m3/day)": 1.054,
                            "Qgi Pot(mil m3/day)": 1.048,
                            "Qgl Pot(mil m3/day)": 1,
                            "Qgco2 Pot(mil m3/day)": 1.054,
                            "Qgco2i Pot(mil m3/day)": 1.054,
                            "Qghc Pot(mil m3/day)": 1.054,
                            "Qghci Pot(mil m3/day)": 1.054,
                        },
                        "IPB": {
                            "Qo Pot(m3/day)": 1.061,
                            "Qw Pot(m3/day)": 1.061 * 0.96,
                            "Qwi Pot(m3/day)": 1.061,
                            "Qg Pot(mil m3/day)": 1.079,
                            "Qgi Pot(mil m3/day)": 1.079,
                            "Qgl Pot(mil m3/day)": 1,
                            "Qgco2 Pot(mil m3/day)": 1.079,
                            "Qgco2i Pot(mil m3/day)": 1.079,
                            "Qghc Pot(mil m3/day)": 1.079,
                            "Qghci Pot(mil m3/day)": 1.079,
                        },
                        "PID1": {
                            "Qo Pot(m3/day)": 1.385,
                            "Qw Pot(m3/day)": 1.385 * 0.96,
                            "Qwi Pot(m3/day)": 1.385,
                            "Qg Pot(mil m3/day)": 1.381,
                            "Qgi Pot(mil m3/day)": 1.381,
                            "Qgl Pot(mil m3/day)": 1,
                            "Qgco2 Pot(mil m3/day)": 1.381,
                            "Qgco2i Pot(mil m3/day)": 1.381,
                            "Qghc Pot(mil m3/day)": 1.381,
                            "Qghci Pot(mil m3/day)": 1.381,
                        },
                        "PID2": {
                            "Qo Pot(m3/day)": 1.265,
                            "Qw Pot(m3/day)": 1.265 * 0.96,
                            "Qwi Pot(m3/day)": 1.265,
                            "Qg Pot(mil m3/day)": 1.421,
                            "Qgi Pot(mil m3/day)": 1.421,
                            "Qgl Pot(mil m3/day)": 1,
                            "Qgco2 Pot(mil m3/day)": 1.421,
                            "Qgco2i Pot(mil m3/day)": 1.421,
                            "Qghc Pot(mil m3/day)": 1.421,
                            "Qghci Pot(mil m3/day)": 1.421,
                        },
                        "EVPRO_P-58": {
                            "Qo Pot(m3/day)": 1.086,
                            "Qw Pot(m3/day)": 1.086 * 0.96,
                            "Qwi Pot(m3/day)": 1.086,
                            "Qg Pot(mil m3/day)": 1.109,
                            "Qgi Pot(mil m3/day)": 1.109,
                            "Qgl Pot(mil m3/day)": 1,
                            "Qgco2 Pot(mil m3/day)": 1.109,
                            "Qgco2i Pot(mil m3/day)": 1.109,
                            "Qghc Pot(mil m3/day)": 1.109,
                            "Qghci Pot(mil m3/day)": 1.109,
                        },
                        "PID4": {
                            "Qo Pot(m3/day)": 1.279,
                            "Qw Pot(m3/day)": 1.279 * 0.96,
                            "Qwi Pot(m3/day)": 1.279,
                            "Qg Pot(mil m3/day)": 1.374,
                            "Qgi Pot(mil m3/day)": 1.374,
                            "Qgl Pot(mil m3/day)": 1,
                            "Qgco2 Pot(mil m3/day)": 1.374,
                            "Qgco2i Pot(mil m3/day)": 1.374,
                            "Qghc Pot(mil m3/day)": 1.374,
                            "Qghci Pot(mil m3/day)": 1.374,
                        },
                        "ICSPB": {
                            "Qo Pot(m3/day)": 1.089,
                            "Qw Pot(m3/day)": 1.089 * 0.96,
                            "Qwi Pot(m3/day)": 1.089,
                            "Qg Pot(mil m3/day)": 1.102,
                            "Qgi Pot(mil m3/day)": 1.102,
                            "Qgl Pot(mil m3/day)": 1,
                            "Qgco2 Pot(mil m3/day)": 1.102,
                            "Qgco2i Pot(mil m3/day)": 1.102,
                            "Qghc Pot(mil m3/day)": 1.102,
                            "Qghci Pot(mil m3/day)": 1.102,
                        },
                    },
                },
                "MCB/COQ-PRB2": {
                    "Projeto": {
                        "ICSPB": {
                            "Qo Pot(m3/day)": 1.471,
                            "Qw Pot(m3/day)": 1.471 * 0.96,
                            "Qwi Pot(m3/day)": 1.471,
                            "Qg Pot(mil m3/day)": 1.493,
                            "Qgi Pot(mil m3/day)": 1.493,
                            "Qgl Pot(mil m3/day)": 1,
                            "Qgco2 Pot(mil m3/day)": 1.493,
                            "Qgco2i Pot(mil m3/day)": 1.493,
                            "Qghc Pot(mil m3/day)": 1.493,
                            "Qghci Pot(mil m3/day)": 1.493,
                        }
                    }
                },
                # "MCB/COQ-ESS172": {
                #     "Projeto": {
                #         "ICSPB": {
                #             "Qo Pot(m3/day)": 1.471,
                #             "Qw Pot(m3/day)": 1.471 * 0.96,
                #             "Qwi Pot(m3/day)": 1.471,
                #             "Qg Pot(mil m3/day)": 1.493,
                #             "Qgi Pot(mil m3/day)": 1.493,
                #             "Qgl Pot(mil m3/day)": 1,
                #             "Qgco2 Pot(mil m3/day)": 1.493,
                #             "Qgco2i Pot(mil m3/day)": 1.493,
                #             "Qghc Pot(mil m3/day)": 1.493,
                #             "Qghci Pot(mil m3/day)": 1.493,
                #         }
                #     }
                # },
                "RO300": {
                    "Projeto": {
                        "PID5": {
                            "Qo Pot(m3/day)": 1.10,
                            "Qw Pot(m3/day)": 1.10 * 0.96,
                            "Qwi Pot(m3/day)": 1.10,
                            "Qg Pot(mil m3/day)": 1.10,
                            "Qgi Pot(mil m3/day)": 1.10,
                            "Qgl Pot(mil m3/day)": 1,
                            "Qgco2 Pot(mil m3/day)": 1.10,
                            "Qgco2i Pot(mil m3/day)": 1.10,
                            "Qghc Pot(mil m3/day)": 1.10,
                            "Qghci Pot(mil m3/day)": 1.10,
                        }
                    }
                },
            }
        }
    }

    return output

@log_execution
def extrair_regras(dispersao, identifiers, value_columns):
    """Extrai todas as regras de dispersão em formato padronizado"""
    rules = []

    @log_execution
    def _extract_rules(node, current_conditions, depth):
        multipliers = {}

        if isinstance(node, dict):
            per_col = {}
            scalar = None

            for key in node:
                if key == 'default':
                    continue

                if key in value_columns:
                    per_col[key] = node[key]
                elif key in identifiers:
                    for value in node[key]:
                        new_conditions = current_conditions.copy()
                        new_conditions[key] = value
                        _extract_rules(node[key][value], new_conditions, depth + 1)
                else:
                    scalar = node[key]

            if scalar is not None:
                per_col = {col: scalar for col in value_columns}

            multipliers.update(per_col)
        else:
            scalar = node
            multipliers = {col: scalar for col in value_columns}

        if multipliers:
            rules.append({
                'conditions': current_conditions.copy(),
                'multipliers': multipliers,
                'depth': depth
            })

    # Extrair regras a partir das chaves superiores (excluindo 'default')
    for key in dispersao:
        if key == 'default':
            continue
        if key not in identifiers:
            continue

        for value in dispersao[key]:
            _extract_rules(dispersao[key][value], {key: value}, 1)

    # Ordenar por profundidade (mais específicas primeiro)
    rules.sort(key=lambda x: -x['depth'])

    return rules

@log_execution
def obter_multipliers_padrao(dispersao, value_columns):
    """Obtém os multiplicadores padrão"""
    default_multipliers = dispersao['default']
    return {col: default_multipliers.get(col, default_multipliers['default'])
            for col in value_columns}

@log_execution
def aplicar_regras(df, rules, default_multipliers, identifiers, value_columns):
    """Aplica todas as regras ao DataFrame com barra de progresso"""
    df = df.copy()
    total_linhas = len(df)
    barra_format = "{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]"

    # Obter datas de transição da configuração
    config = obter_configuracoes()  # Adicionar esta linha
    dispersao = config['dispersao']  # Adicionar esta linha
    transition_start = pd.to_datetime(dispersao.get('transition_start_date'))
    transition_end = pd.to_datetime(dispersao.get('transition_end_date'))

    with tqdm(total=total_linhas, desc="Processando linhas",
             bar_format=barra_format) as pbar:
        for index, row in df.iterrows():
            row_identifiers = {col: row[col] for col in identifiers}
            multipliers = default_multipliers.copy()

            # Verificar regras
            for rule in rules:
                if verificar_condicoes(rule['conditions'], row_identifiers):
                    atualizar_multipliers(multipliers, rule['multipliers'])
                    break

            # Aplicar transição progressiva
            try:
                data = pd.to_datetime(row['Date'])
                if data < transition_start:
                    fator = 0.0
                elif data > transition_end:
                    fator = 1.0
                else:
                    dias_totais = (transition_end - transition_start).days
                    dias_decorridos = (data - transition_start).days
                    fator = min(max(dias_decorridos / dias_totais, 0.0), 1.0)

                # Aplicar interpolação linear apenas se houver multiplicador diferente de 1
                for col in value_columns:
                    base = multipliers[col]
                    if base != 1.0:
                        multipliers[col] = 1 + (base - 1) * fator

            except Exception as e:
                print(f"\nAviso: Erro ao processar data na linha {index}: {str(e)}")
                continue

            # Aplicar multiplicadores
            for col in value_columns:
                df.at[index, col] *= multipliers[col]

            pbar.update(1)

    return df

@log_execution
def verificar_condicoes(conditions, row_identifiers):
    """Verifica se todas as condições são atendidas"""
    for cond_col, cond_val in conditions.items():
        if row_identifiers.get(cond_col) != cond_val:
            return False
    return True

@log_execution
def atualizar_multipliers(multipliers, new_multipliers):
    """Atualiza os multiplicadores com novos valores"""
    for col, val in new_multipliers.items():
        multipliers[col] = val

@log_execution
def salvar_resultado(df, arquivo_saida, df_processado=None):
    """Salva o DataFrame processado em um arquivo Excel"""
    if df_processado is not None:
         arquivo_saida_new = os.path.splitext(arquivo_saida)[0] + ".new.xlsx"
         df_processado.to_excel(arquivo_saida_new, index=False)
    df.to_excel(arquivo_saida, index=False)

@log_execution
def main():
    input_file  = r"L:\res\campos\jubarte\er\er03\PN\2630\C1\P10\PN2630C1_P50baseP10_novoGL.xlsx"

    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M")
    output_file = os.path.splitext(input_file)[0] + f".output.{timestamp}.xlsx"

    print(f"Iniciando processamento do arquivo: {input_file}")
    processar_dados_planilha(input_file, output_file)
    print(f"\nProcessamento concluído! Arquivo salvo em: {output_file}")

if __name__ == "__main__":
    main()