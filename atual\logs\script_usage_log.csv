<PERSON><PERSON><PERSON>,Us<PERSON><PERSON>,Times<PERSON>p,T<PERSON><PERSON>,Funcao
teste2.py,BH2A,2025-08-04 16:26:40,Principal,'N/A'
teste2.py,BH2A,2025-08-04 16:27:00,Principal,'N/A'
teste2.py,BH2A,2025-08-04 16:27:06,Principal,'N/A'
teste1.py,BH2A,2025-08-04 16:27:11,Principal,'N/A'
teste2.py,BH2A,2025-08-04 16:27:11,<PERSON><PERSON><PERSON>,'N/A'
teste1.py,BH2A,2025-08-05 16:27:14,Principal,'N/A'
teste2.py,BH2A,2025-08-05 16:27:14,<PERSON><PERSON><PERSON>,'N/A'
teste1.py,BH2A,2025-08-05 16:27:18,Principal,'N/A'
teste2.py,BH2A,2025-08-05 16:27:18,<PERSON><PERSON><PERSON>,'N/A'
teste1.py,BH2A,2025-08-06 08:36:01,Principal,'N/A'
teste2.py,BH2A,2025-08-06 08:36:01,<PERSON><PERSON><PERSON>,'N/A'
teste2.py,BH2A,2025-08-06 11:05:33,Principal,'N/A'
teste1.py,BH2A,2025-08-06 11:19:16,Principal,'N/A'
teste2.py,BH2A,2025-08-06 11:19:16,Subchamada,'N/A'
teste2.py,BH2A,2025-08-06 11:19:31,Principal,'N/A'
teste3.py,<PERSON>H2A,2025-08-06 11:19:37,Principal,'N/A'
teste1.py,BH2A,2025-08-06 11:19:37,Subchamada,'N/A'
teste2.py,BH2A,2025-08-06 11:19:37,Subchamada,'N/A'
teste3.py,BH2A,2025-08-06 11:26:45,Principal,'N/A'
teste1.py,BH2A,2025-08-06 11:26:45,Subchamada,'N/A'
teste2.py,BH2A,2025-08-06 11:26:45,Subchamada,'N/A'
teste3.py,BH2A,2025-08-07 10:16:52,Principal,'N/A'
teste1.py,BH2A,2025-08-07 10:16:52,Subchamada,'N/A'
teste2.py,BH2A,2025-08-07 10:16:52,Subchamada,'N/A'
limitesPN.py,BH2A,2025-08-07 11:52:06,Principal,'N/A'
limitesPN.py,BH2A,2025-08-08 11:28:46,Principal,'N/A'
limitesPN.py,AKP4,2025-08-08 11:29:26,Principal,'N/A'
ensemble.py,BH2A,2025-08-08 17:01:20,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-08 17:01:20,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-08 17:01:20,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-08 17:12:12,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-08 17:12:12,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-08 17:12:12,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-11 11:19:47,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-11 11:19:47,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-11 11:19:48,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-11 14:54:07,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-11 14:54:07,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-11 14:54:08,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-11 15:23:38,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-11 15:23:38,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-11 15:23:38,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-11 15:56:32,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-11 15:56:32,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-11 15:56:33,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 08:44:53,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 08:44:53,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 08:44:53,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 08:46:51,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 08:46:51,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 08:46:52,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 08:50:02,Principal,'N/A'
ensemble.py,BH2A,2025-08-12 09:29:29,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 09:29:29,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 09:29:29,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 09:31:15,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 09:31:15,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 09:31:16,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 09:34:24,Principal,'N/A'
ensemble.py,BH2A,2025-08-12 09:43:31,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 09:43:31,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 09:43:32,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 09:45:19,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 09:45:19,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 09:45:20,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 09:49:32,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 09:49:32,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 09:49:33,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 09:53:22,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 09:53:22,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 09:53:23,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 09:57:57,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 09:57:57,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 09:57:58,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 10:01:20,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 10:01:20,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 10:01:22,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 10:05:22,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 10:05:22,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 10:05:24,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 10:09:05,Principal,'N/A'
ensemble.py,WTQ8,2025-08-12 10:41:36,Principal,'N/A'
ensemble_report.py,WTQ8,2025-08-12 10:41:36,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 10:46:27,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 10:46:27,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 10:46:27,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 10:48:27,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 10:48:27,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 10:48:28,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 10:52:17,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 10:52:17,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 10:52:18,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 10:55:57,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 10:55:57,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 10:55:58,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 11:00:22,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 11:00:22,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 11:00:23,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 11:03:37,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 11:03:37,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 11:03:39,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 11:07:04,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 11:07:05,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 11:07:05,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 11:10:28,Principal,'N/A'
ensemble.py,WTQ8,2025-08-12 11:25:48,Principal,'N/A'
ensemble_report.py,WTQ8,2025-08-12 11:25:49,Subchamada,'N/A'
ensemble.py,WTQ8,2025-08-12 11:30:34,Principal,'N/A'
ensemble_report.py,WTQ8,2025-08-12 11:30:34,Subchamada,'N/A'
ensemble.py,WTQ8,2025-08-12 11:38:31,Principal,'N/A'
ensemble_report.py,WTQ8,2025-08-12 11:38:31,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 15:13:07,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 15:13:07,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 15:13:07,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 15:33:15,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 15:33:15,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 15:33:15,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 15:34:11,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 15:34:11,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 15:34:11,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 15:35:19,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 15:35:19,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 15:35:19,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 15:37:20,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 15:37:20,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 15:37:21,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 15:38:49,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 15:38:49,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 15:38:50,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 15:40:53,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 15:40:53,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 15:40:54,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 15:42:28,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 15:42:28,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 15:42:29,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 15:44:17,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 15:44:17,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 15:44:17,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 15:45:52,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 15:45:52,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 15:45:53,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 15:47:25,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 15:47:25,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 15:47:25,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 15:49:20,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 15:49:20,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 15:49:21,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 15:53:01,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 15:53:01,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 15:53:02,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 15:56:31,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 15:56:31,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 15:56:32,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 16:00:54,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 16:00:54,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 16:00:55,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 16:04:12,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 16:04:12,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 16:04:13,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 16:07:42,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 16:07:42,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 16:07:43,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 16:11:01,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 16:11:01,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 16:11:01,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 16:11:57,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 16:11:57,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 16:11:57,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 16:13:05,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 16:13:05,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 16:13:06,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 16:14:13,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 16:14:13,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 16:14:13,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 16:15:16,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 16:15:16,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 16:15:17,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 16:16:24,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 16:16:24,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 16:16:25,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 16:17:28,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 16:17:28,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 16:17:28,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 16:18:31,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 16:18:31,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 16:18:31,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 16:19:32,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 16:19:32,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 16:19:33,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 16:20:43,Principal,'N/A'
ensemble_report.py,BH2A,2025-08-12 16:20:43,Subchamada,'N/A'
cmg_out_helper.py,BH2A,2025-08-12 16:20:43,Subchamada,'N/A'
ensemble.py,BH2A,2025-08-12 16:21:37,Principal,'N/A'
ensemble.py,WTQ8,2025-08-13 08:45:43,Principal,'N/A'
ensemble_report.py,WTQ8,2025-08-13 08:45:43,Subchamada,'N/A'
ensemble.py,WTQ8,2025-08-13 10:08:05,Principal,'N/A'
ensemble_report.py,WTQ8,2025-08-13 10:08:05,Subchamada,'N/A'
ensemble.py,WTQ8,2025-08-13 10:08:05,Principal,'N/A'
ensemble_report.py,WTQ8,2025-08-13 10:08:05,Subchamada,'N/A'
processar_PO.py,BH2A,2025-08-13 13:33:20,Principal,'N/A'
processar_PO.py,BH2A,2025-08-13 13:39:33,Principal,'N/A'
processar_PO.py,BH2A,2025-08-13 13:40:19,Principal,'N/A'
processar_PO.py,BH2A,2025-08-13 13:51:04,Principal,'N/A'
processar_PO.py,BH2A,2025-08-13 13:54:19,Principal,'N/A'
processar_PO.py,BH2A,2025-08-13 14:27:37,Principal,'N/A'
processar_PO.py,BH2A,2025-08-13 14:39:06,Principal,'N/A'
processar_PO.py,BH2A,2025-08-13 14:49:23,Principal,'N/A'
processar_PO.py,BH2A,2025-08-13 15:17:48,Principal,'N/A'
processar_PO.py,BH2A,2025-08-13 15:19:59,Principal,'N/A'
limitesPN.py,BH2A,2025-08-13 16:08:50,Subchamada,'N/A'
ajustadorPN.rev2.py,BH2A,2025-08-13 16:08:50,Principal,'N/A'
ensemble.py,WTQ8,2025-08-13 16:29:31,Principal,'N/A'
ensemble_report.py,WTQ8,2025-08-13 16:29:31,Subchamada,'N/A'
limitesPN.py,AKP4,2025-08-13 18:16:53,Subchamada,'N/A'
ajustadorPN.rev2.py,AKP4,2025-08-13 18:16:53,Principal,'N/A'
limitesPN.py,BH2A,2025-08-14 09:34:18,Subchamada,'N/A'
ajustadorPN.rev2.py,BH2A,2025-08-14 09:34:18,Principal,'N/A'
processar_PO.py,BH2A,2025-08-14 10:07:14,Principal,'N/A'
limitesPN.py,BH2A,2025-08-14 10:14:18,Subchamada,'N/A'
ajustadorPN.rev2.py,BH2A,2025-08-14 10:14:18,Principal,'N/A'
limitesPN.py,BH2A,2025-08-14 10:16:28,Subchamada,'N/A'
ajustadorPN.rev2.py,BH2A,2025-08-14 10:16:28,Principal,'N/A'
limitesPN.py,BH2A,2025-08-14 11:01:30,Subchamada,'N/A'
ajustadorPN.rev2.py,BH2A,2025-08-14 11:01:30,Principal,'N/A'
ensemble.py,WTQ8,2025-08-14 14:32:19,Principal,'N/A'
ensemble_report.py,WTQ8,2025-08-14 14:32:19,Subchamada,'N/A'
ensemble.py,WTQ8,2025-08-14 14:40:32,Principal,'N/A'
ensemble_report.py,WTQ8,2025-08-14 14:40:32,Subchamada,'N/A'
ensemble.py,WTQ8,2025-08-15 11:29:14,Principal,'N/A'
ensemble_report.py,WTQ8,2025-08-15 11:29:14,Subchamada,'N/A'
limitesPN.py,BH2A,2025-08-15 12:30:37,Subchamada,'N/A'
ajustadorPN.rev2.py,BH2A,2025-08-15 12:30:37,Principal,'N/A'
ensemble.py,WTQ8,2025-08-18 09:13:30,Principal,'N/A'
ensemble.py,WTQ8,2025-08-18 09:13:42,Principal,'N/A'
ensemble_report.py,WTQ8,2025-08-18 09:13:42,Subchamada,'N/A'
ensemble.py,WTQ8,2025-08-18 09:19:48,Principal,'N/A'
ensemble_report.py,WTQ8,2025-08-18 09:19:48,Subchamada,'N/A'
ensemble.py,WTQ8,2025-08-20 11:24:14,Principal,'N/A'
ensemble_report.py,WTQ8,2025-08-20 11:24:14,Subchamada,'N/A'
ensemble.py,WTQ8,2025-08-21 10:55:48,Principal,'N/A'
ensemble_report.py,WTQ8,2025-08-21 10:55:48,Subchamada,'N/A'
teste1.py,BH2A,2025-08-25 14:55:34,Principal,main
teste2.py,BH2A,2025-08-25 14:55:34,Subchamada,minha_logica2
teste1.py,BH2A,2025-08-25 14:56:32,Principal,main
teste2.py,BH2A,2025-08-25 14:56:32,Subchamada,minha_logica2
ensemble.py,BH2A,2025-08-26 23:04:02,Principal,main
ensemble.py,BH2A,2025-08-26 23:04:02,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:04:02,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:04:03,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:04:03,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:04:03,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:04:03,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:04:03,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:04:03,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:04:04,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:04:04,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:04:04,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:04:04,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:04:04,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:04:04,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:04:04,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:04:04,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:04:04,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:04:04,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:04:04,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:04:19,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:04:19,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:04:19,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:04:19,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-26 23:05:16,Principal,main
ensemble.py,BH2A,2025-08-26 23:05:16,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:05:16,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:05:16,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:05:16,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:05:16,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:05:16,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:05:16,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:05:16,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:05:16,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:05:16,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:05:16,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:05:17,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:05:17,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:05:17,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:05:17,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:05:17,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:05:17,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:05:17,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:05:17,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:05:24,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:05:24,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:05:24,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:05:24,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-26 23:06:13,Principal,main
ensemble.py,BH2A,2025-08-26 23:06:13,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:06:13,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:06:14,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:06:15,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:06:15,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:06:16,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:06:16,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:06:16,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:06:16,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:06:16,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:06:16,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:06:16,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:06:16,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:06:16,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:06:16,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:06:16,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:06:17,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:06:18,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:06:18,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:07:24,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:07:25,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:07:25,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:07:25,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-26 23:09:31,Principal,main
ensemble.py,BH2A,2025-08-26 23:09:31,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:09:31,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:09:32,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:09:32,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:09:32,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:09:33,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:09:33,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:09:33,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:09:33,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:09:33,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:09:33,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:09:33,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:09:33,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:09:33,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:09:33,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:09:34,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:09:35,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:09:35,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:09:35,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:10:47,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:10:48,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:10:48,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:10:48,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-26 23:13:12,Principal,main
ensemble.py,BH2A,2025-08-26 23:13:12,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:13:12,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:13:13,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:13:13,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:13:13,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:13:14,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:13:14,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:13:14,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:13:14,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:13:14,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:13:14,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:13:14,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:13:14,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:13:14,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:13:14,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:13:14,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:13:14,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:13:14,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:13:14,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:14:06,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:14:06,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:14:06,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:14:06,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-26 23:15:03,Principal,main
ensemble.py,BH2A,2025-08-26 23:15:03,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:15:03,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:15:04,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:15:04,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:15:04,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:15:05,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:15:05,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:15:05,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:15:05,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:15:05,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:15:05,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:15:05,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:15:05,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:15:05,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:15:06,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:15:06,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:15:07,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:15:07,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:15:07,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:16:22,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:16:22,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:16:22,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:16:22,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-26 23:18:44,Principal,main
ensemble.py,BH2A,2025-08-26 23:18:44,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:18:44,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:18:45,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:18:45,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:18:45,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:18:47,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:18:47,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:18:47,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:18:47,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:18:47,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:18:47,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:18:47,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:18:47,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:18:47,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:18:47,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:18:47,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:18:48,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:18:48,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:18:48,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:20:19,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:20:20,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:20:20,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:20:20,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-26 23:22:55,Principal,main
ensemble.py,BH2A,2025-08-26 23:22:55,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:22:55,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:22:56,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:22:56,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:22:56,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:22:58,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:22:58,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:22:58,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:22:58,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:22:58,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:22:58,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:22:58,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:22:58,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:22:58,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:22:58,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:22:58,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:22:59,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:22:59,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:22:59,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:24:02,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:24:03,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:24:03,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:24:03,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-26 23:25:59,Principal,main
ensemble.py,BH2A,2025-08-26 23:25:59,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:25:59,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:26:00,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:26:00,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:26:00,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:26:02,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:26:02,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:26:02,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:26:02,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:26:02,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:26:02,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:26:02,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:26:02,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:26:02,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:26:02,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:26:02,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:26:03,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:26:03,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:26:03,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:27:11,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:27:12,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:27:12,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:27:12,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-26 23:29:22,Principal,main
ensemble.py,BH2A,2025-08-26 23:29:22,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:29:22,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:29:22,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:29:23,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:29:23,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:29:23,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:29:23,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:29:23,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:29:23,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:29:23,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:29:23,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:29:23,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:29:23,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:29:23,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:29:23,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:29:23,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:29:24,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:29:24,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:29:24,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:29:31,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:29:31,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:29:31,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:30:36,Principal,main
ensemble.py,BH2A,2025-08-26 23:30:36,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:30:36,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:30:36,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:30:36,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:30:36,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:30:37,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:30:37,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:30:37,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:30:37,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:30:37,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:30:37,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:30:37,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:30:37,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:30:37,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:30:37,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:30:37,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:30:38,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:30:38,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:30:38,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:30:44,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:30:44,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:30:45,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:31:47,Principal,main
ensemble.py,BH2A,2025-08-26 23:31:47,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:31:47,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:31:47,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:31:47,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:31:47,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:31:48,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:31:48,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:31:48,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:31:48,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:31:48,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:31:48,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:31:48,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:31:48,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:31:48,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:31:48,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:31:48,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:31:49,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:31:49,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:31:49,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:31:58,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:31:58,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:31:58,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:32:58,Principal,main
ensemble.py,BH2A,2025-08-26 23:32:58,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:32:58,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:32:58,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:32:58,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:32:58,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:32:59,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:32:59,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:32:59,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:32:59,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:32:59,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:32:59,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:32:59,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:32:59,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:32:59,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:32:59,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:32:59,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:32:59,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:32:59,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:32:59,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:33:08,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:33:08,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:33:08,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:34:01,Principal,main
ensemble.py,BH2A,2025-08-26 23:34:01,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:34:01,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:34:01,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:34:01,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:34:01,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:34:02,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:34:02,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:34:02,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:34:02,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:34:02,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:34:02,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:34:02,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:34:02,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:34:02,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:34:02,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:34:02,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:34:03,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:34:03,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:34:03,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:34:12,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:34:12,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:34:12,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:35:13,Principal,main
ensemble.py,BH2A,2025-08-26 23:35:14,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:35:14,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:35:14,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:35:14,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:35:14,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:35:15,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:35:15,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:35:15,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:35:15,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:35:15,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:35:15,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:35:15,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:35:15,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:35:15,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:35:15,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:35:15,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:35:15,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:35:15,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:35:16,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:35:24,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:35:25,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:35:25,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:36:24,Principal,main
ensemble.py,BH2A,2025-08-26 23:36:24,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:36:24,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:36:25,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:36:25,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:36:25,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:36:26,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:36:26,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:36:26,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:36:26,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:36:26,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:36:26,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:36:26,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:36:26,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:36:26,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:36:26,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:36:26,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:36:27,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:36:27,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:36:27,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:36:34,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:36:34,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:36:34,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:37:31,Principal,main
ensemble.py,BH2A,2025-08-26 23:37:31,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:37:31,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:37:31,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:37:31,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:37:31,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:37:32,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:37:32,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:37:32,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:37:32,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:37:32,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:37:32,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:37:32,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:37:32,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:37:32,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:37:32,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:37:32,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:37:32,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:37:32,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:37:32,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:37:38,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:37:39,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:37:39,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:38:27,Principal,main
ensemble.py,BH2A,2025-08-26 23:38:27,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:38:27,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:38:27,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:38:27,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:38:27,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:38:28,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:38:28,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:38:28,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:38:28,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:38:28,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:38:28,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:38:28,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:38:28,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:38:28,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:38:28,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:38:28,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:38:28,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:38:28,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:38:28,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:38:29,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:38:29,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:38:29,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:39:19,Principal,main
ensemble.py,BH2A,2025-08-26 23:39:19,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:39:19,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:39:20,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:39:20,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:39:20,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:39:21,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:39:21,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:39:21,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:39:21,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:39:21,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:39:21,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:39:21,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:39:21,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:39:21,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:39:21,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:39:21,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:39:23,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:39:23,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:39:23,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:39:45,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:39:45,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:39:45,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:40:56,Principal,main
ensemble.py,BH2A,2025-08-26 23:40:56,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:40:56,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:40:56,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:40:56,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:40:56,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:40:58,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:40:58,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:40:58,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:40:58,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:40:58,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:40:58,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:40:58,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:40:58,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:40:58,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:40:58,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:40:58,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:40:59,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:40:59,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:40:59,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:41:33,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:41:33,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:41:33,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:42:46,Principal,main
ensemble.py,BH2A,2025-08-26 23:42:46,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:42:46,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:42:47,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:42:47,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:42:47,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:42:48,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:42:48,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:42:48,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:42:48,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:42:48,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:42:48,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:42:48,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:42:48,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:42:48,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:42:48,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:42:48,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:42:50,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:42:50,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:42:50,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:43:11,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:43:11,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:43:11,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:44:23,Principal,main
ensemble.py,BH2A,2025-08-26 23:44:23,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:44:23,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:44:24,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:44:24,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:44:24,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:44:25,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:44:25,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:44:25,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:44:25,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:44:25,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:44:25,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:44:25,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:44:25,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:44:25,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:44:25,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:44:25,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:44:25,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:44:25,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:44:25,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:44:43,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:44:43,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:44:43,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:45:38,Principal,main
ensemble.py,BH2A,2025-08-26 23:45:38,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:45:38,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:45:39,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:45:39,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:45:39,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:45:41,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:45:41,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:45:41,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:45:41,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:45:41,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:45:41,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:45:41,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:45:41,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:45:41,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:45:41,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:45:41,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:45:43,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:45:43,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:45:43,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:46:16,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:46:17,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:46:17,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:47:49,Principal,main
ensemble.py,BH2A,2025-08-26 23:47:49,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:47:49,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:47:50,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:47:50,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:47:50,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:47:51,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:47:51,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:47:51,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:47:51,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:47:51,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:47:52,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:47:52,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:47:52,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:47:52,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:47:52,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:47:52,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:47:53,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:47:53,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:47:53,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:48:27,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:48:27,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:48:28,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:49:53,Principal,main
ensemble.py,BH2A,2025-08-26 23:49:53,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:49:53,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:49:54,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:49:54,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:49:54,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:49:55,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:49:55,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:49:55,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:49:55,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:49:55,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:49:55,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:49:55,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:49:55,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:49:55,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:49:55,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:49:55,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:49:56,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:49:56,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:49:57,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:50:18,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:50:18,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:50:18,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:51:25,Principal,main
ensemble.py,BH2A,2025-08-26 23:51:25,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-26 23:51:25,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-26 23:51:26,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-26 23:51:26,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-26 23:51:26,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-26 23:51:27,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-26 23:51:27,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-26 23:51:27,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-26 23:51:27,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-26 23:51:27,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-26 23:51:27,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-26 23:51:27,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-26 23:51:27,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-26 23:51:27,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-26 23:51:27,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-26 23:51:27,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-26 23:51:28,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-26 23:51:29,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-26 23:51:29,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-26 23:51:50,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-26 23:51:50,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-26 23:51:50,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-26 23:53:01,Principal,main
ensemble.py,BH2A,2025-08-26 23:53:01,Principal,process_merge_delta_exports
ensemble.py,BH2A,2025-08-27 08:54:19,Principal,main
ensemble.py,BH2A,2025-08-27 08:54:19,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 08:54:19,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 08:54:19,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 08:54:19,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 08:54:19,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 08:54:20,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 08:54:20,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 08:54:20,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 08:54:20,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 08:54:20,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 08:54:20,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 08:54:20,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 08:54:20,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 08:54:20,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 08:54:20,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 08:54:20,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 08:54:20,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 08:54:21,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 08:54:21,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 08:54:35,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 08:54:35,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 08:54:35,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 08:54:35,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-27 08:55:34,Principal,main
ensemble.py,BH2A,2025-08-27 08:55:34,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 08:55:34,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 08:55:34,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 08:55:34,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 08:55:34,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 08:55:35,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 08:55:35,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 08:55:35,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 08:55:35,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 08:55:35,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 08:55:35,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 08:55:35,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 08:55:35,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 08:55:35,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 08:55:35,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 08:55:35,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 08:55:35,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 08:55:35,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 08:55:35,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 08:55:42,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 08:55:42,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 08:55:42,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 08:55:42,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-27 08:56:35,Principal,main
ensemble.py,BH2A,2025-08-27 08:56:35,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 08:56:35,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 08:56:36,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 08:56:36,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 08:56:36,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 08:56:37,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 08:56:37,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 08:56:37,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 08:56:37,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 08:56:37,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 08:56:37,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 08:56:37,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 08:56:37,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 08:56:37,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 08:56:37,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 08:56:37,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 08:56:38,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 08:56:38,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 08:56:39,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 08:57:46,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 08:57:47,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 08:57:47,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 08:57:47,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-27 08:59:59,Principal,main
ensemble.py,BH2A,2025-08-27 08:59:59,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 08:59:59,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:00:00,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:00:00,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:00:00,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:00:01,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:00:01,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:00:01,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:00:02,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:00:02,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:00:02,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:00:02,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:00:02,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:00:02,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:00:02,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:00:02,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:00:04,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:00:04,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:00:04,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:01:19,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:01:20,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:01:20,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:01:20,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-27 09:04:01,Principal,main
ensemble.py,BH2A,2025-08-27 09:04:01,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:04:01,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:04:01,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:04:01,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:04:01,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:04:02,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:04:03,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:04:03,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:04:03,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:04:03,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:04:03,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:04:03,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:04:03,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:04:03,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:04:03,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:04:03,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:04:03,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:04:03,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:04:03,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:04:56,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:04:57,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:04:57,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:04:57,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-27 09:05:56,Principal,main
ensemble.py,BH2A,2025-08-27 09:05:56,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:05:56,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:05:57,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:05:57,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:05:57,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:05:58,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:05:59,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:05:59,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:05:59,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:05:59,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:05:59,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:05:59,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:05:59,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:05:59,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:05:59,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:05:59,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:06:00,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:06:00,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:06:00,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:07:15,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:07:16,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:07:16,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:07:16,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-27 09:09:33,Principal,main
ensemble.py,BH2A,2025-08-27 09:09:33,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:09:33,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:09:35,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:09:35,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:09:35,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:09:36,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:09:36,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:09:36,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:09:36,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:09:36,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:09:36,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:09:36,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:09:36,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:09:36,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:09:36,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:09:36,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:09:37,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:09:37,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:09:37,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:11:07,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:11:08,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:11:08,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:11:08,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-27 09:13:47,Principal,main
ensemble.py,BH2A,2025-08-27 09:13:47,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:13:47,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:13:48,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:13:48,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:13:48,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:13:49,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:13:49,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:13:49,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:13:49,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:13:49,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:13:49,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:13:49,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:13:49,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:13:50,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:13:50,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:13:50,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:13:51,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:13:51,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:13:51,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:14:57,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:14:58,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:14:58,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:14:58,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-27 09:17:01,Principal,main
ensemble.py,BH2A,2025-08-27 09:17:01,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:17:01,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:17:02,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:17:02,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:17:02,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:17:03,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:17:03,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:17:03,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:17:03,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:17:04,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:17:04,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:17:04,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:17:04,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:17:04,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:17:04,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:17:04,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:17:05,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:17:05,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:17:05,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:18:14,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:18:14,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:18:14,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:18:14,Principal,apply_tps_transformations
ensemble.py,BH2A,2025-08-27 09:20:44,Principal,main
ensemble.py,BH2A,2025-08-27 09:20:44,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:20:44,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:20:45,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:20:45,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:20:45,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:20:46,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:20:46,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:20:46,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:20:46,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:20:46,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:20:46,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:20:46,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:20:46,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:20:46,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:20:46,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:20:46,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:20:47,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:20:47,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:20:47,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:20:54,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:20:54,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:20:54,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:21:55,Principal,main
ensemble.py,BH2A,2025-08-27 09:21:55,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:21:55,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:21:56,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:21:56,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:21:56,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:21:56,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:21:56,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:21:56,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:21:56,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:21:56,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:21:57,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:21:57,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:21:57,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:21:57,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:21:57,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:21:57,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:21:57,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:21:57,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:21:57,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:22:04,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:22:04,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:22:04,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:23:04,Principal,main
ensemble.py,BH2A,2025-08-27 09:23:04,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:23:04,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:23:05,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:23:05,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:23:05,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:23:06,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:23:06,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:23:06,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:23:06,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:23:06,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:23:06,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:23:06,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:23:06,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:23:06,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:23:06,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:23:06,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:23:07,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:23:07,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:23:07,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:23:16,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:23:16,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:23:16,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:24:19,Principal,main
ensemble.py,BH2A,2025-08-27 09:24:19,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:24:19,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:24:19,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:24:20,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:24:20,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:24:20,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:24:20,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:24:20,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:24:20,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:24:20,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:24:20,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:24:20,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:24:20,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:24:20,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:24:20,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:24:20,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:24:20,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:24:20,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:24:21,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:24:29,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:24:30,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:24:30,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:25:22,Principal,main
ensemble.py,BH2A,2025-08-27 09:25:22,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:25:22,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:25:23,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:25:23,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:25:23,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:25:24,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:25:24,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:25:24,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:25:24,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:25:24,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:25:24,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:25:24,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:25:24,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:25:24,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:25:24,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:25:24,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:25:24,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:25:25,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:25:25,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:25:33,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:25:34,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:25:34,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:26:36,Principal,main
ensemble.py,BH2A,2025-08-27 09:26:36,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:26:36,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:26:37,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:26:37,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:26:37,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:26:37,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:26:37,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:26:37,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:26:37,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:26:37,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:26:37,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:26:37,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:26:38,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:26:38,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:26:38,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:26:38,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:26:38,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:26:38,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:26:38,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:26:47,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:26:47,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:26:47,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:27:50,Principal,main
ensemble.py,BH2A,2025-08-27 09:27:51,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:27:51,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:27:51,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:27:51,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:27:51,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:27:52,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:27:52,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:27:52,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:27:52,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:27:52,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:27:52,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:27:52,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:27:52,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:27:52,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:27:52,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:27:52,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:27:52,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:27:52,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:27:52,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:27:59,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:28:00,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:28:00,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:29:00,Principal,main
ensemble.py,BH2A,2025-08-27 09:29:00,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:29:00,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:29:00,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:29:00,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:29:00,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:29:01,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:29:01,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:29:01,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:29:01,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:29:01,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:29:01,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:29:01,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:29:01,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:29:01,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:29:01,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:29:01,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:29:01,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:29:01,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:29:01,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:29:07,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:29:07,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:29:08,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:30:00,Principal,main
ensemble.py,BH2A,2025-08-27 09:30:00,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:30:00,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:30:00,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:30:00,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:30:00,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:30:00,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:30:00,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:30:00,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:30:01,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:30:01,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:30:01,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:30:01,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:30:01,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:30:01,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:30:01,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:30:01,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:30:01,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:30:01,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:30:01,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:30:01,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:30:02,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:30:02,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:30:54,Principal,main
ensemble.py,BH2A,2025-08-27 09:30:55,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:30:55,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:30:55,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:30:55,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:30:55,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:30:56,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:30:56,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:30:57,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:30:57,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:30:57,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:30:57,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:30:57,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:30:57,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:30:57,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:30:57,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:30:57,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:30:58,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:30:58,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:30:58,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:31:20,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:31:20,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:31:20,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:32:37,Principal,main
ensemble.py,BH2A,2025-08-27 09:32:37,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:32:37,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:32:37,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:32:37,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:32:37,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:32:39,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:32:39,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:32:39,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:32:39,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:32:39,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:32:39,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:32:39,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:32:39,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:32:39,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:32:39,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:32:39,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:32:41,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:32:41,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:32:41,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:33:16,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:33:17,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:33:17,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:34:49,Principal,main
ensemble.py,BH2A,2025-08-27 09:34:49,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:34:49,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:34:50,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:34:50,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:34:50,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:34:51,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:34:51,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:34:51,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:34:51,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:34:51,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:34:51,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:34:51,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:34:51,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:34:51,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:34:51,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:34:52,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:34:53,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:34:53,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:34:53,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:35:15,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:35:15,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:35:15,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:36:29,Principal,main
ensemble.py,BH2A,2025-08-27 09:36:29,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:36:29,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:36:30,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:36:30,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:36:30,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:36:31,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:36:31,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:36:31,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:36:31,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:36:31,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:36:31,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:36:31,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:36:31,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:36:31,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:36:31,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:36:32,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:36:32,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:36:32,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:36:32,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:36:49,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:36:49,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:36:49,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:37:41,Principal,main
ensemble.py,BH2A,2025-08-27 09:37:41,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:37:41,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:37:42,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:37:42,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:37:42,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:37:44,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:37:44,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:37:44,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:37:44,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:37:44,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:37:44,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:37:44,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:37:44,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:37:44,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:37:44,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:37:44,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:37:45,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:37:45,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:37:45,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:38:20,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:38:20,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:38:20,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:39:50,Principal,main
ensemble.py,BH2A,2025-08-27 09:39:50,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:39:50,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:39:51,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:39:51,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:39:51,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:39:53,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:39:53,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:39:53,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:39:53,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:39:53,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:39:53,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:39:53,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:39:53,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:39:53,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:39:53,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:39:54,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:39:56,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:39:56,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:39:56,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:40:30,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:40:31,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:40:31,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:42:01,Principal,main
ensemble.py,BH2A,2025-08-27 09:42:01,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:42:01,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:42:02,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:42:02,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:42:02,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:42:03,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:42:03,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:42:03,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:42:03,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:42:03,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:42:03,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:42:04,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:42:04,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:42:04,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:42:04,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:42:04,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:42:05,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:42:05,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:42:05,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:42:26,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:42:26,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:42:26,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:43:35,Principal,main
ensemble.py,BH2A,2025-08-27 09:43:35,Principal,process_delta_export2
ensemble_report.py,BH2A,2025-08-27 09:43:35,Subchamada,read_single_data_file
cmg_out_helper.py,BH2A,2025-08-27 09:43:36,Subchamada,ler_uep_config
cmg_out_helper.py,BH2A,2025-08-27 09:43:36,Subchamada,obter_conexoes_classificadas_por_data
cmg_out_helper.py,BH2A,2025-08-27 09:43:36,Subchamada,extrair_hierarquia
cmg_out_helper.py,BH2A,2025-08-27 09:43:37,Subchamada,carregar_coordenadas
ensemble_report.py,BH2A,2025-08-27 09:43:37,Subchamada,read_wcoord_file
cmg_out_helper.py,BH2A,2025-08-27 09:43:37,Subchamada,processar_bloco
cmg_out_helper.py,BH2A,2025-08-27 09:43:37,Subchamada,filtrar_conexoes_e_classificar
cmg_out_helper.py,BH2A,2025-08-27 09:43:37,Subchamada,filtrar_conexoes_por_coordenadas
cmg_out_helper.py,BH2A,2025-08-27 09:43:37,Subchamada,flatten_children
cmg_out_helper.py,BH2A,2025-08-27 09:43:37,Subchamada,classify_node_ancestry
cmg_out_helper.py,BH2A,2025-08-27 09:43:37,Subchamada,get_ancestors
cmg_out_helper.py,BH2A,2025-08-27 09:43:37,Subchamada,filtrar_por_uep
cmg_out_helper.py,BH2A,2025-08-27 09:43:38,Subchamada,colapsar_pais_para_uep_ou_field
cmg_out_helper.py,BH2A,2025-08-27 09:43:38,Subchamada,get_uep_or_field
ensemble.py,BH2A,2025-08-27 09:43:39,Principal,transform_df_with_uep_intervals
ensemble.py,BH2A,2025-08-27 09:43:39,Principal,build_intervals_for_well
ensemble.py,BH2A,2025-08-27 09:43:39,Principal,get_uep_and_type_for_well_in_date
ensemble.py,BH2A,2025-08-27 09:44:00,Principal,post_process_delta_export
ensemble.py,BH2A,2025-08-27 09:44:00,Principal,apply_column_rules
ensemble.py,BH2A,2025-08-27 09:44:00,Principal,parse_column_rules
ensemble.py,BH2A,2025-08-27 09:45:19,Principal,main
ensemble.py,BH2A,2025-08-27 09:45:19,Principal,process_merge_delta_exports
processar_PO.py,BH2A,2025-08-27 09:57:00,Principal,main
processar_PO.py,BH2A,2025-08-27 09:57:00,Principal,processar_po
processar_PO.py,BH2A,2025-08-27 09:57:00,Principal,ler_arquivo_excel
processar_PO.py,BH2A,2025-08-27 09:57:08,Principal,renomear_colunas
processar_PO.py,BH2A,2025-08-27 09:57:08,Principal,substituir_nan_por_zero
processar_PO.py,BH2A,2025-08-27 09:57:08,Principal,converter_nzp_inteiro
processar_PO.py,BH2A,2025-08-27 09:58:21,Principal,salvar_para_excel
limitesPN.py,BH2A,2025-08-27 10:04:29,Subchamada,standalone_execution
ajustadorPN.rev2.py,BH2A,2025-08-27 10:04:29,Principal,main
ajustadorPN.rev2.py,BH2A,2025-08-27 10:04:29,Principal,ler_excel
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:49,Principal,ler_ordem_projetos
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:49,Principal,pre_ajuste
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:49,Principal,analisar_projeto_cumulativo
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:49,Principal,analisar_plataforma
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:49,Principal,check_limits_for_platform_qg
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:49,Principal,create_campo_zp_nzp_mapping
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:49,Principal,get_limit_for_date
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:49,Principal,verificar_ajustes_negativos
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:49,Principal,check_limits_for_platform_qw
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:49,Principal,check_limits_for_platform_ql
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:50,Principal,check_limits_for_platform_qo
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:50,Principal,check_limits_for_platform_qwi
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:50,Principal,flatten_and_unique
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:50,Principal,apply_priority_sorting
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:50,Principal,get_highest_priority
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:50,Principal,calcular_rateio_campo_zp
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:50,Principal,get_unique_values
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:50,Principal,criar_linhas_ajuste_rateadas
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:50,Principal,criar_linha_ajuste_base
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:50,Principal,extract_value
ajustadorPN.rev2.py,BH2A,2025-08-27 10:05:52,Principal,str_to_date
ajustadorPN.rev2.py,BH2A,2025-08-27 10:21:40,Principal,planilhaGIR
