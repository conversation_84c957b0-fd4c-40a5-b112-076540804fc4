import pandas as pd
from Auxiliares.modules.logging_module import log_execution

# Carregar o arquivo Excel
df = pd.read_excel(r"L:\res\campos\jubarte\er\er03\PN\2630\C1\2505071558_Probabilisticas\PN2630C1_novoGL.xlsx")

# Definir as colunas de identificação e valores
identifiers = [
    "Versao", "Cenario", "Campo", "ZP", "NZP", "UEP", "Projeto", "IUPI", "Well", "Date"
]
value_columns = [
    "Qo Pot(m3/day)",
    "Qw Pot(m3/day)",
    "Qwi Pot(m3/day)",
    "Qg Pot(mil m3/day)",
    "Qgi Pot(mil m3/day)",
    "Qgl Pot(mil m3/day)",
    "Qgco2 Pot(mil m3/day)",
    "Qgco2i Pot(mil m3/day)",
    "Qghc Pot(mil m3/day)",
    "Qghci Pot(mil m3/day)"
]

# Dicionário de dispersão fornecido

dispersao = {
    "default": {
        "default": 1.2,
        "Qo Pot(m3/day)": 1.2,
        "Qw Pot(m3/day)": 1.1,
        "Qwi Pot(m3/day)": 1.2,
        "Qg Pot(mil m3/day)": 1.2,
        "Qgi Pot(mil m3/day)": 1.2,
        "Qgl Pot(mil m3/day)": 1,
        "Qgco2 Pot(mil m3/day)": 1.2,
        "Qgco2i Pot(mil m3/day)": 1.2,
        "Qghc Pot(mil m3/day)": 1.2,
        "Qghci Pot(mil m3/day)": 1.2,
    },
    "ZP": {
        "MCB/COQ-ESS103A": {
            "Projeto": {
                "IPB": {
                    "Qo Pot(m3/day)": 1.2,
                    "Qw Pot(m3/day)": 1.1,
                    "Qwi Pot(m3/day)": 1.2,
                    "Qg Pot(mil m3/day)": 1.2,
                    "Qgi Pot(mil m3/day)": 1.2,
                    "Qgl Pot(mil m3/day)": 1,
                    "Qgco2 Pot(mil m3/day)": 1.2,
                    "Qgco2i Pot(mil m3/day)": 1.2,
                    "Qghc Pot(mil m3/day)": 1.2,
                    "Qghci Pot(mil m3/day)": 1.2,
                },
            }
        },
    }
}

# Extrair regras de dispersão
rules = []

@log_execution
def extract_rules(node, current_conditions, depth):
    multipliers = {}
    if isinstance(node, dict):
        per_col = {}
        scalar = None
        for key in node:
            if key == 'default':
                continue
            if key in value_columns:
                per_col[key] = node[key]
            elif key in identifiers:
                for value in node[key]:
                    new_conditions = current_conditions.copy()
                    new_conditions[key] = value
                    extract_rules(node[key][value], new_conditions, depth + 1)
            else:
                scalar = node[key]
        if scalar is not None:
            per_col = {col: scalar for col in value_columns}
        multipliers.update(per_col)
    else:
        scalar = node
        multipliers = {col: scalar for col in value_columns}

    if multipliers:
        rules.append({
            'conditions': current_conditions.copy(),
            'multipliers': multipliers,
            'depth': depth
        })

# Extrair regras a partir das chaves superiores de dispersao (excluindo 'default')
for key in dispersao:
    if key == 'default':
        continue
    if key not in identifiers:
        continue
    for value in dispersao[key]:
        extract_rules(dispersao[key][value], {key: value}, 1)

# Ordenar as regras por profundidade descendente
rules.sort(key=lambda x: -x['depth'])

# Obter multiplicadores padrão
default_multipliers = dispersao['default']
default_per_col = {col: default_multipliers.get(col, default_multipliers['default']) for col in value_columns}

# Aplicar multiplicadores a cada linha
for index, row in df.iterrows():
    row_identifiers = {col: row[col] for col in identifiers}
    multipliers = default_per_col.copy()

    for rule in rules:
        match = True
        for cond_col, cond_val in rule['conditions'].items():
            if row_identifiers.get(cond_col) != cond_val:
                match = False
                break
        if match:
            for col in value_columns:
                if col in rule['multipliers']:
                    multipliers[col] = rule['multipliers'][col]
            break  # A regra mais profunda foi aplicada

    for col in value_columns:
        df.at[index, col] *= multipliers[col]

# Salvar o DataFrame modificado
df.to_excel(r"L:\res\campos\jubarte\er\er03\PN\2630\C1\2505071558_Probabilisticas\PN2630C1_novoGL.output.xlsx", index=False)