import os
import sys
import pandas as pd
from glob import glob
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from Auxiliares.modules.logging_module import log_execution

@log_execution
def find_first_wcoord_file(directory):
    wcoord_pattern = os.path.join(directory, "*.wcoord")
    wcoord_files = glob(wcoord_pattern)
    return wcoord_files[0] if wcoord_files else None

@log_execution
def read_wcoord_files(run_folder):
    attempt_pattern = os.path.join(run_folder, "ATTEMPT_*")
    attempt_dirs = glob(attempt_pattern)
    total_attempts = len(attempt_dirs)
    print(f"Encontrados {total_attempts} diretórios ATTEMPT_XXXX")
    dfs = []
    for i, attempt_dir in enumerate(attempt_dirs, 1):
        wcoord_path = find_first_wcoord_file(attempt_dir)
        if wcoord_path and os.path.exists(wcoord_path):
            df = pd.read_csv(wcoord_path)
            dfs.append(df)
            print(f"Processado {i}/{total_attempts}: {os.path.basename(attempt_dir)}")
        else:
            print(f"Arquivo .wcoord não encontrado em {i}/{total_attempts}: {os.path.basename(attempt_dir)}")
    if not dfs:
        print("Nenhum arquivo .wcoord foi encontrado.")
        return None
    combined_df = pd.concat(dfs, ignore_index=True)
    unique_df = combined_df.drop_duplicates(subset="Poço")
    return unique_df

@log_execution
def add_well_scatter(fig, well_coords, result, row, col):
    # Adiciona todos os poços em cinza
    fig.add_trace(
        go.Scatter(
            x=well_coords['x'],
            y=well_coords['y'],
            mode='markers',
            marker=dict(color='darkgrey', size=8),
            text=well_coords['Poço'],
            name='Todos os Poços',
            showlegend=False
        ),
        row=row, col=col
    )

    # Adiciona os poços do escopo individualmente
    for poco in result['Escopo']:
        well = well_coords[well_coords['Poço'] == poco]
        if not well.empty:
            fig.add_trace(
                go.Scatter(
                    x=[well['x'].values[0]],
                    y=[well['y'].values[0]],
                    mode='markers',
                    marker=dict(color='green', size=8),
                    text=poco,
                    name=poco,
                    legendgroup=poco,
                    showlegend=True
                ),
                row=row, col=col
            )

@log_execution
def add_bars(fig, df, column, row, color):
    for i, (index, row_data) in enumerate(df.iterrows()):
        fig.add_trace(go.Bar(
            x=[row_data['Escopo']],
            y=[row_data[column]],
            name=row_data['Escopo'],
            legendgroup=row_data['Escopo'],
            showlegend=False,  # Não mostra na legenda, pois já está no scatter
            marker_color=color),
            row=row, col=1
        )

@log_execution
def generate_links_html(result):
    links_html = "<div style='margin-top: 20px; padding: 10px; background-color: #f0f0f0;'>"
    links_html += "<h3>Links para Relatórios:</h3>"
    links_html += "<ul style='list-style-type: none; padding: 0;'>"
    for _, row in result.iterrows():
        # Substitui "L:\" por "\\dfs.petrobras.biz\cientifico\"
        report_path = row['Report'].replace("L:\\", "\\\\dfs.petrobras.biz\\cientifico\\")
        links_html += f"<li style='margin-bottom: 5px;'><a href='{report_path}' target='_blank'>{row['Escopo']}</a></li>"
    links_html += "</ul></div>"
    return links_html

@log_execution
def main(run_folder):
    well_coords = read_wcoord_files(run_folder)
    if well_coords is None:
        print("Não foi possível criar o DataFrame de coordenadas dos poços.")
        return

    attempt_pattern = os.path.join(run_folder, "ATTEMPT_*")
    attempt_dirs = glob(attempt_pattern)
    total_attempts = len(attempt_dirs)
    print(f"Encontrados {total_attempts} diretórios ATTEMPT_XXXX")
    dfs = []
    for i, attempt_dir in enumerate(attempt_dirs, 1):
        csv_path = os.path.join(attempt_dir, "ensemble_report.csv")
        if os.path.exists(csv_path):
            df = pd.read_csv(csv_path, index_col=0)
            df['attempt'] = os.path.basename(attempt_dir)
            dfs.append(df)
            print(f"Processado {i}/{total_attempts}: {os.path.basename(attempt_dir)}")
        else:
            print(f"Arquivo não encontrado em {i}/{total_attempts}: {os.path.basename(attempt_dir)}")

    if not dfs:
        print("Nenhum arquivo ensemble_report.csv encontrado.")
        return

    print(f"\nCombinando {len(dfs)} arquivos CSV...")
    result = pd.concat(dfs, axis=0)
    result = result.reset_index().rename(columns={'index': 'Escopo'})
    result_sorted_dnpe_star = result.sort_values('DNpe* P50', ascending=False)
    result_sorted_dnpe = result.sort_values('DNpe P50', ascending=False)
    result_sorted_dnp = result.sort_values('DNp P50', ascending=False)
    result_sorted_dgp = result.sort_values('DGp P50', ascending=False)

    csv_output_path = os.path.join(run_folder, "ranking.csv")
    result.to_csv(csv_output_path, index=False)
    print(f"Ranking salvo em: {csv_output_path}")

    fig = make_subplots(rows=6, cols=1,
                        specs=[[{"type": "table"}],
                               [{"type": "xy"}],
                               [{"type": "bar"}],
                               [{"type": "bar"}],
                               [{"type": "bar"}],
                               [{"type": "bar"}]],
                        vertical_spacing=0.05,
                        subplot_titles=("Ranking Table",
                                        "Mapa de Poços",
                                        f"DNpe* P50 ({len(result)} Escopos)",
                                        f"DNp P50 + DGp P50 ({len(result)} Escopos)",
                                        f"DNp P50 ({len(result)} Escopos)",
                                        f"DGp P50 ({len(result)} Escopos)"))

    fig.add_trace(
        go.Table(
            header=dict(values=list(result.columns),
                        fill_color='paleturquoise',
                        align='left'),
            cells=dict(values=[result[col] for col in result.columns],
                       fill_color='lavender',
                       align='left')
        ),
        row=1, col=1
    )

    add_well_scatter(fig, well_coords, result, 2, 1)

    add_bars(fig, result_sorted_dnpe_star, 'DNpe* P50', 3, 'blue')

    for i, (index, row) in enumerate(result_sorted_dnpe.iterrows()):
        fig.add_trace(go.Bar(
            x=[row['Escopo']],
            y=[row['DNp P50']],
            name=row['Escopo'],
            legendgroup=row['Escopo'],
            showlegend=False,
            marker_color='green'),
            row=4, col=1
        )
        fig.add_trace(go.Bar(
            x=[row['Escopo']],
            y=[row['DGp P50']],
            name=row['Escopo'],
            legendgroup=row['Escopo'],
            showlegend=False,
            marker_color='red'),
            row=4, col=1
        )

    add_bars(fig, result_sorted_dnp, 'DNp P50', 5, 'green')
    add_bars(fig, result_sorted_dgp, 'DGp P50', 6, 'red')

    # Configurar o layout do gráfico de dispersão para ter a mesma escala em x e y
    fig.update_xaxes(title_text="X", row=2, col=1, scaleanchor="y", scaleratio=1)
    fig.update_yaxes(title_text="Y", row=2, col=1)

    # Calcular o centro do mapa
    x_center = (well_coords['x'].max() + well_coords['x'].min()) / 2
    y_center = (well_coords['y'].max() + well_coords['y'].min()) / 2

    # Calcular a extensão do mapa
    x_range = well_coords['x'].max() - well_coords['x'].min()
    y_range = well_coords['y'].max() - well_coords['y'].min()
    max_range = max(x_range, y_range)

    # Definir os limites do mapa
    fig.update_xaxes(range=[x_center - max_range/2, x_center + max_range/2], row=2, col=1)
    fig.update_yaxes(range=[y_center - max_range/2, y_center + max_range/2], row=2, col=1)

    fig.update_layout(
        height=3000,
        showlegend=True,
        legend=dict(
            orientation="v",
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.02,
            itemsizing='constant'
        ),
        barmode='stack',
        margin=dict(t=100, b=50, l=50, r=50),
    )

    # Garantir que o gráfico de dispersão seja quadrado
    fig.update_layout(yaxis2=dict(scaleanchor="x2", scaleratio=1))

    fig.update_xaxes(categoryorder='array', categoryarray=result_sorted_dnpe_star['Escopo'].tolist(), row=3, col=1)
    fig.update_xaxes(categoryorder='array', categoryarray=result_sorted_dnpe['Escopo'].tolist(), row=4, col=1)
    fig.update_xaxes(categoryorder='array', categoryarray=result_sorted_dnp['Escopo'].tolist(), row=5, col=1)
    fig.update_xaxes(categoryorder='array', categoryarray=result_sorted_dgp['Escopo'].tolist(), row=6, col=1)

    html_output_path = os.path.join(run_folder, "ranking.html")
    plot_html = fig.to_html(full_html=False, include_plotlyjs='cdn')
    links_html = generate_links_html(result)

    with open(html_output_path, 'w', encoding='utf-8') as f:
        f.write(f"""
        <html>
        <head>
            <title>Ranking de Poços</title>
        </head>
        <body>
            {plot_html}
            {links_html}
        </body>
        </html>
        """)

    print(f"Arquivo HTML interativo com gráficos e links salvo em: {html_output_path}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Uso: python ranking.py <caminho_para_runFolder>")
        sys.exit(1)

    run_folder = sys.argv[1]
    main(run_folder)
