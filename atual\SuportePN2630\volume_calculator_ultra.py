#!/usr/bin/env python3
"""
Sistema ULTRA-OTIMIZADO de Cálculo de Volumes para Dados de Petróleo
Versão Python 3.0 - Performance Avançada com Numba, Polars, Multiprocessing

Melhorias de Performance:
- Numba JIT compilation para funções críticas
- Polars para operações DataFrame ultra-rápidas
- Multiprocessing para paralelização
- Vectorização avançada com NumPy
- Memory mapping para arquivos grandes
- Caching inteligente

Autor: Sistema de Análise de Produção Otimizado
Data: 2025
"""

import pandas as pd
import numpy as np
import polars as pl
import os
import sys
from datetime import datetime
import warnings
from pathlib import Path
import argparse
from typing import Dict, List, Tuple, Optional, Union
import logging
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import multiprocessing as mp
from functools import lru_cache, partial
import gc
import psutil
import time

# Imports para otimização
try:
    from numba import jit, njit, prange
    NUMBA_AVAILABLE = True
except ImportError:
    print("Numba não disponível. Instalando seria recomendado para máxima performance.")
    NUMBA_AVAILABLE = False
    def jit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    def njit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    prange = range

try:
    import cupy as cp
    CUPY_AVAILABLE = True
    print("CuPy detectado - Aceleração GPU disponível!")
except ImportError:
    CUPY_AVAILABLE = False
    cp = np

# Configuração otimizada de logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('volume_calc.log')
    ]
)
logger = logging.getLogger(__name__)

# Suprimir warnings
warnings.filterwarnings('ignore')

class PerformanceMonitor:
    """Monitor de performance para tracking de otimizações"""
    
    def __init__(self):
        self.start_time = None
        self.checkpoints = {}
        
    def start(self):
        self.start_time = time.perf_counter()
        
    def checkpoint(self, name: str):
        if self.start_time:
            elapsed = time.perf_counter() - self.start_time
            self.checkpoints[name] = elapsed
            logger.info(f"Checkpoint '{name}': {elapsed:.3f}s")
            
    def get_memory_usage(self):
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024  # MB

# Funções Numba otimizadas para operações críticas
@njit(parallel=True, cache=True)
def calculate_monthly_volumes_numba(vazoes: np.ndarray, dias_mes: np.ndarray) -> np.ndarray:
    """Calcula volumes mensais usando Numba JIT para máxima velocidade"""
    n = len(vazoes)
    volumes = np.empty(n, dtype=np.float64)
    
    for i in prange(n):
        volumes[i] = vazoes[i] * dias_mes[i]
    
    return volumes

@njit(parallel=True, cache=True)
def calculate_cumulative_by_group_numba(volumes: np.ndarray, group_ids: np.ndarray) -> np.ndarray:
    """Calcula acumulados por grupo usando Numba"""
    n = len(volumes)
    cumulative = np.empty(n, dtype=np.float64)
    
    # Primeiro, identifica grupos únicos
    unique_groups = np.unique(group_ids)
    
    for group in unique_groups:
        group_mask = group_ids == group
        group_indices = np.where(group_mask)[0]
        
        cumsum = 0.0
        for idx in group_indices:
            cumsum += volumes[idx]
            cumulative[idx] = cumsum
    
    return cumulative

@njit(cache=True)
def fast_string_to_float(value: str) -> float:
    """Conversão rápida string para float com Numba"""
    try:
        # Remove espaços e substitui vírgula por ponto
        cleaned = value.replace(' ', '').replace(',', '.')
        return float(cleaned)
    except:
        return 0.0

class OptimizedVolumeCalculator:
    """
    Classe ULTRA-OTIMIZADA para cálculo de volumes de produção de petróleo
    """
    
    def __init__(self, use_gpu: bool = None, max_workers: int = None):
        """
        Inicializa o calculador com configurações de performance
        
        Args:
            use_gpu: Usar GPU se disponível (auto-detecta se None)
            max_workers: Número máximo de workers para paralelização
        """
        self.use_gpu = use_gpu if use_gpu is not None else CUPY_AVAILABLE
        self.max_workers = max_workers or min(mp.cpu_count(), 8)
        
        self.vazoes_config = {
            'Qo': {'coluna': '1 - Qo Pot', 'nome': 'Óleo'},
            'Qg': {'coluna': '2 - Qg Pot', 'nome': 'Gás'},
            'Qw': {'coluna': '3 - Qw Pot', 'nome': 'Água'},
            'Qwi': {'coluna': '4 - Qwi Pot', 'nome': 'Água Injetada'},
            'Qgi': {'coluna': '5 - Qgi Pot', 'nome': 'Gás Injetado'},
            'GL': {'coluna': '6 - GL Pot', 'nome': 'Gas Lift'},
            'Auto': {'coluna': '7 - Auto inj Pot', 'nome': 'Auto Injeção'}
        }
        
        self.df_polars = None
        self.df_pandas = None
        self.perf_monitor = PerformanceMonitor()
        
        logger.info(f"Calculador inicializado - GPU: {self.use_gpu}, Workers: {self.max_workers}")
    
    def carregar_excel_otimizado(self, caminho_arquivo: str, nome_aba: Optional[str] = None) -> pl.DataFrame:
        """
        Carregamento ultra-otimizado usando Polars + memory mapping
        """
        self.perf_monitor.start()
        
        try:
            logger.info(f"Carregando arquivo com Polars: {caminho_arquivo}")
            
            if not os.path.exists(caminho_arquivo):
                raise FileNotFoundError(f"Arquivo não encontrado: {caminho_arquivo}")
            
            # Estratégia 1: Tenta Polars primeiro (mais rápido)
            try:
                # Converte para CSV temporário se for Excel (Polars lê CSV mais rápido)
                if caminho_arquivo.endswith(('.xlsx', '.xls')):
                    temp_csv = self._excel_to_csv_temp(caminho_arquivo, nome_aba)
                    df_pl = pl.read_csv(
                        temp_csv,
                        try_parse_dates=True,
                        null_values=['', 'NA', 'NULL', 'null'],
                        ignore_errors=True
                    )
                    os.remove(temp_csv)  # Limpa arquivo temporário
                else:
                    df_pl = pl.read_csv(caminho_arquivo, try_parse_dates=True)
                    
                self.perf_monitor.checkpoint("Carregamento Polars")
                
            except Exception as e:
                logger.warning(f"Polars falhou, usando Pandas: {e}")
                # Fallback para Pandas otimizado
                df_pandas = self._load_with_pandas_optimized(caminho_arquivo, nome_aba)
                df_pl = pl.from_pandas(df_pandas)
                self.perf_monitor.checkpoint("Carregamento Pandas+Polars")
            
            logger.info(f"Dados carregados: {len(df_pl)} registros, {len(df_pl.columns)} colunas")
            
            # Limpeza otimizada
            df_pl = self._preparar_dados_polars(df_pl)
            self.perf_monitor.checkpoint("Preparação dados")
            
            self.df_polars = df_pl
            return df_pl
            
        except Exception as e:
            logger.error(f"Erro ao carregar arquivo: {str(e)}")
            raise
    
    def _excel_to_csv_temp(self, excel_path: str, sheet_name: Optional[str] = None) -> str:
        """Converte Excel para CSV temporário para leitura mais rápida"""
        temp_csv = f"temp_data_{os.getpid()}.csv"
        
        # Leitura otimizada com chunks se arquivo for muito grande
        file_size = os.path.getsize(excel_path) / (1024 * 1024)  # MB
        
        if file_size > 100:  # Arquivo grande
            df_chunks = pd.read_excel(
                excel_path, 
                sheet_name=sheet_name,
                chunksize=10000,
                engine='openpyxl'
            )
            
            first_chunk = True
            for chunk in df_chunks:
                chunk.to_csv(temp_csv, mode='a', header=first_chunk, index=False)
                first_chunk = False
        else:
            df = pd.read_excel(excel_path, sheet_name=sheet_name, engine='openpyxl')
            df.to_csv(temp_csv, index=False)
            
        return temp_csv
    
    def _load_with_pandas_optimized(self, caminho_arquivo: str, nome_aba: Optional[str] = None) -> pd.DataFrame:
        """Carregamento Pandas otimizado com configurações de performance"""
        read_kwargs = {
            'engine': 'openpyxl',
            'na_values': ['', 'NA', 'NULL', 'null', '#N/A'],
            'keep_default_na': True,
        }
        
        if nome_aba:
            read_kwargs['sheet_name'] = nome_aba
            
        return pd.read_excel(caminho_arquivo, **read_kwargs)
    
    def _preparar_dados_polars(self, df: pl.DataFrame) -> pl.DataFrame:
        """Preparação ultra-rápida usando Polars expressions"""
        logger.info("Preparando dados com Polars...")
        
        # Pipeline de limpeza otimizada
        df_clean = df.with_columns([
            # Converte data
            pl.col("Data").str.strptime(pl.Date, format=None, strict=False).alias("Data"),
            
            # Limpa e converte colunas de vazão em paralelo
            *[
                pl.col(vazao_info['coluna'])
                .str.replace_all(" ", "")
                .str.replace_all(",", ".")
                .cast(pl.Float64, strict=False)
                .fill_null(0.0)
                .alias(vazao_info['coluna'])
                for vazao_key, vazao_info in self.vazoes_config.items()
                if vazao_info['coluna'] in df.columns
            ]
        ]).filter(
            pl.col("Data").is_not_null()
        ).sort(["Poço", "Data"])
        
        logger.info(f"Dados limpos: {len(df_clean)} registros válidos")
        return df_clean
    
    def calcular_volumes_ultra_otimizado(self) -> pl.DataFrame:
        """
        Cálculo ULTRA-OTIMIZADO usando todas as técnicas de performance
        """
        if self.df_polars is None:
            raise ValueError("Dados não carregados. Use carregar_excel_otimizado() primeiro.")
        
        logger.info("Iniciando cálculo ultra-otimizado...")
        self.perf_monitor.checkpoint("Início cálculo")
        
        # Estratégia de paralelização baseada no tamanho dos dados
        num_records = len(self.df_polars)
        
        if num_records > 100000:  # Dados grandes - usar multiprocessing
            resultado = self._calcular_volumes_multiprocessing()
        elif num_records > 10000:  # Dados médios - usar threading + numba
            resultado = self._calcular_volumes_numba_optimized()
        else:  # Dados pequenos - usar Polars puro
            resultado = self._calcular_volumes_polars_pure()
        
        self.perf_monitor.checkpoint("Cálculo concluído")
        logger.info(f"Cálculo concluído. Memória: {self.perf_monitor.get_memory_usage():.1f}MB")
        
        return resultado
    
    def _calcular_volumes_polars_pure(self) -> pl.DataFrame:
        """Cálculo usando Polars puro - mais rápido para dados pequenos/médios"""
        logger.info("Usando Polars puro...")
        
        # Adiciona dias do mês
        df_calc = self.df_polars.with_columns([
            pl.col("Data").dt.days_in_month().alias("dias_mes")
        ])
        
        # Calcula volumes e acumulados em uma única passada
        volume_expressions = []
        acum_expressions = []
        
        for vazao_key, vazao_info in self.vazoes_config.items():
            col_vazao = vazao_info['coluna']
            
            if col_vazao in df_calc.columns:
                # Volume mensal
                vol_expr = (pl.col(col_vazao) * pl.col("dias_mes")).alias(f'Vol_{vazao_key}')
                volume_expressions.append(vol_expr)
                
                # Volume acumulado
                acum_expr = (pl.col(col_vazao) * pl.col("dias_mes")).cumsum().over("Poço").alias(f'Acum_{vazao_key}')
                acum_expressions.append(acum_expr)
        
        # Aplica todas as expressões de uma vez
        resultado = df_calc.with_columns(volume_expressions + acum_expressions).drop("dias_mes")
        
        return resultado
    
    def _calcular_volumes_numba_optimized(self) -> pl.DataFrame:
        """Cálculo usando Numba para operações críticas"""
        if not NUMBA_AVAILABLE:
            return self._calcular_volumes_polars_pure()
            
        logger.info("Usando Numba JIT compilation...")
        
        # Converte para numpy para Numba
        df_pandas = self.df_polars.to_pandas()
        
        # Calcula dias do mês
        dias_mes = df_pandas['Data'].dt.days_in_month.values
        
        # Cria mapeamento de poços para IDs numéricos
        poco_map = {poco: i for i, poco in enumerate(df_pandas['Poço'].unique())}
        poco_ids = df_pandas['Poço'].map(poco_map).values
        
        # Processa cada vazão com Numba
        for vazao_key, vazao_info in self.vazoes_config.items():
            col_vazao = vazao_info['coluna']
            
            if col_vazao in df_pandas.columns:
                vazoes = df_pandas[col_vazao].values.astype(np.float64)
                
                # Cálculo otimizado com Numba
                volumes = calculate_monthly_volumes_numba(vazoes, dias_mes)
                acumulados = calculate_cumulative_by_group_numba(volumes, poco_ids)
                
                df_pandas[f'Vol_{vazao_key}'] = volumes
                df_pandas[f'Acum_{vazao_key}'] = acumulados
        
        return pl.from_pandas(df_pandas)
    
    def _calcular_volumes_multiprocessing(self) -> pl.DataFrame:
        """Cálculo usando multiprocessing para datasets grandes"""
        logger.info(f"Usando multiprocessing com {self.max_workers} workers...")
        
        # Divide dados por poço para paralelização
        df_pandas = self.df_polars.to_pandas()
        pocos_unicos = df_pandas['Poço'].unique()
        
        # Divide poços em chunks para workers
        chunk_size = max(1, len(pocos_unicos) // self.max_workers)
        poco_chunks = [pocos_unicos[i:i + chunk_size] for i in range(0, len(pocos_unicos), chunk_size)]
        
        # Função para processar chunk
        def processar_chunk(pocos_chunk):
            df_chunk = df_pandas[df_pandas['Poço'].isin(pocos_chunk)].copy()
            calc_temp = OptimizedVolumeCalculator(use_gpu=False, max_workers=1)
            calc_temp.df_polars = pl.from_pandas(df_chunk)
            resultado_chunk = calc_temp._calcular_volumes_polars_pure()
            return resultado_chunk.to_pandas()
        
        # Executa em paralelo
        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            resultados = list(executor.map(processar_chunk, poco_chunks))
        
        # Combina resultados
        df_final = pd.concat(resultados, ignore_index=True)
        df_final = df_final.sort_values(['Poço', 'Data']).reset_index(drop=True)
        
        return pl.from_pandas(df_final)
    
    @lru_cache(maxsize=128)
    def _get_cached_days_in_month(self, year: int, month: int) -> int:
        """Cache para dias do mês - evita recálculos"""
        from calendar import monthrange
        return monthrange(year, month)[1]
    
    def gerar_relatorio_performance(self) -> Dict:
        """Gera relatório detalhado de performance"""
        relatorio = {
            'checkpoints': self.perf_monitor.checkpoints,
            'memoria_mb': self.perf_monitor.get_memory_usage(),
            'configuracao': {
                'gpu_disponivel': CUPY_AVAILABLE,
                'gpu_usado': self.use_gpu,
                'numba_disponivel': NUMBA_AVAILABLE,
                'max_workers': self.max_workers,
                'cpu_count': mp.cpu_count()
            }
        }
        
        if self.df_polars is not None:
            relatorio['dados'] = {
                'registros': len(self.df_polars),
                'colunas': len(self.df_polars.columns),
                'pocos_unicos': self.df_polars['Poço'].n_unique(),
                'memoria_estimada_mb': len(self.df_polars) * len(self.df_polars.columns) * 8 / 1024 / 1024
            }
        
        return relatorio
    
    def salvar_resultado_otimizado(self, caminho_saida: str, formato: str = 'excel'):
        """
        Salvamento otimizado com múltiplos formatos
        
        Args:
            caminho_saida: Caminho base do arquivo
            formato: 'excel', 'parquet', 'csv' ou 'all'
        """
        if self.df_polars is None:
            raise ValueError("Execute calcular_volumes_ultra_otimizado() primeiro.")
        
        logger.info(f"Salvando em formato: {formato}")
        self.perf_monitor.checkpoint("Início salvamento")
        
        if formato in ['excel', 'all']:
            # Excel otimizado
            df_pandas = self.df_polars.to_pandas()
            caminho_excel = caminho_saida.replace('.xlsx', '') + '.xlsx'
            
            with pd.ExcelWriter(caminho_excel, engine='openpyxl', 
                              options={'remove_timezone': True}) as writer:
                df_pandas.to_excel(writer, sheet_name='Dados_Completos', index=False)
                
                # Resumos otimizados
                self._salvar_resumos_excel(writer)
            
            self.perf_monitor.checkpoint("Salvamento Excel")
        
        if formato in ['parquet', 'all']:
            # Parquet - formato mais eficiente
            caminho_parquet = caminho_saida.replace('.xlsx', '') + '.parquet'
            self.df_polars.write_parquet(caminho_parquet, compression='snappy')
            self.perf_monitor.checkpoint("Salvamento Parquet")
        
        if formato in ['csv', 'all']:
            # CSV otimizado
            caminho_csv = caminho_saida.replace('.xlsx', '') + '.csv'
            self.df_polars.write_csv(caminho_csv)
            self.perf_monitor.checkpoint("Salvamento CSV")
        
        logger.info("Salvamento concluído!")
    
    def _salvar_resumos_excel(self, writer):
        """Salva resumos otimizados no Excel"""
        # Resumo por poço usando Polars
        resumo_poco = self.df_polars.group_by("Poço").agg([
            pl.col("Data").min().alias("Data_Inicio"),
            pl.col("Data").max().alias("Data_Fim"),
            pl.col("Data").count().alias("Registros"),
            *[pl.col(f'Vol_{key}').sum().alias(f'Vol_Total_{key}') 
              for key in self.vazoes_config.keys() 
              if f'Vol_{key}' in self.df_polars.columns]
        ]).to_pandas()
        
        resumo_poco.to_excel(writer, sheet_name='Resumo_por_Poco', index=False)
        
        # Resumo mensal usando Polars
        resumo_mensal = self.df_polars.with_columns([
            pl.col("Data").dt.strftime("%Y-%m").alias("Ano_Mes")
        ]).group_by("Ano_Mes").agg([
            pl.col("Poço").n_unique().alias("Pocos_Ativos"),
            *[pl.col(f'Vol_{key}').sum().alias(f'Vol_Total_{key}') 
              for key in self.vazoes_config.keys() 
              if f'Vol_{key}' in self.df_polars.columns]
        ]).to_pandas()
        
        resumo_mensal.to_excel(writer, sheet_name='Resumo_Mensal', index=False)

def benchmark_performance():
    """Função para benchmark de diferentes configurações"""
    print("=== BENCHMARK DE PERFORMANCE ===")
    
    # Gera dados sintéticos para teste
    n_records = 50000
    n_wells = 100
    
    print(f"Gerando {n_records} registros sintéticos para benchmark...")
    
    # Dados sintéticos
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', periods=n_records//n_wells, freq='D')
    wells = [f'POCO_{i:03d}' for i in range(n_wells)]
    
    data = []
    for well in wells:
        for date in dates:
            data.append({
                'Poço': well,
                'Data': date,
                'Campo': f'Campo_{np.random.randint(1, 6)}',
                '1 - Qo Pot': np.random.uniform(0, 1000),
                '2 - Qg Pot': np.random.uniform(0, 5000),
                '3 - Qw Pot': np.random.uniform(0, 2000),
            })
    
    df_test = pd.DataFrame(data)
    temp_file = 'benchmark_data.xlsx'
    df_test.to_excel(temp_file, index=False)
    
    # Testa diferentes configurações
    configs = [
        {'name': 'Padrão', 'use_gpu': False, 'max_workers': 1},
        {'name': 'Multi-core', 'use_gpu': False, 'max_workers': mp.cpu_count()},
        {'name': 'GPU (se disponível)', 'use_gpu': True, 'max_workers': mp.cpu_count()},
    ]
    
    results = {}
    
    for config in configs:
        print(f"\nTestando configuração: {config['name']}")
        
        calc = OptimizedVolumeCalculator(
            use_gpu=config['use_gpu'], 
            max_workers=config['max_workers']
        )
        
        start_time = time.perf_counter()
        
        try:
            calc.carregar_excel_otimizado(temp_file)
            calc.calcular_volumes_ultra_otimizado()
            
            elapsed = time.perf_counter() - start_time
            results[config['name']] = elapsed
            
            print(f"Tempo: {elapsed:.2f}s")
            
        except Exception as e:
            print(f"Erro: {e}")
            results[config['name']] = float('inf')
    
    # Cleanup
    os.remove(temp_file)
    
    # Mostra resultados
    print("\n=== RESULTADOS DO BENCHMARK ===")
    best_time = min(results.values())
    
    for name, time_taken in results.items():
        if time_taken != float('inf'):
            speedup = best_time / time_taken
            print(f"{name}: {time_taken:.2f}s (speedup: {speedup:.2f}x)")
        else:
            print(f"{name}: FALHOU")

def main():
    """Função principal otimizada"""
    parser = argparse.ArgumentParser(
        description='Calculador ULTRA-OTIMIZADO de Volumes de Produção de Petróleo'
    )
    parser.add_argument('arquivo_excel', help='Caminho para o arquivo Excel de entrada')
    parser.add_argument('-o', '--output', default='resultado_volumes_otimizado.xlsx', 
                       help='Nome do arquivo de saída')
    parser.add_argument('--aba', help='Nome da aba do Excel')
    parser.add_argument('--formato', choices=['excel', 'parquet', 'csv', 'all'], 
                       default='excel', help='Formato de saída')
    parser.add_argument('--workers', type=int, help='Número de workers para paralelização')
    parser.add_argument('--gpu', action='store_true', help='Forçar uso de GPU')
    parser.add_argument('--benchmark', action='store_true', help='Executar benchmark')
    parser.add_argument('--performance', action='store_true', 
                       help='Mostrar relatório de performance')
    
    args = parser.parse_args()
    
    if args.benchmark:
        benchmark_performance()
        return
    
    try:
        # Inicializa calculador otimizado
        calc = OptimizedVolumeCalculator(
            use_gpu=args.gpu,
            max_workers=args.workers
        )
        
        print("Iniciando processamento ultra-otimizado...")
        
        # Carrega e processa dados
        calc.carregar_excel_otimizado(args.arquivo_excel, args.aba)
        calc.calcular_volumes_ultra_otimizado()
        
        # Salva resultado
        calc.salvar_resultado_otimizado(args.output, args.formato)
        
        # Mostra relatório de performance
        if args.performance:
            relatorio = calc.gerar_relatorio_performance()
            print("\n" + "="*60)
            print("RELATÓRIO DE PERFORMANCE")
            print("="*60)
            for checkpoint, tempo in relatorio['checkpoints'].items():
                print(f"{checkpoint}: {tempo:.3f}s")
            print(f"Memória utilizada: {relatorio['memoria_mb']:.1f}MB")
            print(f"Configuração GPU: {relatorio['configuracao']['gpu_usado']}")
            print(f"Workers: {relatorio['configuracao']['max_workers']}")
        
        print("Processamento concluído com máxima performance!")
        
    except Exception as e:
        logger.error(f"Erro durante processamento: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("Sistema Ultra-Otimizado de Cálculo de Volumes")
        print("Para benchmark: python script.py --benchmark")
        print("Para usar: python script.py dados.xlsx --performance")
    else:
        main()

# Dependências otimizadas (requirements.txt):
"""
pandas>=2.0.0
numpy>=1.24.0
polars>=0.20.0
openpyxl>=3.1.0
numba>=0.58.0
psutil>=5.9.0
cupy-cuda12x>=12.0.0  # Para GPU NVIDIA (opcional)
"""